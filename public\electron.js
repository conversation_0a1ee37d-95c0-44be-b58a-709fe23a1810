try {
  console.log('بدء تحميل Electron...');
  const electron = require('electron');
  const { app, BrowserWindow, ipcMain } = electron;
  const path = require('path');
  const fs = require('fs');
  
  console.log('Electron تم تحميله بنجاح');
  console.log('app object:', !!app);
  
  // فحص وجود ملفات البناء بدلاً من متغير البيئة
  const distPath = path.join(__dirname, '../dist/index.html');
  const isDev = !fs.existsSync(distPath);

  console.log('مجلد العمل:', __dirname);
  console.log('مسار ملف البناء:', distPath);
  console.log('وضع التطوير:', isDev);

  function createWindow() {
    const mainWindow = new BrowserWindow({
      width: 1400,
      height: 900,
      minWidth: 1000,
      minHeight: 700,
      icon: path.join(__dirname, 'favicon.ico'),
      webPreferences: {
        nodeIntegration: true,
        contextIsolation: false,
        enableRemoteModule: true,
        webSecurity: false
      },
      show: false,
      titleBarStyle: 'default'
    });

    // تحميل التطبيق - دائماً من ملف البناء
    if (isDev) {
      console.log('تحذير: ملف البناء غير موجود. يرجى تشغيل npm run build');
      mainWindow.loadURL('data:text/html,<h1 style="text-align:center;color:red;font-family:Arial;">يرجى تشغيل npm run build أولاً</h1>');
    } else {
      console.log('تحميل التطبيق من:', distPath);
      mainWindow.loadFile(distPath);
    }

    mainWindow.once('ready-to-show', () => {
      mainWindow.show();
      mainWindow.focus();
    });

    // إزالة شريط القوائم
    mainWindow.setMenuBarVisibility(false);
  }

  app.whenReady().then(() => {
    createWindow();
    
    app.on('activate', () => {
      if (BrowserWindow.getAllWindows().length === 0) {
        createWindow();
      }
    });
  });

  app.on('window-all-closed', () => {
    if (process.platform !== 'darwin') {
      app.quit();
    }
  });

  // إعداد إضافي لمنع مشاكل الأمان
  app.on('web-contents-created', (event, contents) => {
    contents.on('new-window', (event, navigationUrl) => {
      event.preventDefault();
    });
  });

  // معالجات IPC
  ipcMain.handle('get-app-version', () => {
    return app.getVersion();
  });

  ipcMain.handle('get-user-data-path', () => {
    return app.getPath('userData');
  });

  // معالجات قاعدة البيانات (ستحتاج لتنفيذها حسب قاعدة البيانات المستخدمة)
  ipcMain.handle('get-categories', () => {
    // ستحتاج لتنفيذها حسب نظام قاعدة البيانات
    return [];
  });

} catch (error) {
  console.error('خطأ في تشغيل Electron:', error);
  process.exit(1);
}
