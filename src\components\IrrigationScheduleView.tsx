import React, { useState } from 'react';
import { Droplets, CheckCircle, Clock } from 'lucide-react';

const initialValves = [
  { id: 1, name: 'محبس 1', irrigated: false },
  { id: 2, name: 'محبس 2', irrigated: false },
  { id: 3, name: 'محبس 3', irrigated: false },
  { id: 4, name: 'محبس 4', irrigated: false },
];

const IrrigationScheduleView: React.FC = () => {
  const [valves, setValves] = useState(initialValves);

  const handleIrrigate = (id: number) => {
    setValves(valves.map(v => v.id === id ? { ...v, irrigated: true } : v));
  };

  return (
    <div className="min-h-screen w-full bg-gradient-to-br from-green-50 via-emerald-50 to-blue-50 py-8 px-2 md:px-8">
      <div className="max-w-2xl mx-auto">
        <div className="flex items-center gap-3 mb-8 bg-gradient-to-r from-emerald-500 to-emerald-700 rounded-2xl shadow-lg border border-emerald-200 p-6">
          <Droplets className="w-8 h-8 text-white drop-shadow" />
          <h1 className="text-2xl font-bold bg-gradient-to-br from-white to-emerald-100 bg-clip-text text-transparent drop-shadow">جدولة الري اليومية</h1>
        </div>
        <div className="bg-white rounded-2xl shadow-2xl border border-emerald-100 p-6">
          <table className="w-full text-center">
            <thead>
              <tr className="bg-emerald-50">
                <th className="py-3 text-lg font-bold text-emerald-700">المحبس</th>
                <th className="py-3 text-lg font-bold text-emerald-700">مدة الري</th>
                <th className="py-3 text-lg font-bold text-emerald-700">الحالة</th>
                <th className="py-3 text-lg font-bold text-emerald-700">إجراء</th>
              </tr>
            </thead>
            <tbody>
              {valves.map((valve) => (
                <tr key={valve.id} className="border-b hover:bg-emerald-50 transition-all duration-200">
                  <td className="py-4 text-lg font-bold text-emerald-800">{valve.name}</td>
                  <td className="py-4 text-lg text-emerald-700 font-bold flex items-center justify-center gap-2">
                    <Clock className="w-5 h-5 text-emerald-400" /> 40 دقيقة
                  </td>
                  <td className="py-4">
                    {valve.irrigated ? (
                      <span className="inline-flex items-center gap-1 px-3 py-1 rounded-full bg-green-100 text-green-700 font-bold shadow-sm">
                        <CheckCircle className="w-4 h-4" /> تم الري
                      </span>
                    ) : (
                      <span className="inline-flex items-center gap-1 px-3 py-1 rounded-full bg-yellow-100 text-yellow-700 font-bold shadow-sm">
                        <Clock className="w-4 h-4" /> لم يتم
                      </span>
                    )}
                  </td>
                  <td className="py-4">
                    <button
                      disabled={valve.irrigated}
                      onClick={() => handleIrrigate(valve.id)}
                      className={`px-5 py-2 rounded-xl font-bold text-white transition-all shadow-lg border-2 border-emerald-400 hover:border-emerald-600 transform hover:scale-105 ${valve.irrigated ? 'bg-gray-300 cursor-not-allowed' : 'bg-gradient-to-r from-emerald-500 to-emerald-700 hover:from-emerald-600 hover:to-emerald-800'}`}
                    >
                      {valve.irrigated ? 'تم الري' : 'تسجيل الري'}
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};

export default IrrigationScheduleView; 