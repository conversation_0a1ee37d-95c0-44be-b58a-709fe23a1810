import React, { useState, useEffect } from 'react';
import { Database, Plus, Package, TrendingUp, AlertTriangle, Box, Users } from 'lucide-react';
import { InventoryItem } from '../types';
import { getInventoryItems, addInventoryItem } from '../utils/database';
import Modal from './Modal';

// تعريف نوع الفئة ليشمل جميع الفئات بالإضافة إلى 'new'
type InventoryCategory = 'pesticides' | 'fertilizers' | 'seeds' | 'packaging' | 'equipment' | 'new';

const InventoryView: React.FC = () => {
  const [inventory, setInventory] = useState<InventoryItem[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [newItem, setNewItem] = useState<{
    name: string;
    category: InventoryCategory;
    quantity: number;
    unit: string;
    minQuantity: number;
    commercialName: string;
    activeIngredient: string;
    usageMethod: string;
    supplier: string;
    purchaseDate: string;
    expiryDate: string;
    notes: string;
  }>({
    name: '',
    category: 'pesticides',
    quantity: 0,
    unit: 'لتر',
    minQuantity: 0,
    commercialName: '',
    activeIngredient: '',
    usageMethod: '',
    supplier: '',
    purchaseDate: new Date().toISOString().split('T')[0],
    expiryDate: '',
    notes: ''
  });

  // حالة التعديل
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [editItem, setEditItem] = useState<InventoryItem | null>(null);

  // فلاتر البحث والتصفية
  const [categoryFilter, setCategoryFilter] = useState('all');
  const [searchTerm, setSearchTerm] = useState<string>('');

  // تصفية البيانات حسب الفلاتر
  const filteredInventory = inventory.filter(item => {
    const matchesCategory = categoryFilter === 'all' || item.category === categoryFilter;
    const matchesSearch =
      item.name.includes(searchTerm) ||
      (item.commercialName && item.commercialName.includes(searchTerm)) ||
      (item.activeIngredient && item.activeIngredient.includes(searchTerm));
    return matchesCategory && (!searchTerm || matchesSearch);
  });

  useEffect(() => {
    loadInventory();
  }, []);

  const loadInventory = async () => {
    try {
      const inventoryData = await getInventoryItems();
      setInventory(inventoryData);
    } catch (error) {
      console.error('خطأ في تحميل المخزون:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleAddItem = async () => {
    if (newItem.category === 'new') {
      alert('يرجى اختيار فئة صحيحة أو إدخال اسم الفئة الجديدة أولاً.');
      return;
    }
    try {
      const itemToAdd: Omit<InventoryItem, 'id'> = {
        ...newItem,
        category: newItem.category // هنا النوع مضمون أنه صحيح
      };
      const result = await addInventoryItem(itemToAdd);
      if (result.success) {
        await loadInventory();
        setIsAddModalOpen(false);
        setNewItem({
          name: '',
          category: 'pesticides',
          quantity: 0,
          unit: 'لتر',
          minQuantity: 0,
          commercialName: '',
          activeIngredient: '',
          usageMethod: '',
          supplier: '',
          purchaseDate: new Date().toISOString().split('T')[0],
          expiryDate: '',
          notes: ''
        });
        alert('تم إضافة العنصر بنجاح!');
      } else {
        alert('خطأ في إضافة العنصر: ' + result.error);
      }
    } catch (error) {
      console.error('خطأ في إضافة العنصر:', error);
      alert('حدث خطأ أثناء إضافة العنصر');
    }
  };

  // حفظ التعديل
  const handleSaveEdit = async () => {
    if (!editItem) return;
    try {
      // تحديث العنصر في قاعدة البيانات (يفترض وجود دالة updateInventoryItem)
      const result = await (window as any).updateInventoryItem(editItem); // استبدل بهذا الدالة الصحيحة إذا كانت موجودة
      if (result?.success) {
        await loadInventory();
        setIsEditModalOpen(false);
        setEditItem(null);
        alert('تم تحديث البيانات بنجاح!');
      } else {
        alert('خطأ في تحديث البيانات: ' + (result?.error || ''));
      }
    } catch (error) {
      alert('حدث خطأ أثناء تحديث البيانات');
    }
  };

  // حذف عنصر
  const handleDeleteClick = async (item: InventoryItem) => {
    if (!window.confirm('هل أنت متأكد من حذف هذا الصنف؟')) return;
    try {
      // حذف العنصر من قاعدة البيانات (يفترض وجود دالة deleteInventoryItem)
      const result = await (window as any).deleteInventoryItem(item.id); // استبدل بهذا الدالة الصحيحة إذا كانت موجودة
      if (result?.success) {
        await loadInventory();
        alert('تم حذف الصنف بنجاح!');
      } else {
        alert('خطأ في حذف الصنف: ' + (result?.error || ''));
      }
    } catch (error) {
      alert('حدث خطأ أثناء حذف الصنف');
    }
  };

  // دالة فتح نافذة التعديل
  const handleEditClick = (item: InventoryItem) => {
    setEditItem(item);
    setIsEditModalOpen(true);
  };

  const getCategoryText = (category: string) => {
    switch (category) {
      case 'pesticides': return 'مبيدات';
      case 'fertilizers': return 'أسمدة';
      case 'seeds': return 'بذور';
      case 'packaging': return 'صناديق تعبئة';
      case 'equipment': return 'معدات زراعية';
      default: return category;
    }
  };

  // حساب الإحصائيات
  const totalItems = inventory.length;
  const lowStockItems = inventory.filter(item => item.quantity <= item.minQuantity).length;
  const totalValue = inventory.reduce((sum, item) => sum + (item.quantity * 100), 0); // قيمة تقديرية
  const categories = [...new Set(inventory.map(item => item.category))].length;

  // إحصائيات لكل فئة
  const categoryStats = [
    {
      key: 'pesticides',
      label: 'مبيدات',
      color: 'border-emerald-500',
      icon: <Package className="h-8 w-8 text-emerald-600" />,
      count: inventory.filter(item => item.category === 'pesticides').length,
    },
    {
      key: 'fertilizers',
      label: 'أسمدة',
      color: 'border-blue-500',
      icon: <TrendingUp className="h-8 w-8 text-blue-600" />,
      count: inventory.filter(item => item.category === 'fertilizers').length,
    },
    {
      key: 'seeds',
      label: 'بذور',
      color: 'border-yellow-500',
      icon: <Box className="h-8 w-8 text-yellow-600" />,
      count: inventory.filter(item => item.category === 'seeds').length,
    },
    {
      key: 'packaging',
      label: 'صناديق',
      color: 'border-orange-500',
      icon: <Box className="h-8 w-8 text-orange-600" />,
      count: inventory.filter(item => item.category === 'packaging').length,
    },
    {
      key: 'equipment',
      label: 'معدات',
      color: 'border-purple-500',
      icon: <Users className="h-8 w-8 text-purple-600" />,
      count: inventory.filter(item => item.category === 'equipment').length,
    },
  ];

  // تحديث دالة setNewItem لتغيير الحقول حسب الفئة
  const handleNewItemChange = (field: string, value: any) => {
    setNewItem(prev => ({
      ...prev,
      [field]: value,
      // إعادة تعيين الحقول غير الضرورية عند تغيير الفئة
      ...(field === 'category' && {
        activeIngredient: ['pesticides', 'fertilizers', 'seeds'].includes(value) ? prev.activeIngredient : '',
        usageMethod: ['pesticides', 'fertilizers', 'seeds'].includes(value) ? prev.usageMethod : '',
      })
    }));
  };

  // دالة تصدير PDF
  const handleExportPDF = async () => {
    const [{ default: jsPDF }, autoTable] = await Promise.all([
      import('jspdf'),
      import('jspdf-autotable')
    ]);
    const doc = new jsPDF({ orientation: 'landscape', unit: 'pt', format: 'a4' });

    // عنوان التقرير
    doc.setFont('helvetica', 'bold');
    doc.setFontSize(20);
    doc.text('تقرير المخزون', doc.internal.pageSize.getWidth() - 40, 40, { align: 'right' });
    doc.setFontSize(12);
    doc.text(`تاريخ التصدير: ${new Date().toLocaleDateString('ar-EG')}`, doc.internal.pageSize.getWidth() - 40, 65, { align: 'right' });

    // بيانات الجدول
    const tableData = filteredInventory.map(item => [
      item.name ?? '',
      getCategoryText(item.category) ?? '',
      item.quantity ?? 0,
      item.unit ?? '',
      item.commercialName ?? '',
      item.activeIngredient ?? '',
      item.usageMethod ?? '',
      item.supplier ?? '',
      item.purchaseDate ?? '',
      item.expiryDate ?? '',
      item.notes ?? ''
    ]);

    const table = autoTable.default(doc, {
      head: [[
        'اسم الصنف',
        'الفئة',
        'الكمية',
        'الوحدة',
        'الاسم التجاري',
        'المادة الفعالة',
        'طريقة الاستخدام',
        'المورد',
        'تاريخ الشراء',
        'تاريخ الانتهاء',
        'ملاحظات'
      ]],
      body: tableData,
      styles: {
        font: 'helvetica',
        fontStyle: 'normal',
        fontSize: 12,
        halign: 'right',
        cellPadding: 4,
      },
      headStyles: {
        fillColor: [59, 130, 246],
        textColor: 255,
        fontStyle: 'bold',
        halign: 'right',
      },
      startY: 90,
      margin: { left: 40, right: 40 },
    }) as any;

    // ملخص أسفل الجدول
    doc.setFontSize(14);
    doc.setFont('helvetica', 'bold');
    const summaryY = table?.finalY ?? 90;
    doc.text(`إجمالي الأصناف: ${filteredInventory.length}`, doc.internal.pageSize.getWidth() - 40, summaryY + 30, { align: 'right' });
    doc.text(`عدد الفئات: ${categories}`, doc.internal.pageSize.getWidth() - 40, summaryY + 55, { align: 'right' });
    doc.text(`الأصناف منخفضة المخزون: ${lowStockItems}`, doc.internal.pageSize.getWidth() - 40, summaryY + 80, { align: 'right' });

    doc.save('تقرير-المخزون.pdf');
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-emerald-600"></div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 via-emerald-50 to-teal-100 p-6 space-y-6">
      {/* عنوان الصفحة */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-green-700 drop-shadow">إدارة المخزون</h1>
          <p className="text-emerald-600 mt-2 font-semibold">تسجيل ومتابعة أصناف المخزون</p>
        </div>
        <button
          onClick={() => setIsAddModalOpen(true)}
          className="bg-gradient-to-r from-green-500 via-emerald-500 to-teal-500 text-white px-6 py-3 rounded-xl hover:from-green-600 hover:via-emerald-600 hover:to-teal-600 transition-all duration-300 flex items-center gap-2 shadow-xl hover:shadow-2xl transform hover:scale-105 font-arabic font-bold tracking-wide"
        >
          <Plus size={20} />
          إضافة صنف جديد
        </button>
      </div>

      {/* بطاقات الإحصائيات */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <div onClick={() => setCategoryFilter('all')} className={`cursor-pointer relative overflow-hidden rounded-2xl shadow-2xl p-6 bg-gradient-to-br from-green-500 via-emerald-500 to-teal-500 border-0 group transition-transform duration-300 hover:scale-105 ${categoryFilter === 'all' ? 'ring-4 ring-green-300' : ''}`}>
          {/* زخرفة دائرية شفافة */}
          <div className="absolute -top-10 -left-10 w-32 h-32 bg-white opacity-40 rounded-full blur-2xl z-0"></div>
          <div className="flex items-center gap-4 relative z-10">
            <div className="p-3 rounded-lg bg-white/90 shadow-lg border-2 border-white">
              <Database className="h-10 w-10 drop-shadow text-green-700" />
            </div>
            <div>
              <p className="text-lg font-bold text-white drop-shadow-sm tracking-wide mb-1">
                الكل
              </p>
              <p className="text-4xl font-extrabold text-white drop-shadow-lg tracking-tight">
                {totalItems}
              </p>
            </div>
          </div>
        </div>
        {categoryStats.map((stat, index) => {
          const gradients = [
            'from-green-600 via-green-500 to-emerald-500',
            'from-emerald-600 via-emerald-500 to-teal-500',
            'from-teal-600 via-teal-500 to-cyan-500',
            'from-green-500 via-emerald-500 to-teal-500',
            'from-emerald-500 via-teal-500 to-green-500'
          ];
          const ringColors = [
            'ring-green-300',
            'ring-emerald-300',
            'ring-teal-300',
            'ring-green-300',
            'ring-emerald-300'
          ];
          return (
            <div key={stat.key} onClick={() => setCategoryFilter(stat.key)} className={`cursor-pointer relative overflow-hidden rounded-2xl shadow-2xl p-6 bg-gradient-to-br ${gradients[index]} border-0 group transition-transform duration-300 hover:scale-105 ${(categoryFilter === stat.key) ? `ring-4 ${ringColors[index]}` : ''}`}>
              {/* زخرفة دائرية شفافة */}
              <div className="absolute -top-10 -left-10 w-32 h-32 bg-white opacity-30 rounded-full blur-2xl z-0"></div>
              <div className="flex items-center gap-4 relative z-10">
                <div className="p-3 rounded-lg bg-white/80 shadow-lg border-2 border-white">
                  {React.cloneElement(stat.icon, { className: 'h-10 w-10 drop-shadow text-white' })}
                </div>
                <div>
                  <p className="text-lg font-bold text-white drop-shadow-sm tracking-wide mb-1">
                    {stat.label}
                  </p>
                  <p className="text-4xl font-extrabold text-white drop-shadow-lg tracking-tight">
                    {stat.count}
                  </p>
                </div>
              </div>
            </div>
          );
        })}
      </div>

      {/* جدول المخزون */}
      <div className="bg-gradient-to-br from-white via-green-50 to-emerald-50 rounded-3xl shadow-2xl border-2 border-green-200 p-6">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center gap-3">
            <div className="p-2 rounded-lg bg-gradient-to-r from-green-500 to-emerald-500">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
              </svg>
            </div>
            <div>
              <h3 className="text-2xl font-extrabold text-green-800 drop-shadow tracking-wide">سجل الأصناف</h3>
              <p className="text-base text-emerald-600 font-medium">عرض وإدارة جميع المواد المخزنة</p>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <span className="px-4 py-2 bg-gradient-to-r from-green-100 to-emerald-100 text-green-700 rounded-full text-base font-bold">
              {filteredInventory.length} صنف
            </span>
          </div>
        </div>

        <div className="table-container">
          <table className="table">
            <thead className="table-header">
              <tr className="bg-gradient-to-r from-green-500 via-emerald-500 to-teal-500">
                <th className="table-header-cell text-white font-extrabold text-lg">الاسم</th>
                <th className="table-header-cell text-white font-extrabold text-lg">الفئة</th>
                <th className="table-header-cell text-white font-extrabold text-lg">الكمية</th>
                <th className="table-header-cell text-white font-extrabold text-lg">الوحدة</th>
                <th className="table-header-cell text-white font-extrabold text-lg">الحد الأدنى</th>
                <th className="table-header-cell text-white font-extrabold text-lg">الملاحظات</th>
                <th className="table-header-cell text-white font-extrabold text-lg">الإجراءات</th>
              </tr>
            </thead>
            <tbody className="table-body">
              {filteredInventory.map((item, index) => (
                <tr key={item.id} className={`table-row hover:bg-gradient-to-r hover:from-green-50 hover:to-emerald-50 transition-all duration-300 ${index % 2 === 0 ? 'bg-white' : 'bg-green-25'}`}>
                    <td className="table-cell font-bold text-green-700 text-lg">{item.name}</td>
                    <td className="table-cell">
                      <span className="px-3 py-2 rounded-full text-sm font-bold bg-gradient-to-r from-emerald-100 to-teal-100 text-emerald-700">
                        {getCategoryText(item.category)}
                      </span>
                    </td>
                    <td className="table-cell">
                      <span className="font-extrabold text-orange-700 bg-orange-100 px-3 py-2 rounded-lg text-lg">
                        {item.quantity}
                      </span>
                    </td>
                    <td className="table-cell text-gray-600 font-bold text-base">{item.unit}</td>
                    <td className="table-cell">
                      <span className={`px-3 py-2 rounded-full text-sm font-bold ${item.quantity <= item.minQuantity ? 'bg-red-100 text-red-700' : 'bg-green-100 text-green-700'}`}>
                        {item.minQuantity}
                      </span>
                    </td>
                    <td className="table-cell text-gray-500 font-medium text-base">{item.notes || '-'}</td>
                    <td className="table-cell">
                      <div className="flex gap-1">
                        <button className="text-green-600 hover:text-green-800 p-2 transition-colors hover:bg-green-100 rounded-lg" title="تعديل" onClick={() => handleEditClick(item)}>
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.232 5.232l3.536 3.536M9 13l6-6m2 2l-6 6m-2 2h6" /></svg>
                        </button>
                        <button className="text-red-600 hover:text-red-800 p-2 transition-colors hover:bg-red-100 rounded-lg" title="حذف" onClick={() => handleDeleteClick(item)}>
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" /></svg>
                        </button>
                      </div>
                    </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

        {/* أزرار التصدير */}
        <div className="flex flex-wrap gap-4 mb-4">
          <button
            onClick={handleExportPDF}
            className="bg-gradient-to-r from-green-600 via-emerald-600 to-teal-600 text-white px-6 py-3 rounded-lg font-semibold flex items-center gap-2 hover:from-green-700 hover:via-emerald-700 hover:to-teal-700 shadow-lg transform hover:scale-105 transition-all duration-300"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" /></svg>
            تصدير PDF
          </button>
        </div>

        {/* نافذة إضافة مخزون */}
        <Modal isOpen={isAddModalOpen} onClose={() => setIsAddModalOpen(false)} title="إضافة مادة جديدة للمخزون">
          <form
            onSubmit={e => {
              e.preventDefault();
              handleAddItem();
            }}
            className="space-y-4"
          >
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block mb-1 text-sm font-medium">الفئة</label>
                <select
                  className="input-field rounded-xl border-2 border-green-100 shadow focus:border-green-400 focus:ring-2 focus:ring-green-200 transition-all duration-200"
                  value={newItem.category}
                  onChange={e => handleNewItemChange('category', e.target.value as InventoryCategory)}
                  required
                >
                  <option value="pesticides">مبيدات</option>
                  <option value="fertilizers">أسمدة</option>
                  <option value="seeds">بذور</option>
                  <option value="packaging">صناديق</option>
                  <option value="equipment">معدات</option>
                  <option value="new">+ إضافة صنف جديد...</option>
                </select>
              </div>
              <div>
                <label className="block mb-1 text-sm font-medium">اسم المادة</label>
                <input
                  type="text"
                  className="input-field rounded-xl border-2 border-blue-100 shadow focus:border-blue-400 focus:ring-2 focus:ring-blue-200 transition-all duration-200"
                  value={newItem.name}
                  onChange={e => handleNewItemChange('name', e.target.value)}
                  required
                />
              </div>
              <div>
                <label className="block mb-1 text-sm font-medium">الكمية</label>
                <input
                  type="number"
                  min="0"
                  className="input-field rounded-xl border-2 border-green-100 shadow focus:border-green-400 focus:ring-2 focus:ring-green-200 transition-all duration-200"
                  value={newItem.quantity}
                  onChange={e => handleNewItemChange('quantity', Number(e.target.value))}
                  required
                />
              </div>
              <div>
                <label className="block mb-1 text-sm font-medium">الوحدة</label>
                <select
                  className="input-field rounded-xl border-2 border-indigo-100 shadow focus:border-indigo-400 focus:ring-2 focus:ring-indigo-200 transition-all duration-200"
                  value={newItem.unit}
                  onChange={e => handleNewItemChange('unit', e.target.value)}
                  required
                >
                  <option value="لتر">لتر</option>
                  <option value="كيلو">كيلو</option>
                  <option value="غم">غم</option>
                  <option value="ملي غرام">ملي غرام</option>
                  <option value="حبة">حبة</option>
                  <option value="صندوق">صندوق</option>
                  <option value="كرنونة">كرنونة</option>
                  <option value="ربطة">ربطة</option>
                  <option value="شوال">شوال</option>
                  <option value="كيس">كيس</option>
                </select>
              </div>
              <div>
                <label className="block mb-1 text-sm font-medium">الحد الأدنى (اختياري)</label>
                <input
                  type="number"
                  min="0"
                  className="input-field rounded-xl border-2 border-blue-100 shadow focus:border-blue-400 focus:ring-2 focus:ring-blue-200 transition-all duration-200"
                  value={newItem.minQuantity}
                  onChange={e => handleNewItemChange('minQuantity', Number(e.target.value))}
                />
              </div>
              <div>
                <label className="block mb-1 text-sm font-medium">المورد (اختياري)</label>
                <input
                  type="text"
                  className="input-field rounded-xl border-2 border-green-100 shadow focus:border-green-400 focus:ring-2 focus:ring-green-200 transition-all duration-200"
                  value={newItem.supplier}
                  onChange={e => handleNewItemChange('supplier', e.target.value)}
                />
              </div>
              <div>
                <label className="block mb-1 text-sm font-medium">تاريخ الشراء (اختياري)</label>
                <input
                  type="date"
                  className="input-field rounded-xl border-2 border-blue-100 shadow focus:border-blue-400 focus:ring-2 focus:ring-blue-200 transition-all duration-200"
                  value={newItem.purchaseDate}
                  onChange={e => handleNewItemChange('purchaseDate', e.target.value)}
                />
              </div>

              <div className="md:col-span-2">
                <label className="block mb-1 text-sm font-medium">ملاحظات</label>
                <textarea
                  className="input-field rounded-xl border-2 border-blue-100 shadow focus:border-blue-400 focus:ring-2 focus:ring-blue-200 transition-all duration-200"
                  value={newItem.notes}
                  onChange={e => handleNewItemChange('notes', e.target.value)}
                  rows={2}
                />
              </div>
            </div>
            {/* إضافة صنف جديد */}
            {newItem.category === 'new' && (
              <div className="bg-yellow-50 border border-yellow-300 rounded-lg p-4 mt-2">
                <label className="block mb-1 text-sm font-medium">اسم الصنف الجديد</label>
                <input
                  type="text"
                  className="border rounded-lg px-4 py-2 w-full text-sm focus:ring-yellow-500 focus:border-yellow-500"
                  value={newItem.name}
                  onChange={e => handleNewItemChange('name', e.target.value)}
                  required
                />
              </div>
            )}
            <div className="flex justify-end gap-2 mt-4">
              <button
                type="button"
                className="bg-gradient-to-r from-gray-300 to-gray-400 text-gray-800 px-6 py-2 rounded-xl shadow hover:from-gray-400 hover:to-gray-500 transition-all duration-300 font-arabic font-medium tracking-wide"
                onClick={() => setIsAddModalOpen(false)}
              >
                إلغاء
              </button>
              <button
                type="submit"
                className="bg-gradient-to-r from-green-500 to-blue-500 text-white px-6 py-2 rounded-xl shadow hover:from-blue-600 hover:to-green-600 transition-all duration-300 font-arabic font-medium tracking-wide"
              >
                إضافة
              </button>
            </div>
          </form>
        </Modal>
      {/* نافذة تعديل مخزون */}
      <Modal isOpen={isEditModalOpen} onClose={() => setIsEditModalOpen(false)} title="تعديل بيانات المادة">
        {editItem && (
          <form
            onSubmit={e => {
              e.preventDefault();
              handleSaveEdit();
            }}
            className="space-y-4"
          >
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block mb-1 text-sm font-medium">الفئة</label>
                <select
                  className="input-field rounded-xl border-2 border-green-100 shadow focus:border-green-400 focus:ring-2 focus:ring-green-200 transition-all duration-200"
                  value={editItem.category}
                  onChange={e => setEditItem({ ...editItem, category: e.target.value as any })}
                  required
                >
                  <option value="pesticides">مبيدات</option>
                  <option value="fertilizers">أسمدة</option>
                  <option value="seeds">بذور</option>
                  <option value="packaging">صناديق</option>
                  <option value="equipment">معدات</option>
                </select>
              </div>
              <div>
                <label className="block mb-1 text-sm font-medium">اسم المادة</label>
                <input
                  type="text"
                  className="input-field rounded-xl border-2 border-blue-100 shadow focus:border-blue-400 focus:ring-2 focus:ring-blue-200 transition-all duration-200"
                  value={editItem.name}
                  onChange={e => setEditItem({ ...editItem, name: e.target.value })}
                  required
                />
              </div>
              <div>
                <label className="block mb-1 text-sm font-medium">الكمية</label>
                <input
                  type="number"
                  min="0"
                  className="input-field rounded-xl border-2 border-green-100 shadow focus:border-green-400 focus:ring-2 focus:ring-green-200 transition-all duration-200"
                  value={editItem.quantity}
                  onChange={e => setEditItem({ ...editItem, quantity: Number(e.target.value) })}
                  required
                />
              </div>
              <div>
                <label className="block mb-1 text-sm font-medium">الوحدة</label>
                <select
                  className="input-field rounded-xl border-2 border-indigo-100 shadow focus:border-indigo-400 focus:ring-2 focus:ring-indigo-200 transition-all duration-200"
                  value={editItem.unit}
                  onChange={e => setEditItem({ ...editItem, unit: e.target.value })}
                  required
                >
                  <option value="لتر">لتر</option>
                  <option value="كيلو">كيلو</option>
                  <option value="غم">غم</option>
                  <option value="ملي غرام">ملي غرام</option>
                  <option value="حبة">حبة</option>
                  <option value="صندوق">صندوق</option>
                  <option value="كرنونة">كرنونة</option>
                  <option value="ربطة">ربطة</option>
                  <option value="شوال">شوال</option>
                  <option value="كيس">كيس</option>
                </select>
              </div>
              <div>
                <label className="block mb-1 text-sm font-medium">الحد الأدنى (اختياري)</label>
                <input
                  type="number"
                  min="0"
                  className="input-field rounded-xl border-2 border-blue-100 shadow focus:border-blue-400 focus:ring-2 focus:ring-blue-200 transition-all duration-200"
                  value={editItem.minQuantity}
                  onChange={e => setEditItem({ ...editItem, minQuantity: Number(e.target.value) })}
                />
              </div>
              <div>
                <label className="block mb-1 text-sm font-medium">المورد (اختياري)</label>
                <input
                  type="text"
                  className="input-field rounded-xl border-2 border-green-100 shadow focus:border-green-400 focus:ring-2 focus:ring-green-200 transition-all duration-200"
                  value={editItem.supplier}
                  onChange={e => setEditItem({ ...editItem, supplier: e.target.value })}
                />
              </div>
              <div>
                <label className="block mb-1 text-sm font-medium">تاريخ الشراء (اختياري)</label>
                <input
                  type="date"
                  className="input-field rounded-xl border-2 border-blue-100 shadow focus:border-blue-400 focus:ring-2 focus:ring-blue-200 transition-all duration-200"
                  value={editItem.purchaseDate}
                  onChange={e => setEditItem({ ...editItem, purchaseDate: e.target.value })}
                />
              </div>

              <div className="md:col-span-2">
                <label className="block mb-1 text-sm font-medium">ملاحظات</label>
                <textarea
                  className="input-field rounded-xl border-2 border-blue-100 shadow focus:border-blue-400 focus:ring-2 focus:ring-blue-200 transition-all duration-200"
                  value={editItem.notes}
                  onChange={e => setEditItem({ ...editItem, notes: e.target.value })}
                  rows={2}
                />
              </div>
            </div>
            <div className="flex justify-end gap-2 mt-4">
              <button
                type="button"
                className="bg-gradient-to-r from-gray-300 to-gray-400 text-gray-800 px-6 py-2 rounded-xl shadow hover:from-gray-400 hover:to-gray-500 transition-all duration-300 font-arabic font-medium tracking-wide"
                onClick={() => setIsEditModalOpen(false)}
              >
                إلغاء
              </button>
              <button
                type="submit"
                className="bg-gradient-to-r from-green-500 to-blue-500 text-white px-6 py-2 rounded-xl shadow hover:from-blue-600 hover:to-green-600 transition-all duration-300 font-arabic font-medium tracking-wide"
              >
                حفظ التعديلات
              </button>
            </div>
          </form>
        )}
      </Modal>
    </div>
  );
};

export default InventoryView; 