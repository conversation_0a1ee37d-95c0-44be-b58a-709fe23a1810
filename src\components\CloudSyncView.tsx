import React, { useState, useEffect } from 'react';
import { 
  Cloud, 
  Download, 
  Upload, 
  Wifi, 
  WifiOff, 
  RefreshCw, 
  Check, 
  X, 
  AlertTriangle,
  Clock,
  Settings
} from 'lucide-react';
import { cloudSync, getCloudSyncStatus, setupCloudSync } from '../utils/cloudSync';
import Modal from './Modal';

interface CloudSyncViewProps {
  onSyncComplete?: () => void;
}

interface CloudConfig {
  provider: 'firebase' | 'supabase' | 'custom';
  apiKey: string;
  projectId?: string;
  endpoint?: string;
}

const CloudSyncView: React.FC<CloudSyncViewProps> = ({ onSyncComplete }) => {
  const [syncStatus, setSyncStatus] = useState(getCloudSyncStatus());
  const [isUploading, setIsUploading] = useState(false);
  const [isDownloading, setIsDownloading] = useState(false);
  const [lastSyncResult, setLastSyncResult] = useState<{ success: boolean; message: string } | null>(null);
  const [cloudBackups, setCloudBackups] = useState<any[]>([]);
  const [isConfigModalOpen, setIsConfigModalOpen] = useState(false);
  const [config, setConfig] = useState<CloudConfig>({
    provider: 'firebase',
    apiKey: '',
    projectId: 'lettuce-farm-system'
  });

  useEffect(() => {
    loadCloudBackups();
    
    // تحديث حالة الاتصال كل 5 ثوانِ
    const interval = setInterval(() => {
      setSyncStatus(getCloudSyncStatus());
    }, 5000);

    return () => clearInterval(interval);
  }, []);

  const loadCloudBackups = async () => {
    try {
      const backups = await cloudSync.getCloudBackups();
      setCloudBackups(backups);
    } catch (error) {
      console.error('خطأ في تحميل قائمة النسخ الاحتياطية:', error);
    }
  };

  const handleUploadBackup = async () => {
    setIsUploading(true);
    setLastSyncResult(null);
    
    try {
      const result = await cloudSync.uploadBackup();
      
      if (result.success) {
        setLastSyncResult({ success: true, message: 'تم رفع النسخة الاحتياطية بنجاح' });
        await loadCloudBackups();
        onSyncComplete?.();
      } else {
        setLastSyncResult({ success: false, message: result.error || 'فشل في رفع النسخة الاحتياطية' });
      }
    } catch (error) {
      setLastSyncResult({ success: false, message: 'حدث خطأ أثناء الرفع' });
    } finally {
      setIsUploading(false);
    }
  };

  const handleDownloadBackup = async (backupId: string) => {
    setIsDownloading(true);
    setLastSyncResult(null);
    
    try {
      const result = await cloudSync.downloadBackup(backupId);
      
      if (result.success) {
        setLastSyncResult({ success: true, message: 'تم تحميل واستيراد النسخة الاحتياطية بنجاح' });
        onSyncComplete?.();
      } else {
        setLastSyncResult({ success: false, message: result.error || 'فشل في تحميل النسخة الاحتياطية' });
      }
    } catch (error) {
      setLastSyncResult({ success: false, message: 'حدث خطأ أثناء التحميل' });
    } finally {
      setIsDownloading(false);
    }
  };

  const handleAutoSync = async () => {
    const result = await cloudSync.autoSync();
    
    if (result.success) {
      setLastSyncResult({ success: true, message: 'تمت المزامنة التلقائية بنجاح' });
      await loadCloudBackups();
    } else {
      setLastSyncResult({ success: false, message: result.error || 'فشل في المزامنة التلقائية' });
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('ar-SA', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 بايت';
    const k = 1024;
    const sizes = ['بايت', 'كيلوبايت', 'ميجابايت', 'جيجابايت'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const setupCustomConfig = (newConfig: CloudConfig) => {
    setConfig(newConfig);
    setupCloudSync(newConfig);
    setIsConfigModalOpen(false);
  };

  return (
    <div className="min-h-screen w-full bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 py-8 px-2 md:px-8">
      {/* Header */}
      <div className="bg-gradient-to-r from-blue-600 via-indigo-600 to-purple-600 text-white p-8 shadow-2xl rounded-2xl mb-6 relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-r from-blue-500/20 to-purple-500/20"></div>
        <div className="container mx-auto relative z-10">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-6">
              <div className="bg-white/25 p-4 rounded-2xl shadow-xl border border-white/40 backdrop-blur-sm">
                <Cloud size={32} className="text-white drop-shadow-lg" />
              </div>
              <div>
                <h1 className="text-4xl font-bold font-arabic tracking-wide drop-shadow-lg mb-2">التزامن السحابي</h1>
                <p className="text-blue-100 text-lg font-medium tracking-wider">Cloud Backup & Synchronization</p>
              </div>
            </div>
            <button
              onClick={() => setIsConfigModalOpen(true)}
              className="text-lg bg-white/25 px-6 py-3 rounded-2xl backdrop-blur-sm border border-white/30 shadow-lg hover:bg-white/35 transition-all duration-200"
            >
              <div className="flex items-center gap-3">
                <Settings className="h-5 w-5" />
                <span className="font-arabic font-medium">الإعدادات</span>
              </div>
            </button>
          </div>
        </div>
      </div>

      {/* حالة الاتصال */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <div className={`rounded-3xl p-6 shadow-xl border-2 ${
          syncStatus.isOnline 
            ? 'bg-gradient-to-br from-green-50 to-emerald-100 border-green-200' 
            : 'bg-gradient-to-br from-red-50 to-pink-100 border-red-200'
        }`}>
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-gray-700 text-sm font-bold mb-2">حالة الاتصال</h3>
              <p className={`text-2xl font-bold ${syncStatus.isOnline ? 'text-green-600' : 'text-red-600'}`}>
                {syncStatus.isOnline ? 'متصل' : 'غير متصل'}
              </p>
            </div>
            <div className={`w-12 h-12 rounded-2xl flex items-center justify-center shadow-lg ${
              syncStatus.isOnline 
                ? 'bg-gradient-to-br from-green-500 to-emerald-600' 
                : 'bg-gradient-to-br from-red-500 to-pink-600'
            }`}>
              {syncStatus.isOnline ? <Wifi size={24} className="text-white" /> : <WifiOff size={24} className="text-white" />}
            </div>
          </div>
        </div>

        <div className="bg-gradient-to-br from-blue-50 to-cyan-100 rounded-3xl p-6 shadow-xl border-2 border-blue-200">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-blue-700 text-sm font-bold mb-2">آخر مزامنة</h3>
              <p className="text-lg font-bold text-blue-600">
                {syncStatus.lastSync 
                  ? formatDate(new Date(parseInt(syncStatus.lastSync)).toISOString())
                  : 'لم تتم بعد'
                }
              </p>
            </div>
            <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-cyan-600 rounded-2xl flex items-center justify-center shadow-lg">
              <Clock size={24} className="text-white" />
            </div>
          </div>
        </div>

        <div className="bg-gradient-to-br from-purple-50 to-pink-100 rounded-3xl p-6 shadow-xl border-2 border-purple-200">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-purple-700 text-sm font-bold mb-2">طابور المزامنة</h3>
              <p className="text-2xl font-bold text-purple-600">{syncStatus.queueLength}</p>
              <p className="text-purple-500 text-xs mt-1">عملية معلقة</p>
            </div>
            <div className="w-12 h-12 bg-gradient-to-br from-purple-500 to-pink-600 rounded-2xl flex items-center justify-center shadow-lg">
              <RefreshCw size={24} className="text-white" />
            </div>
          </div>
        </div>
      </div>

      {/* نتيجة آخر عملية */}
      {lastSyncResult && (
        <div className={`mb-8 p-6 rounded-2xl shadow-xl border-2 ${
          lastSyncResult.success 
            ? 'bg-gradient-to-r from-green-50 to-emerald-50 border-green-200' 
            : 'bg-gradient-to-r from-red-50 to-pink-50 border-red-200'
        }`}>
          <div className="flex items-center gap-4">
            {lastSyncResult.success ? (
              <Check className="h-8 w-8 text-green-600" />
            ) : (
              <X className="h-8 w-8 text-red-600" />
            )}
            <div>
              <h3 className={`text-lg font-bold ${lastSyncResult.success ? 'text-green-800' : 'text-red-800'}`}>
                {lastSyncResult.success ? 'تمت العملية بنجاح' : 'فشلت العملية'}
              </h3>
              <p className={`${lastSyncResult.success ? 'text-green-600' : 'text-red-600'}`}>
                {lastSyncResult.message}
              </p>
            </div>
          </div>
        </div>
      )}

      {/* أزرار التحكم */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <button
          onClick={handleUploadBackup}
          disabled={isUploading || !syncStatus.isOnline}
          className="flex items-center justify-center gap-4 bg-gradient-to-r from-blue-500 to-indigo-600 text-white p-6 rounded-2xl shadow-xl hover:from-blue-600 hover:to-indigo-700 transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed transform hover:scale-105"
        >
          {isUploading ? (
            <RefreshCw className="w-6 h-6 animate-spin" />
          ) : (
            <CloudUpload className="w-6 h-6" />
          )}
          <span className="text-xl font-bold font-arabic">
            {isUploading ? 'جاري الرفع...' : 'رفع نسخة احتياطية'}
          </span>
        </button>

        <button
          onClick={handleAutoSync}
          disabled={!syncStatus.isOnline}
          className="flex items-center justify-center gap-4 bg-gradient-to-r from-green-500 to-emerald-600 text-white p-6 rounded-2xl shadow-xl hover:from-green-600 hover:to-emerald-700 transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed transform hover:scale-105"
        >
          <RefreshCw className="w-6 h-6" />
          <span className="text-xl font-bold font-arabic">مزامنة تلقائية</span>
        </button>

        <button
          onClick={loadCloudBackups}
          className="flex items-center justify-center gap-4 bg-gradient-to-r from-purple-500 to-pink-600 text-white p-6 rounded-2xl shadow-xl hover:from-purple-600 hover:to-pink-700 transition-all duration-300 transform hover:scale-105"
        >
          <RefreshCw className="w-6 h-6" />
          <span className="text-xl font-bold font-arabic">تحديث القائمة</span>
        </button>
      </div>

      {/* قائمة النسخ الاحتياطية السحابية */}
      <div className="bg-white rounded-2xl shadow-xl overflow-hidden border-2 border-blue-100">
        <div className="p-6 bg-gradient-to-r from-blue-50 to-indigo-50 border-b border-blue-200">
          <h3 className="text-2xl font-bold text-blue-700 font-arabic">النسخ الاحتياطية السحابية</h3>
        </div>
        
        {cloudBackups.length === 0 ? (
          <div className="p-12 text-center">
            <Cloud className="w-16 h-16 text-gray-400 mx-auto mb-4" />
            <h3 className="text-xl font-bold text-gray-600 mb-2 font-arabic">لا توجد نسخ احتياطية</h3>
            <p className="text-gray-500 font-arabic">قم برفع أول نسخة احتياطية للسحابة</p>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-blue-100">
              <thead className="bg-gradient-to-r from-blue-100 to-indigo-100">
                <tr>
                  <th className="px-6 py-4 text-right text-sm font-bold text-blue-700 font-arabic">ID</th>
                  <th className="px-6 py-4 text-right text-sm font-bold text-blue-700 font-arabic">التاريخ</th>
                  <th className="px-6 py-4 text-right text-sm font-bold text-blue-700 font-arabic">الحجم</th>
                  <th className="px-6 py-4 text-right text-sm font-bold text-blue-700 font-arabic">الإصدار</th>
                  <th className="px-6 py-4 text-right text-sm font-bold text-blue-700 font-arabic">الإجراءات</th>
                </tr>
              </thead>
              <tbody className="divide-y divide-blue-50">
                {cloudBackups.map((backup) => (
                  <tr key={backup.id} className="hover:bg-blue-50 transition-all duration-200">
                    <td className="px-6 py-4 text-sm font-mono text-blue-600">{backup.id.slice(-8)}</td>
                    <td className="px-6 py-4 text-sm text-blue-700 font-arabic">
                      {formatDate(backup.timestamp)}
                    </td>
                    <td className="px-6 py-4 text-sm text-blue-700 font-arabic">
                      {formatFileSize(backup.size)}
                    </td>
                    <td className="px-6 py-4 text-sm text-blue-700 font-arabic">{backup.version}</td>
                    <td className="px-6 py-4">
                      <button
                        onClick={() => handleDownloadBackup(backup.id)}
                        disabled={isDownloading || !syncStatus.isOnline}
                        className="text-blue-600 hover:text-blue-800 p-2 hover:bg-blue-100 rounded-xl transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        {isDownloading ? (
                          <RefreshCw size={18} className="animate-spin" />
                        ) : (
                          <Download size={18} />
                        )}
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>

      {/* نافذة إعدادات التزامن */}
      <Modal
        isOpen={isConfigModalOpen}
        onClose={() => setIsConfigModalOpen(false)}
        title="إعدادات التزامن السحابي"
      >
        <div className="space-y-6">
          <div>
            <label className="block text-sm font-bold text-gray-700 mb-2 font-arabic">موفر السحابة</label>
            <select
              value={config.provider}
              onChange={(e) => setConfig({ ...config, provider: e.target.value as any })}
              className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 font-arabic"
            >
              <option value="firebase">Firebase</option>
              <option value="supabase">Supabase</option>
              <option value="custom">خادم مخصص</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-bold text-gray-700 mb-2 font-arabic">مفتاح API</label>
            <input
              type="password"
              value={config.apiKey}
              onChange={(e) => setConfig({ ...config, apiKey: e.target.value })}
              className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 font-mono"
              placeholder="أدخل مفتاح API"
            />
          </div>

          {config.provider === 'firebase' && (
            <div>
              <label className="block text-sm font-bold text-gray-700 mb-2 font-arabic">معرف المشروع</label>
              <input
                type="text"
                value={config.projectId || ''}
                onChange={(e) => setConfig({ ...config, projectId: e.target.value })}
                className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 font-mono"
                placeholder="project-id"
              />
            </div>
          )}

          {config.provider === 'custom' && (
            <div>
              <label className="block text-sm font-bold text-gray-700 mb-2 font-arabic">عنوان الخادم</label>
              <input
                type="url"
                value={config.endpoint || ''}
                onChange={(e) => setConfig({ ...config, endpoint: e.target.value })}
                className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 font-mono"
                placeholder="https://api.example.com"
              />
            </div>
          )}

          <div className="flex gap-4 pt-4">
            <button
              onClick={() => setupCustomConfig(config)}
              className="flex-1 bg-gradient-to-r from-blue-500 to-indigo-600 text-white py-3 px-6 rounded-xl hover:from-blue-600 hover:to-indigo-700 transition-all duration-300 font-arabic font-bold"
            >
              حفظ الإعدادات
            </button>
            <button
              onClick={() => setIsConfigModalOpen(false)}
              className="flex-1 bg-gray-500 text-white py-3 px-6 rounded-xl hover:bg-gray-600 transition-all duration-300 font-arabic font-bold"
            >
              إلغاء
            </button>
          </div>
        </div>
      </Modal>
    </div>
  );
};

export default CloudSyncView;