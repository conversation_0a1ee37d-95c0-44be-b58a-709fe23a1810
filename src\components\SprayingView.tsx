import React, { useState, useEffect } from 'react';
import { Droplets, Plus, Calendar } from 'lucide-react';
import { Spraying } from '../types';
import { getSprayings, addSpraying, getZones, formatDate } from '../utils/database';
import Modal from './Modal';

const SprayingView: React.FC = () => {
  const [sprayings, setSprayings] = useState<Spraying[]>([]);
  const [zones, setZones] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [newSpraying, setNewSpraying] = useState({
    date: new Date().toISOString().split('T')[0],
    material: '',
    commercialName: '', // الاسم التجاري
    activeIngredient: '', // المادة الفعالة
    quantity: 0,
    unit: 'لتر',
    targetZone: 1,
    notes: ''
  });

  // حالة التعديل
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [editSpraying, setEditSpraying] = useState<Spraying | null>(null);

  const [sprayingMaterials, setSprayingMaterials] = useState<string[]>(() => {
    try {
      return JSON.parse(localStorage.getItem('sprayingMaterials') || '[]');
    } catch {
      return [];
    }
  });
  const [showAddMaterialInput, setShowAddMaterialInput] = useState(false);
  const [newMaterialInput, setNewMaterialInput] = useState('');
  const [showMaterialManagement, setShowMaterialManagement] = useState(false);

  // دالة حذف مادة رش
  const deleteSprayingMaterial = (materialToDelete: string) => {
    if (window.confirm(`هل أنت متأكد من حذف المادة "${materialToDelete}"؟`)) {
      const updated = sprayingMaterials.filter(material => material !== materialToDelete);
      setSprayingMaterials(updated);
      localStorage.setItem('sprayingMaterials', JSON.stringify(updated));
      alert('تم حذف المادة بنجاح!');
    }
  };

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      const [sprayingsData, zonesData] = await Promise.all([
        getSprayings(),
        getZones()
      ]);
      setSprayings(sprayingsData);
      setZones(zonesData);
    } catch (error) {
      console.error('خطأ في تحميل البيانات:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleAddSpraying = async () => {
    try {
      const result = await addSpraying(newSpraying);
      if (result.success) {
        await loadData();
        setIsAddModalOpen(false);
        setNewSpraying({
          date: new Date().toISOString().split('T')[0],
          material: '',
          commercialName: '', // الاسم التجاري
          activeIngredient: '', // المادة الفعالة
          quantity: 0,
          unit: 'لتر',
          targetZone: 1,
          notes: ''
        });
        alert('تم إضافة الرش بنجاح!');
      } else {
        alert('خطأ في إضافة الرش: ' + result.error);
      }
    } catch (error) {
      console.error('خطأ في إضافة الرش:', error);
      alert('حدث خطأ أثناء إضافة الرش');
    }
  };

  // فتح نافذة التعديل
  const handleEditClick = (spraying: Spraying) => {
    setEditSpraying(spraying);
    setIsEditModalOpen(true);
  };

  // حفظ التعديل
  const handleSaveEdit = async () => {
    if (!editSpraying) return;
    try {
      const result = await (window as any).updateSpraying(editSpraying); // يجب إضافة الدالة في utils لاحقًا
      if (result?.success) {
        await loadData();
        setIsEditModalOpen(false);
        setEditSpraying(null);
        alert('تم تحديث بيانات الرش بنجاح!');
      } else {
        alert('خطأ في تحديث البيانات: ' + (result?.error || ''));
      }
    } catch (error) {
      alert('حدث خطأ أثناء تحديث البيانات');
    }
  };

  // حذف عملية رش
  const handleDeleteClick = async (spraying: Spraying) => {
    if (!window.confirm('هل أنت متأكد من حذف عملية الرش؟')) return;
    try {
      const result = await (window as any).deleteSpraying(spraying.id); // يجب إضافة الدالة في utils لاحقًا
      if (result?.success) {
        await loadData();
        alert('تم حذف عملية الرش بنجاح!');
      } else {
        alert('خطأ في حذف العملية: ' + (result?.error || ''));
      }
    } catch (error) {
      alert('حدث خطأ أثناء حذف العملية');
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-emerald-600"></div>
      </div>
    );
  }

  return (
    <div className="min-h-screen w-full bg-gradient-to-br from-emerald-50 via-green-50 to-blue-50 py-8 px-2 md:px-8 space-y-8">
      {/* زخرفة دائرية فيروزي أوضح */}
      <div className="absolute -top-16 -left-16 w-56 h-56 bg-cyan-300 opacity-40 rounded-full blur-xl z-0"></div>
      {/* عنوان الصفحة */}
      <div className="flex items-center justify-between bg-white rounded-2xl shadow-md border border-emerald-100 p-6 mb-2 relative z-10">
        <div>
          <h1 className="text-3xl font-bold text-emerald-700 drop-shadow">إدارة الرش</h1>
          <p className="text-gray-600 mt-2">تسجيل عمليات الرش للمراحل الزراعية</p>
        </div>
        <button
          onClick={() => setIsAddModalOpen(true)}
          className="bg-gradient-to-r from-emerald-400 to-emerald-600 text-white px-6 py-3 rounded-xl font-bold flex items-center gap-2 shadow-md hover:shadow-lg border-2 border-emerald-400 hover:border-emerald-600 transform hover:scale-105 transition-all"
        >
          <Plus size={20} />
          إضافة رش جديد
        </button>
      </div>
      {/* جدول الرش داخل بطاقة عصرية */}
      <div className="bg-gradient-to-br from-cyan-100 via-emerald-50 to-blue-200 rounded-2xl shadow-2xl border-2 border-cyan-200/70 p-6 relative z-10">
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-lg font-bold text-emerald-700 drop-shadow">سجل الرش</h3>
        </div>
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-emerald-100">
            <thead className="bg-cyan-50">
              <tr>
                <th className="px-4 py-2 text-right text-sm font-bold text-emerald-700">التاريخ / Date</th>
                <th className="px-4 py-2 text-right text-sm font-bold text-emerald-700">المادة المستخدمة / Material</th>
                <th className="px-4 py-2 text-right text-sm font-bold text-emerald-700">الاسم التجاري / Commercial Name</th>
                <th className="px-4 py-2 text-right text-sm font-bold text-emerald-700">المادة الفعالة / Active Ingredient</th>
                <th className="px-4 py-2 text-right text-sm font-bold text-emerald-700">الكمية / Quantity</th>
                <th className="px-4 py-2 text-right text-sm font-bold text-emerald-700">المرحلة المستهدفة / Target Zone</th>
                <th className="px-4 py-2 text-right text-sm font-bold text-emerald-700">الملاحظات / Notes</th>
                <th className="px-4 py-2 text-right text-sm font-bold text-emerald-700">الإجراءات / Actions</th>
              </tr>
            </thead>
            <tbody>
              {sprayings.map((spraying) => (
                <tr key={spraying.id} className="hover:bg-cyan-50 transition-colors">
                  <td className="px-4 py-2">{formatDate(spraying.date)}</td>
                  <td className="px-4 py-2 font-bold text-emerald-800">{spraying.material}</td>
                  <td className="px-4 py-2">{spraying.commercialName || '-'}</td>
                  <td className="px-4 py-2">{spraying.activeIngredient || '-'}</td>
                  <td className="px-4 py-2">{spraying.quantity} {spraying.unit}</td>
                  <td className="px-4 py-2">{zones.find(z => z.id === spraying.targetZone)?.name || `المرحلة ${spraying.targetZone}`}</td>
                  <td className="px-4 py-2">{spraying.notes || '-'}</td>
                  <td className="px-4 py-2 flex gap-2 justify-center">
                    <button className="bg-gradient-to-r from-sky-400 to-blue-700 text-white p-2 rounded-full shadow hover:from-blue-600 hover:to-blue-800 transition-all duration-200 font-bold" title="تعديل" onClick={() => handleEditClick(spraying)}>
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.232 5.232l3.536 3.536M9 13l6-6m2 2l-6 6m-2 2h6" /></svg>
                    </button>
                    <button className="bg-gradient-to-r from-rose-400 to-red-700 text-white p-2 rounded-full shadow hover:from-red-600 hover:to-red-800 transition-all duration-200 font-bold" title="حذف" onClick={() => handleDeleteClick(spraying)}>
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" /></svg>
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* نافذة إضافة رش جديد */}
      <Modal
        isOpen={isAddModalOpen}
        onClose={() => setIsAddModalOpen(false)}
        title="إضافة رش جديد"
      >
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">التاريخ / Date</label>
            <input
              type="date"
              value={newSpraying.date}
              onChange={(e) => setNewSpraying({...newSpraying, date: e.target.value})}
              className="input-field rounded-xl border-2 border-blue-100 shadow focus:border-blue-400 focus:ring-2 focus:ring-blue-200 transition-all duration-200"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">المادة المستخدمة / Material</label>
            <div className="flex gap-2 items-center">
              <select
                value={newSpraying.material}
                onChange={e => {
                  if (e.target.value === '__add_new__') {
                    setShowAddMaterialInput(true);
                  } else if (e.target.value === '__manage__') {
                    setShowMaterialManagement(true);
                  } else {
                    setNewSpraying({ ...newSpraying, material: e.target.value });
                  }
                }}
                className="input-field rounded-xl border-2 border-green-100 shadow focus:border-green-400 focus:ring-2 focus:ring-green-200 transition-all duration-200"
              >
                <option value="">اختر المادة المستخدمة...</option>
                {sprayingMaterials.map(material => (
                  <option key={material} value={material}>{material}</option>
                ))}
                <option value="__add_new__">+ إضافة مادة جديدة...</option>
                <option value="__manage__">⚙️ إدارة المواد...</option>
              </select>
              {showAddMaterialInput && (
                <div className="flex gap-2 items-center">
                  <input
                    type="text"
                    value={newMaterialInput}
                    onChange={e => setNewMaterialInput(e.target.value)}
                    className="input-field rounded-xl border-2 border-green-200 shadow focus:border-green-400 focus:ring-2 focus:ring-green-200 transition-all duration-200"
                    placeholder="أدخل مادة جديدة..."
                  />
                  <button
                    type="button"
                    className="bg-green-500 text-white px-3 py-1 rounded-xl shadow hover:bg-green-700"
                    onClick={() => {
                      if (newMaterialInput.trim() && !sprayingMaterials.includes(newMaterialInput.trim())) {
                        const updated = [...sprayingMaterials, newMaterialInput.trim()];
                        setSprayingMaterials(updated);
                        localStorage.setItem('sprayingMaterials', JSON.stringify(updated));
                        setNewSpraying({ ...newSpraying, material: newMaterialInput.trim() });
                      }
                      setShowAddMaterialInput(false);
                      setNewMaterialInput('');
                    }}
                  >حفظ</button>
                  <button
                    type="button"
                    className="bg-gray-300 text-gray-700 px-2 py-1 rounded-xl shadow hover:bg-gray-400"
                    onClick={() => { setShowAddMaterialInput(false); setNewMaterialInput(''); }}
                  >إلغاء</button>
                </div>
              )}
            </div>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">الاسم التجاري / Commercial Name</label>
            <input
              type="text"
              value={newSpraying.commercialName}
              onChange={(e) => setNewSpraying({...newSpraying, commercialName: e.target.value})}
              className="input-field rounded-xl border-2 border-blue-100 shadow focus:border-blue-400 focus:ring-2 focus:ring-blue-200 transition-all duration-200"
              placeholder="أدخل الاسم التجاري... / Enter commercial name..."
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">المادة الفعالة / Active Ingredient</label>
            <input
              type="text"
              value={newSpraying.activeIngredient}
              onChange={(e) => setNewSpraying({...newSpraying, activeIngredient: e.target.value})}
              className="input-field rounded-xl border-2 border-green-100 shadow focus:border-green-400 focus:ring-2 focus:ring-green-200 transition-all duration-200"
              placeholder="أدخل المادة الفعالة... / Enter active ingredient..."
            />
          </div>
          <div className="flex gap-4">
            <div className="flex-1">
              <label className="block text-sm font-medium text-gray-700 mb-2">الكمية</label>
              <input
                type="number"
                value={newSpraying.quantity}
                onChange={(e) => setNewSpraying({...newSpraying, quantity: Number(e.target.value)})}
                className="input-field rounded-xl border-2 border-indigo-100 shadow focus:border-indigo-400 focus:ring-2 focus:ring-indigo-200 transition-all duration-200"
              />
            </div>
            <div className="flex-1">
              <label className="block text-sm font-medium text-gray-700 mb-2">الوحدة</label>
              <select
                value={newSpraying.unit}
                onChange={(e) => setNewSpraying({...newSpraying, unit: e.target.value})}
                className="input-field rounded-xl border-2 border-green-100 shadow focus:border-green-400 focus:ring-2 focus:ring-green-200 transition-all duration-200"
              >
                <option value="لتر">لتر</option>
                <option value="مل">مل</option>
                <option value="كجم">كجم</option>
              </select>
            </div>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">المرحلة المستهدفة</label>
            <select
              value={newSpraying.targetZone}
              onChange={(e) => setNewSpraying({...newSpraying, targetZone: Number(e.target.value)})}
              className="input-field rounded-xl border-2 border-indigo-100 shadow focus:border-indigo-400 focus:ring-2 focus:ring-indigo-200 transition-all duration-200"
            >
              {zones.map(zone => (
                <option key={zone.id} value={zone.id}>{zone.name || `المرحلة ${zone.id}`}</option>
              ))}
            </select>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">الملاحظات</label>
            <textarea
              value={newSpraying.notes}
              onChange={(e) => setNewSpraying({...newSpraying, notes: e.target.value})}
              className="input-field rounded-xl border-2 border-blue-100 shadow focus:border-blue-400 focus:ring-2 focus:ring-blue-200 transition-all duration-200"
            />
          </div>
          <div className="flex justify-end gap-4 mt-6">
            <button
              onClick={handleAddSpraying}
              className="bg-gradient-to-r from-emerald-500 to-blue-500 text-white px-6 py-2 rounded-xl shadow hover:from-blue-600 hover:to-emerald-600 transition-all duration-300 font-arabic font-medium tracking-wide"
            >
              حفظ
            </button>
            <button
              onClick={() => setIsAddModalOpen(false)}
              className="bg-gradient-to-r from-gray-300 to-gray-400 text-gray-800 px-6 py-2 rounded-xl shadow hover:from-gray-400 hover:to-gray-500 transition-all duration-300 font-arabic font-medium tracking-wide"
            >
              إلغاء
            </button>
          </div>
        </div>
      </Modal>

      {/* نافذة تعديل الرش */}
      <Modal isOpen={isEditModalOpen} onClose={() => setIsEditModalOpen(false)} title="تعديل بيانات الرش">
        {editSpraying && (
          <form
            onSubmit={e => {
              e.preventDefault();
              handleSaveEdit();
            }}
            className="space-y-4"
          >
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block mb-1 text-sm font-medium">التاريخ / Date</label>
                <input
                  type="date"
                  className="w-full px-4 py-2 border border-emerald-300 rounded-xl focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
                  value={editSpraying.date}
                  onChange={e => setEditSpraying({ ...editSpraying, date: e.target.value })}
                  required
                />
              </div>
              <div>
                <label className="block mb-1 text-sm font-medium">المادة المستخدمة / Material</label>
                <div className="flex gap-2 items-center">
                  <select
                    className="w-full px-4 py-2 border border-emerald-300 rounded-xl focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
                    value={editSpraying.material}
                    onChange={e => {
                      if (e.target.value === '__add_new__') {
                        setShowAddMaterialInput(true);
                      } else if (e.target.value === '__manage__') {
                        setShowMaterialManagement(true);
                      } else {
                        setEditSpraying({ ...editSpraying, material: e.target.value });
                      }
                    }}
                    required
                  >
                    <option value="">اختر المادة المستخدمة...</option>
                    {sprayingMaterials.map(material => (
                      <option key={material} value={material}>{material}</option>
                    ))}
                    <option value="__add_new__">+ إضافة مادة جديدة...</option>
                    <option value="__manage__">⚙️ إدارة المواد...</option>
                  </select>
                  {showAddMaterialInput && (
                    <div className="flex gap-2 items-center">
                      <input
                        type="text"
                        value={newMaterialInput}
                        onChange={e => setNewMaterialInput(e.target.value)}
                        className="input-field rounded-xl border-2 border-green-200 shadow focus:border-green-400 focus:ring-2 focus:ring-green-200 transition-all duration-200"
                        placeholder="أدخل مادة جديدة..."
                      />
                      <button
                        type="button"
                        className="bg-green-500 text-white px-3 py-1 rounded-xl shadow hover:bg-green-700"
                        onClick={() => {
                          if (newMaterialInput.trim() && !sprayingMaterials.includes(newMaterialInput.trim())) {
                            const updated = [...sprayingMaterials, newMaterialInput.trim()];
                            setSprayingMaterials(updated);
                            localStorage.setItem('sprayingMaterials', JSON.stringify(updated));
                            setEditSpraying({ ...editSpraying, material: newMaterialInput.trim() });
                          }
                          setShowAddMaterialInput(false);
                          setNewMaterialInput('');
                        }}
                      >حفظ</button>
                      <button
                        type="button"
                        className="bg-gray-300 text-gray-700 px-2 py-1 rounded-xl shadow hover:bg-gray-400"
                        onClick={() => { setShowAddMaterialInput(false); setNewMaterialInput(''); }}
                      >إلغاء</button>
                    </div>
                  )}
                </div>
              </div>
              <div>
                <label className="block mb-1 text-sm font-medium">الاسم التجاري / Commercial Name</label>
                <input
                  type="text"
                  className="w-full px-4 py-2 border border-blue-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  value={editSpraying.commercialName}
                  onChange={e => setEditSpraying({ ...editSpraying, commercialName: e.target.value })}
                />
              </div>
              <div>
                <label className="block mb-1 text-sm font-medium">المادة الفعالة / Active Ingredient</label>
                <input
                  type="text"
                  className="w-full px-4 py-2 border border-green-300 rounded-xl focus:ring-2 focus:ring-green-500 focus:border-green-500"
                  value={editSpraying.activeIngredient}
                  onChange={e => setEditSpraying({ ...editSpraying, activeIngredient: e.target.value })}
                />
              </div>
              <div>
                <label className="block mb-1 text-sm font-medium">الكمية</label>
                <input
                  type="number"
                  className="w-full px-4 py-2 border border-emerald-300 rounded-xl focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
                  value={editSpraying.quantity}
                  onChange={e => setEditSpraying({ ...editSpraying, quantity: Number(e.target.value) })}
                  min="0"
                  required
                />
              </div>
              <div>
                <label className="block mb-1 text-sm font-medium">الوحدة</label>
                <select
                  className="w-full px-4 py-2 border border-emerald-300 rounded-xl focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
                  value={editSpraying.unit}
                  onChange={e => setEditSpraying({ ...editSpraying, unit: e.target.value })}
                  required
                >
                  <option value="لتر">لتر</option>
                  <option value="كيلوغرام">كيلوغرام</option>
                  <option value="جرام">جرام</option>
                  <option value="ملليلتر">ملليلتر</option>
                </select>
              </div>
              <div>
                <label className="block mb-1 text-sm font-medium">المرحلة المستهدفة</label>
                <select
                  className="w-full px-4 py-2 border border-emerald-300 rounded-xl focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
                  value={editSpraying.targetZone}
                  onChange={e => setEditSpraying({ ...editSpraying, targetZone: Number(e.target.value) })}
                  required
                >
                  {zones.map((zone) => (
                    <option key={zone.id} value={zone.id}>{zone.name}</option>
                  ))}
                </select>
              </div>
              <div className="md:col-span-2">
                <label className="block mb-1 text-sm font-medium">ملاحظات</label>
                <textarea
                  className="w-full px-4 py-2 border border-emerald-300 rounded-xl focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
                  value={editSpraying.notes}
                  onChange={e => setEditSpraying({ ...editSpraying, notes: e.target.value })}
                  rows={2}
                />
              </div>
            </div>
            <div className="flex justify-end gap-2 mt-4">
              <button
                type="button"
                className="bg-gray-100 text-gray-700 px-6 py-2 rounded-xl font-bold hover:bg-gray-200 transition-all shadow"
                onClick={() => setIsEditModalOpen(false)}
              >
                إلغاء
              </button>
              <button
                type="submit"
                className="bg-gradient-to-r from-blue-500 to-blue-700 text-white px-6 py-2 rounded-xl font-bold hover:from-blue-600 hover:to-blue-800 transition-all shadow"
              >
                حفظ التعديلات
              </button>
            </div>
          </form>
        )}
      </Modal>

      {/* نافذة إدارة المواد المستخدمة في الرش */}
      <Modal
        isOpen={showMaterialManagement}
        onClose={() => setShowMaterialManagement(false)}
        title="إدارة المواد المستخدمة في الرش"
      >
        <div className="space-y-4">
          <div className="mb-4">
            <h4 className="text-lg font-semibold text-gray-800 mb-2">المواد المسجلة:</h4>
            {sprayingMaterials.length === 0 ? (
              <p className="text-gray-500 text-center py-4">لا توجد مواد مسجلة</p>
            ) : (
              <div className="space-y-2 max-h-60 overflow-y-auto">
                {sprayingMaterials.map((material, index) => (
                  <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <span className="font-medium text-gray-700">{material}</span>
                    <button
                      onClick={() => deleteSprayingMaterial(material)}
                      className="text-red-600 hover:text-red-800 p-1 rounded transition-colors"
                      title="حذف"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                      </svg>
                    </button>
                  </div>
                ))}
              </div>
            )}
          </div>
          
          <div className="border-t pt-4">
            <h4 className="text-lg font-semibold text-gray-800 mb-2">إضافة مادة جديدة:</h4>
            <div className="flex gap-2">
              <input
                type="text"
                value={newMaterialInput}
                onChange={e => setNewMaterialInput(e.target.value)}
                className="flex-1 input-field rounded-xl border-2 border-green-200 shadow focus:border-green-400 focus:ring-2 focus:ring-green-200 transition-all duration-200"
                placeholder="أدخل مادة جديدة..."
              />
              <button
                onClick={() => {
                  if (newMaterialInput.trim() && !sprayingMaterials.includes(newMaterialInput.trim())) {
                    const updated = [...sprayingMaterials, newMaterialInput.trim()];
                    setSprayingMaterials(updated);
                    localStorage.setItem('sprayingMaterials', JSON.stringify(updated));
                    setNewMaterialInput('');
                    alert('تم إضافة المادة بنجاح!');
                  } else if (sprayingMaterials.includes(newMaterialInput.trim())) {
                    alert('هذه المادة موجودة بالفعل!');
                  }
                }}
                className="bg-green-500 text-white px-4 py-2 rounded-xl shadow hover:bg-green-700 transition-colors"
              >
                إضافة
              </button>
            </div>
          </div>
          
          <div className="flex justify-end gap-2 mt-6">
            <button
              onClick={() => setShowMaterialManagement(false)}
              className="bg-gray-300 text-gray-700 px-6 py-2 rounded-xl shadow hover:bg-gray-400 transition-colors"
            >
              إغلاق
            </button>
          </div>
        </div>
      </Modal>
    </div>
  );
};

export default SprayingView; 