import React, { useState, useEffect } from 'react';
import { Package, Plus } from 'lucide-react';
import { Fertilization } from '../types';
import { getFertilizations, addFertilization, getZones, formatDate } from '../utils/database';
import Modal from './Modal';

const FertilizationView: React.FC = () => {
  const [fertilizations, setFertilizations] = useState<Fertilization[]>([]);
  const [zones, setZones] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [newFertilization, setNewFertilization] = useState({
    date: new Date().toISOString().split('T')[0],
    fertilizerType: '',
    commercialName: '', // الاسم التجاري
    activeIngredient: '', // المادة الفعالة
    quantity: 0,
    unit: 'كيلوغرام',
    targetZone: 1,
    notes: ''
  });

  // حالة التعديل
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [editFertilization, setEditFertilization] = useState<Fertilization | null>(null);

  const [fertilizerTypes, setFertilizerTypes] = useState<string[]>(() => {
    try {
      return JSON.parse(localStorage.getItem('fertilizerTypes') || '[]');
    } catch {
      return [];
    }
  });
  const [showAddTypeInput, setShowAddTypeInput] = useState(false);
  const [newTypeInput, setNewTypeInput] = useState('');
  const [showTypeManagement, setShowTypeManagement] = useState(false);

  // دالة حذف نوع سماد
  const deleteFertilizerType = (typeToDelete: string) => {
    if (window.confirm(`هل أنت متأكد من حذف نوع السماد "${typeToDelete}"؟`)) {
      const updated = fertilizerTypes.filter(type => type !== typeToDelete);
      setFertilizerTypes(updated);
      localStorage.setItem('fertilizerTypes', JSON.stringify(updated));
      alert('تم حذف نوع السماد بنجاح!');
    }
  };

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      const [fertilizationsData, zonesData] = await Promise.all([
        getFertilizations(),
        getZones()
      ]);
      setFertilizations(fertilizationsData);
      setZones(zonesData);
    } catch (error) {
      console.error('خطأ في تحميل البيانات:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleAddFertilization = async () => {
    try {
      const result = await addFertilization(newFertilization);
      if (result.success) {
        await loadData();
        setIsAddModalOpen(false);
        setNewFertilization({
          date: new Date().toISOString().split('T')[0],
          fertilizerType: '',
          commercialName: '',
          activeIngredient: '',
          quantity: 0,
          unit: 'كيلوغرام',
          targetZone: 1,
          notes: ''
        });
        alert('تم إضافة التسميد بنجاح!');
      } else {
        alert('خطأ في إضافة التسميد: ' + result.error);
      }
    } catch (error) {
      console.error('خطأ في إضافة التسميد:', error);
      alert('حدث خطأ أثناء إضافة التسميد');
    }
  };

  // فتح نافذة التعديل
  const handleEditClick = (fertilization: Fertilization) => {
    setEditFertilization(fertilization);
    setIsEditModalOpen(true);
  };

  // حفظ التعديل
  const handleSaveEdit = async () => {
    if (!editFertilization) return;
    try {
      const result = await (window as any).updateFertilization(editFertilization); // يجب إضافة الدالة في utils لاحقًا
      if (result?.success) {
        await loadData();
        setIsEditModalOpen(false);
        setEditFertilization(null);
        alert('تم تحديث بيانات التسميد بنجاح!');
      } else {
        alert('خطأ في تحديث البيانات: ' + (result?.error || ''));
      }
    } catch (error) {
      alert('حدث خطأ أثناء تحديث البيانات');
    }
  };

  // حذف عملية تسميد
  const handleDeleteClick = async (fertilization: Fertilization) => {
    if (!window.confirm('هل أنت متأكد من حذف عملية التسميد؟')) return;
    try {
      const result = await (window as any).deleteFertilization(fertilization.id); // يجب إضافة الدالة في utils لاحقًا
      if (result?.success) {
        await loadData();
        alert('تم حذف عملية التسميد بنجاح!');
      } else {
        alert('خطأ في حذف العملية: ' + (result?.error || ''));
      }
    } catch (error) {
      alert('حدث خطأ أثناء حذف العملية');
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-emerald-600"></div>
      </div>
    );
  }

  return (
    <>
      {/* زخارف خلفية */}
      <div className="absolute top-0 left-0 w-full h-full pointer-events-none z-0">
        <div className="absolute -top-24 -left-24 w-80 h-80 bg-gradient-to-br from-green-300 via-blue-200 to-indigo-200 opacity-40 rounded-full blur-2xl animate-pulse"></div>
        <div className="absolute bottom-0 right-0 w-96 h-96 bg-gradient-to-tr from-indigo-200 via-blue-100 to-green-200 opacity-30 rounded-full blur-2xl animate-pulse-slow"></div>
      </div>
      <div className="relative z-10 min-h-screen bg-gradient-to-br from-green-200 via-blue-100 to-indigo-200 p-6 space-y-8">
        {/* عنوان الصفحة */}
        <div className="flex items-center justify-between mb-6">
          <div>
            <h1 className="text-4xl font-extrabold bg-gradient-to-r from-green-600 via-blue-600 to-indigo-600 bg-clip-text text-transparent drop-shadow-lg tracking-tight">إدارة التسميد</h1>
            <div className="h-1 w-24 bg-gradient-to-r from-green-400 via-blue-400 to-indigo-400 rounded-full mt-2 mb-1 animate-pulse"></div>
            <p className="text-lg text-blue-700 mt-2 font-medium">تسجيل عمليات التسميد للمراحل الزراعية</p>
          </div>
          <button
            onClick={() => setIsAddModalOpen(true)}
            className="bg-gradient-to-r from-green-500 via-blue-500 to-indigo-500 text-white px-8 py-4 rounded-2xl shadow-2xl hover:from-indigo-600 hover:to-green-600 transition-all duration-300 flex items-center gap-3 font-arabic font-bold text-lg tracking-wide transform hover:scale-105 active:scale-95 animate-bounce-slow"
          >
            <Plus size={28} className="animate-spin-slow" />
            إضافة تسميد جديد
          </button>
        </div>

        {/* جدول التسميد */}
        <div className="bg-gradient-to-br from-green-100 via-blue-100 to-indigo-100/80 bg-opacity-80 rounded-3xl shadow-2xl border-0 p-8 transition-all duration-300 hover:shadow-emerald-200 hover:scale-[1.01] backdrop-blur-md">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-2xl font-extrabold bg-gradient-to-r from-green-600 via-blue-600 to-indigo-600 bg-clip-text text-transparent drop-shadow">سجل التسميد</h3>
          </div>
          <div className="overflow-x-auto">
            <table className="min-w-full text-center">
              <thead className="bg-gradient-to-r from-green-100 via-blue-100 to-indigo-100">
                <tr>
                  <th className="py-3 px-4 text-lg font-bold text-indigo-700 border-b-2 border-indigo-200">التاريخ / Date</th>
                  <th className="py-3 px-4 text-lg font-bold text-green-700 border-b-2 border-green-200">نوع السماد / Fertilizer Type</th>
                  <th className="py-3 px-4 text-lg font-bold text-green-700 border-b-2 border-green-200">الاسم التجاري / Commercial Name</th>
                  <th className="py-3 px-4 text-lg font-bold text-green-700 border-b-2 border-green-200">المادة الفعالة / Active Ingredient</th>
                  <th className="py-3 px-4 text-lg font-bold text-blue-700 border-b-2 border-blue-200">الكمية / Quantity</th>
                  <th className="py-3 px-4 text-lg font-bold text-emerald-700 border-b-2 border-emerald-200">المرحلة المستهدفة / Target Zone</th>
                  <th className="py-3 px-4 text-lg font-bold text-gray-700 border-b-2 border-gray-200">الملاحظات / Notes</th>
                  <th className="py-3 px-4 text-lg font-bold text-indigo-700 border-b-2 border-indigo-200">الإجراءات / Actions</th>
                </tr>
              </thead>
              <tbody>
                {fertilizations.map((fertilization) => (
                  <tr key={fertilization.id} className="hover:bg-gradient-to-l hover:from-blue-50 hover:via-green-50 hover:to-indigo-50 transition-all duration-200 group">
                    <td className="py-3 px-4 font-semibold text-indigo-800 group-hover:text-indigo-900">{formatDate(fertilization.date)}</td>
                    <td className="py-3 px-4 font-bold text-green-700 group-hover:text-green-900">{fertilization.fertilizerType}</td>
                    <td className="py-3 px-4">{fertilization.commercialName || '-'}</td>
                    <td className="py-3 px-4">{fertilization.activeIngredient || '-'}</td>
                    <td className="py-3 px-4 text-blue-700 group-hover:text-blue-900">{fertilization.quantity} {fertilization.unit}</td>
                    <td className="py-3 px-4 text-emerald-700 group-hover:text-emerald-900">{zones.find(z => z.id === fertilization.targetZone)?.name || `المرحلة ${fertilization.targetZone}`}</td>
                    <td className="py-3 px-4 text-gray-600 group-hover:text-gray-800">{fertilization.notes || '-'}</td>
                    <td className="py-3 px-4 flex gap-2 justify-center">
                      <button className="text-blue-600 hover:text-blue-800 p-2 rounded-lg transition-colors bg-blue-50 hover:bg-blue-100 shadow-sm" title="تعديل" onClick={() => handleEditClick(fertilization)}>
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.232 5.232l3.536 3.536M9 13l6-6m2 2l-6 6m-2 2h6" /></svg>
                      </button>
                      <button className="text-red-600 hover:text-red-800 p-2 rounded-lg transition-colors bg-red-50 hover:bg-red-100 shadow-sm" title="حذف" onClick={() => handleDeleteClick(fertilization)}>
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" /></svg>
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>

        {/* نافذة إضافة تسميد جديد */}
        <Modal
          isOpen={isAddModalOpen}
          onClose={() => setIsAddModalOpen(false)}
          title="إضافة تسميد جديد"
          >
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">التاريخ / Date</label>
              <input
                type="date"
                value={newFertilization.date}
                onChange={(e) => setNewFertilization({...newFertilization, date: e.target.value})}
                className="input-field rounded-xl border-2 border-blue-100 shadow focus:border-blue-400 focus:ring-2 focus:ring-blue-200 transition-all duration-200"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">نوع السماد / Fertilizer Type</label>
              <div className="flex gap-2 items-center">
                <select
                  value={newFertilization.fertilizerType}
                  onChange={e => {
                    if (e.target.value === '__add_new__') {
                      setShowAddTypeInput(true);
                    } else if (e.target.value === '__manage__') {
                      setShowTypeManagement(true);
                    } else {
                      setNewFertilization({ ...newFertilization, fertilizerType: e.target.value });
                    }
                  }}
                  className="input-field rounded-xl border-2 border-green-100 shadow focus:border-green-400 focus:ring-2 focus:ring-green-200 transition-all duration-200"
                >
                  <option value="">اختر نوع السماد...</option>
                  {fertilizerTypes.map(type => (
                    <option key={type} value={type}>{type}</option>
                  ))}
                  <option value="__add_new__">+ إضافة نوع جديد...</option>
                  <option value="__manage__">⚙️ إدارة الأنواع...</option>
                </select>
                {showAddTypeInput && (
                  <div className="flex gap-2 items-center">
                    <input
                      type="text"
                      value={newTypeInput}
                      onChange={e => setNewTypeInput(e.target.value)}
                      className="input-field rounded-xl border-2 border-green-200 shadow focus:border-green-400 focus:ring-2 focus:ring-green-200 transition-all duration-200"
                      placeholder="أدخل نوع جديد..."
                    />
                    <button
                      type="button"
                      className="bg-green-500 text-white px-3 py-1 rounded-xl shadow hover:bg-green-700"
                      onClick={() => {
                        if (newTypeInput.trim() && !fertilizerTypes.includes(newTypeInput.trim())) {
                          const updated = [...fertilizerTypes, newTypeInput.trim()];
                          setFertilizerTypes(updated);
                          localStorage.setItem('fertilizerTypes', JSON.stringify(updated));
                          setNewFertilization({ ...newFertilization, fertilizerType: newTypeInput.trim() });
                        }
                        setShowAddTypeInput(false);
                        setNewTypeInput('');
                      }}
                    >حفظ</button>
                    <button
                      type="button"
                      className="bg-gray-300 text-gray-700 px-2 py-1 rounded-xl shadow hover:bg-gray-400"
                      onClick={() => { setShowAddTypeInput(false); setNewTypeInput(''); }}
                    >إلغاء</button>
                  </div>
                )}
              </div>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">الاسم التجاري / Commercial Name</label>
              <input
                type="text"
                value={newFertilization.commercialName}
                onChange={(e) => setNewFertilization({...newFertilization, commercialName: e.target.value})}
                className="input-field rounded-xl border-2 border-blue-100 shadow focus:border-blue-400 focus:ring-2 focus:ring-blue-200 transition-all duration-200"
                placeholder="أدخل الاسم التجاري... / Enter commercial name..."
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">المادة الفعالة / Active Ingredient</label>
              <input
                type="text"
                value={newFertilization.activeIngredient}
                onChange={(e) => setNewFertilization({...newFertilization, activeIngredient: e.target.value})}
                className="input-field rounded-xl border-2 border-green-100 shadow focus:border-green-400 focus:ring-2 focus:ring-green-200 transition-all duration-200"
                placeholder="أدخل المادة الفعالة... / Enter active ingredient..."
              />
            </div>
            <div className="flex gap-4">
              <div className="flex-1">
                <label className="block text-sm font-medium text-gray-700 mb-2">الكمية / Quantity</label>
                <input
                  type="number"
                  value={newFertilization.quantity}
                  onChange={(e) => setNewFertilization({...newFertilization, quantity: Number(e.target.value)})}
                  className="input-field rounded-xl border-2 border-indigo-100 shadow focus:border-indigo-400 focus:ring-2 focus:ring-indigo-200 transition-all duration-200"
                />
              </div>
              <div className="flex-1">
                <label className="block text-sm font-medium text-gray-700 mb-2">الوحدة / Unit</label>
                <select
                  value={newFertilization.unit}
                  onChange={(e) => setNewFertilization({...newFertilization, unit: e.target.value})}
                  className="input-field rounded-xl border-2 border-green-100 shadow focus:border-green-400 focus:ring-2 focus:ring-green-200 transition-all duration-200"
                >
                  <option value="كيلوغرام">كيلوغرام</option>
                  <option value="لتر">لتر</option>
                </select>
              </div>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">المرحلة المستهدفة / Target Zone</label>
              <select
                value={newFertilization.targetZone}
                onChange={(e) => setNewFertilization({...newFertilization, targetZone: Number(e.target.value)})}
                className="input-field rounded-xl border-2 border-indigo-100 shadow focus:border-indigo-400 focus:ring-2 focus:ring-indigo-200 transition-all duration-200"
              >
                {zones.map(zone => (
                  <option key={zone.id} value={zone.id}>{zone.name || `المرحلة ${zone.id}`}</option>
                ))}
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">الملاحظات / Notes</label>
              <textarea
                value={newFertilization.notes}
                onChange={(e) => setNewFertilization({...newFertilization, notes: e.target.value})}
                className="input-field rounded-xl border-2 border-blue-100 shadow focus:border-blue-400 focus:ring-2 focus:ring-blue-200 transition-all duration-200"
              />
            </div>
            <div className="flex justify-end gap-4 mt-6">
              <button
                onClick={handleAddFertilization}
                className="bg-gradient-to-r from-green-500 to-blue-500 text-white px-6 py-2 rounded-xl shadow hover:from-blue-600 hover:to-green-600 transition-all duration-300 font-arabic font-medium tracking-wide"
              >
                حفظ / Save
              </button>
              <button
                onClick={() => setIsAddModalOpen(false)}
                className="bg-gradient-to-r from-gray-300 to-gray-400 text-gray-800 px-6 py-2 rounded-xl shadow hover:from-gray-400 hover:to-gray-500 transition-all duration-300 font-arabic font-medium tracking-wide"
              >
                إلغاء / Cancel
              </button>
            </div>
          </div>
        </Modal>

        {/* نافذة تعديل التسميد */}
        <Modal isOpen={isEditModalOpen} onClose={() => setIsEditModalOpen(false)} title="تعديل بيانات التسميد">
          {editFertilization && (
            <form
              onSubmit={e => {
                e.preventDefault();
                handleSaveEdit();
              }}
              className="space-y-4"
            >
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block mb-1 text-sm font-medium">التاريخ / Date</label>
                  <input
                    type="date"
                    className="input-field"
                    value={editFertilization.date}
                    onChange={e => setEditFertilization({ ...editFertilization, date: e.target.value })}
                    required
                  />
                </div>
                <div>
                  <label className="block mb-1 text-sm font-medium">نوع السماد / Fertilizer Type</label>
                  <div className="flex gap-2 items-center">
                    <select
                      className="input-field"
                      value={editFertilization.fertilizerType}
                      onChange={e => {
                        if (e.target.value === '__add_new__') {
                          setShowAddTypeInput(true);
                        } else if (e.target.value === '__manage__') {
                          setShowTypeManagement(true);
                        } else {
                          setEditFertilization({ ...editFertilization, fertilizerType: e.target.value });
                        }
                      }}
                      required
                    >
                      <option value="">اختر نوع السماد...</option>
                      {fertilizerTypes.map(type => (
                        <option key={type} value={type}>{type}</option>
                      ))}
                      <option value="__add_new__">+ إضافة نوع جديد...</option>
                      <option value="__manage__">⚙️ إدارة الأنواع...</option>
                    </select>
                    {showAddTypeInput && (
                      <div className="flex gap-2 items-center">
                        <input
                          type="text"
                          value={newTypeInput}
                          onChange={e => setNewTypeInput(e.target.value)}
                          className="input-field rounded-xl border-2 border-green-200 shadow focus:border-green-400 focus:ring-2 focus:ring-green-200 transition-all duration-200"
                          placeholder="أدخل نوع جديد..."
                        />
                        <button
                          type="button"
                          className="bg-green-500 text-white px-3 py-1 rounded-xl shadow hover:bg-green-700"
                          onClick={() => {
                            if (newTypeInput.trim() && !fertilizerTypes.includes(newTypeInput.trim())) {
                              const updated = [...fertilizerTypes, newTypeInput.trim()];
                              setFertilizerTypes(updated);
                              localStorage.setItem('fertilizerTypes', JSON.stringify(updated));
                              setEditFertilization({ ...editFertilization, fertilizerType: newTypeInput.trim() });
                            }
                            setShowAddTypeInput(false);
                            setNewTypeInput('');
                          }}
                        >حفظ</button>
                        <button
                          type="button"
                          className="bg-gray-300 text-gray-700 px-2 py-1 rounded-xl shadow hover:bg-gray-400"
                          onClick={() => { setShowAddTypeInput(false); setNewTypeInput(''); }}
                        >إلغاء</button>
                      </div>
                    )}
                  </div>
                </div>
                <div>
                  <label className="block mb-1 text-sm font-medium">الاسم التجاري / Commercial Name</label>
                  <input
                    type="text"
                    className="input-field"
                    value={editFertilization.commercialName}
                    onChange={e => setEditFertilization({ ...editFertilization, commercialName: e.target.value })}
                  />
                </div>
                <div>
                  <label className="block mb-1 text-sm font-medium">المادة الفعالة / Active Ingredient</label>
                  <input
                    type="text"
                    className="input-field"
                    value={editFertilization.activeIngredient}
                    onChange={e => setEditFertilization({ ...editFertilization, activeIngredient: e.target.value })}
                  />
                </div>
                <div>
                  <label className="block mb-1 text-sm font-medium">الكمية / Quantity</label>
                  <input
                    type="number"
                    className="input-field"
                    value={editFertilization.quantity}
                    onChange={e => setEditFertilization({ ...editFertilization, quantity: Number(e.target.value) })}
                    min="0"
                    required
                  />
                </div>
                <div>
                  <label className="block mb-1 text-sm font-medium">الوحدة / Unit</label>
                  <select
                    className="input-field"
                    value={editFertilization.unit}
                    onChange={e => setEditFertilization({ ...editFertilization, unit: e.target.value })}
                    required
                  >
                    <option value="كيلوغرام">كيلوغرام</option>
                    <option value="جرام">جرام</option>
                    <option value="لتر">لتر</option>
                    <option value="كيس">كيس</option>
                  </select>
                </div>
                <div>
                  <label className="block mb-1 text-sm font-medium">المرحلة المستهدفة / Target Zone</label>
                  <select
                    className="input-field"
                    value={editFertilization.targetZone}
                    onChange={e => setEditFertilization({ ...editFertilization, targetZone: Number(e.target.value) })}
                    required
                  >
                    {zones.map((zone) => (
                      <option key={zone.id} value={zone.id}>{zone.name}</option>
                    ))}
                  </select>
                </div>
                <div className="md:col-span-2">
                  <label className="block mb-1 text-sm font-medium">ملاحظات / Notes</label>
                  <textarea
                    className="input-field"
                    value={editFertilization.notes}
                    onChange={e => setEditFertilization({ ...editFertilization, notes: e.target.value })}
                    rows={2}
                  />
                </div>
              </div>
              <div className="flex justify-end gap-2 mt-4">
                <button
                  type="button"
                  className="bg-gray-100 text-gray-700 px-6 py-2 rounded-lg font-semibold hover:bg-gray-200"
                  onClick={() => setIsEditModalOpen(false)}
                >
                  إلغاء / Cancel
                </button>
                <button
                  type="submit"
                  className="bg-blue-600 text-white px-6 py-2 rounded-lg font-semibold hover:bg-blue-700"
                >
                  حفظ التعديلات / Save Changes
                </button>
              </div>
            </form>
          )}
        </Modal>
      </div>

      {/* نافذة إدارة أنواع السماد */}
      <Modal
        isOpen={showTypeManagement}
        onClose={() => setShowTypeManagement(false)}
        title="إدارة أنواع السماد"
      >
        <div className="space-y-4">
          <div className="mb-4">
            <h4 className="text-lg font-semibold text-gray-800 mb-2">الأنواع المسجلة:</h4>
            {fertilizerTypes.length === 0 ? (
              <p className="text-gray-500 text-center py-4">لا توجد أنواع مسجلة</p>
            ) : (
              <div className="space-y-2 max-h-60 overflow-y-auto">
                {fertilizerTypes.map((type, index) => (
                  <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <span className="font-medium text-gray-700">{type}</span>
                    <button
                      onClick={() => deleteFertilizerType(type)}
                      className="text-red-600 hover:text-red-800 p-1 rounded transition-colors"
                      title="حذف"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                      </svg>
                    </button>
                  </div>
                ))}
              </div>
            )}
          </div>
          
          <div className="border-t pt-4">
            <h4 className="text-lg font-semibold text-gray-800 mb-2">إضافة نوع جديد:</h4>
            <div className="flex gap-2">
              <input
                type="text"
                value={newTypeInput}
                onChange={e => setNewTypeInput(e.target.value)}
                className="flex-1 input-field rounded-xl border-2 border-green-200 shadow focus:border-green-400 focus:ring-2 focus:ring-green-200 transition-all duration-200"
                placeholder="أدخل نوع جديد..."
              />
              <button
                onClick={() => {
                  if (newTypeInput.trim() && !fertilizerTypes.includes(newTypeInput.trim())) {
                    const updated = [...fertilizerTypes, newTypeInput.trim()];
                    setFertilizerTypes(updated);
                    localStorage.setItem('fertilizerTypes', JSON.stringify(updated));
                    setNewTypeInput('');
                    alert('تم إضافة نوع السماد بنجاح!');
                  } else if (fertilizerTypes.includes(newTypeInput.trim())) {
                    alert('هذا النوع موجود بالفعل!');
                  }
                }}
                className="bg-green-500 text-white px-4 py-2 rounded-xl shadow hover:bg-green-700 transition-colors"
              >
                إضافة
              </button>
            </div>
          </div>
          
          <div className="flex justify-end gap-2 mt-6">
            <button
              onClick={() => setShowTypeManagement(false)}
              className="bg-gray-300 text-gray-700 px-6 py-2 rounded-xl shadow hover:bg-gray-400 transition-colors"
            >
              إغلاق
            </button>
          </div>
        </div>
      </Modal>
    </>
  );
};

export default FertilizationView; 