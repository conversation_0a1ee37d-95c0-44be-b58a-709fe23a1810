// ===== أنواع البيانات الأساسية =====

export interface Zone {
  id: number;
  name: string;
  status: 'active' | 'inactive' | 'harvesting' | 'preparing';
  plantingDate: string;
  harvestDate: string;
  lettuceType: 'iceberg' | 'romaine';
  irrigationStatus: 'scheduled' | 'completed' | 'pending';
  lastIrrigation: string;
  nextIrrigation: string;
  notes: string;
  valves: IrrigationValve[];
}

export interface IrrigationValve {
  id: number;
  zoneId: number;
  valveNumber: number;
  lettuceType: 'iceberg' | 'romaine';
  status: 'active' | 'inactive';
  lastIrrigation: string;
  nextIrrigation: string;
}

export interface Spraying {
  id: number;
  date: string;
  material: string;
  commercialName: string; // الاسم التجاري
  activeIngredient: string; // المادة الفعالة
  quantity: number;
  unit: string;
  targetZone: number;
  notes: string;
  createdAt: string;
}

export interface Fertilization {
  id: number;
  date: string;
  fertilizerType: string;
  commercialName: string; // الاسم التجاري
  activeIngredient: string; // المادة الفعالة
  quantity: number;
  unit: string;
  targetZone: number;
  notes: string;
  createdAt: string;
}

export interface InventoryItem {
  id: number;
  name: string;
  category: 'pesticides' | 'fertilizers' | 'seeds' | 'packaging' | 'equipment';
  quantity: number;
  unit: string;
  minQuantity: number;
  commercialName?: string;
  activeIngredient?: string;
  usageMethod?: string;
  supplier: string;
  purchaseDate: string;
  expiryDate?: string;
  notes: string;
}

export interface InventoryMovement {
  id: number;
  itemId: number;
  type: 'in' | 'out';
  quantity: number;
  date: string;
  reason: string;
  notes: string;
  createdAt: string;
}

export interface Employee {
  id: number;
  name: string;
  phone: string;
  position: string;
  salary: number;
  hireDate: string;
  status: 'active' | 'inactive' | 'vacation';
  notes: string;
  createdAt: string;
}

export interface Supplier {
  id: number;
  name: string;
  phone: string;
  email?: string;
  address?: string;
  materials: string[];
  paymentMethod: string;
  notes: string;
  createdAt: string;
}

export interface Advance {
  id: number;
  employeeId: number;
  employeeName: string;
  amount: number;
  date: string;
  reason: string;
  status: 'pending' | 'approved' | 'rejected' | 'paid';
  notes: string;
  createdAt: string;
}

export interface LocalSale {
  id: number;
  customerName: string;
  customerPhone: string;
  date: string;
  amount: number;
  itemType: 'iceberg' | 'romaine' | 'packaging';
  quantity: number;
  unit: string;
  notes: string;
  createdAt: string;
}

export interface IrrigationSchedule {
  id: number;
  zoneId: number;
  date: string;
  duration: number; // بالدقائق
  status: 'scheduled' | 'completed' | 'cancelled';
  notes: string;
  createdAt: string;
}

export interface Report {
  id: number;
  type: 'daily' | 'weekly' | 'monthly';
  date: string;
  data: any;
  createdAt: string;
}

// ===== أنواع الإحصائيات =====

export interface DashboardStats {
  totalZones: number;
  activeZones: number;
  harvestingZones: number;
  totalIrrigationValves: number;
  activeValves: number;
  todayIrrigations: number;
  pendingIrrigations: number;
  totalEmployees: number;
  activeEmployees: number;
  totalSales: number;
  monthlySales: number;
  lowStockItems: number;
  pendingAdvances: number;
}

// ===== أنواع النوافذ المنبثقة =====

export interface ModalProps {
  isOpen: boolean;
  onClose: () => void;
  title: React.ReactNode;
  children: React.ReactNode;
}

// ===== أنواع التنقل =====

export interface NavigationItem {
  id: string;
  name: string;
  icon: React.ComponentType<any>;
  path: string;
}

// ===== أنواع التقارير =====

export interface IrrigationReport {
  zoneId: number;
  zoneName: string;
  irrigationCount: number;
  totalDuration: number;
  lastIrrigation: string;
  nextIrrigation: string;
}

export interface SprayingReport {
  zoneId: number;
  zoneName: string;
  sprayingCount: number;
  materialsUsed: string[];
  totalQuantity: number;
  lastSpraying: string;
}

export interface FertilizationReport {
  zoneId: number;
  zoneName: string;
  fertilizationCount: number;
  fertilizersUsed: string[];
  totalQuantity: number;
  lastFertilization: string;
}

export interface InventoryReport {
  category: string;
  itemCount: number;
  totalValue: number;
  lowStockItems: number;
  expiringItems: number;
}

export interface SalesReport {
  totalSales: number;
  icebergSales: number;
  romaineSales: number;
  packagingSales: number;
  topCustomers: Array<{
    name: string;
    totalAmount: number;
    purchaseCount: number;
  }>;
}

// نوع بيانات المعاملات المالية (مصروف/إيراد)
export interface Transaction {
  id: number;
  type: 'income' | 'expense';
  amount: number;
  description: string;
  date: string;
  category?: string;
  notes?: string;
}

// ===== أنواع التحسينات الجديدة =====

// نوع الإشعارات (Toast)
export interface Toast {
  id: string;
  type: 'success' | 'error' | 'warning' | 'info';
  title: string;
  message: string;
  duration?: number;
  isVisible?: boolean;
}

// نوع خيارات البحث
export interface SearchOptions {
  searchTerm: string;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
  filterBy?: Record<string, any>;
  page?: number;
  pageSize?: number;
}

// نوع نتائج البحث
export interface SearchResult<T> {
  data: T[];
  totalCount: number;
  hasMore: boolean;
  currentPage: number;
  totalPages: number;
}

// نوع خيارات الفلترة
export interface FilterOption {
  key: string;
  label: string;
  type: 'select' | 'multiselect' | 'date' | 'daterange';
  options?: Array<{value: any; label: string}>;
}

// نوع معلومات النسخة الاحتياطية
export interface BackupInfo {
  name: string;
  createdAt: string;
  version: string;
  recordCount: number;
  size: number;
  checksum?: string;
}

// نوع خيارات النسخ الاحتياطي
export interface BackupOptions {
  includeImages?: boolean;
  compress?: boolean;
  encrypt?: boolean;
  includeTables?: string[];
}

// نوع نتيجة العملية
export interface OperationResult {
  success: boolean;
  message?: string;
  data?: any;
  error?: string;
  recordCount?: number;
}

// نوع إعدادات قاعدة البيانات
export interface DatabaseConfig {
  name: string;
  version: number;
  stores: Array<{
    name: string;
    keyPath: string;
    autoIncrement?: boolean;
    indexes?: Array<{
      name: string;
      keyPath: string | string[];
      unique?: boolean;
    }>;
  }>;
}

// نوع حالة التحميل
export interface LoadingState {
  isLoading: boolean;
  message?: string;
  progress?: number;
  error?: string;
}

// نوع إحصائيات النظام المحسنة
export interface SystemStats extends DashboardStats {
  databaseSize: number;
  lastBackup?: string;
  systemHealth: 'excellent' | 'good' | 'warning' | 'critical';
  performance: {
    avgResponseTime: number;
    memoryUsage: number;
    activeConnections: number;
  };
}