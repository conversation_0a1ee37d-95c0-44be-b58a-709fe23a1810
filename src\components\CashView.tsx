import React, { useState, useEffect } from 'react';
import {
  Plus,
  Minus,
  FileText,
  DollarSign,
  TrendingUp,
  TrendingDown,
  Edit,
  Leaf,
  BarChart3,
  X,
  Printer,
  Download,
  Calendar
} from 'lucide-react';

interface CashTransaction {
  id: number;
  type: 'income' | 'expense';
  amount: number;
  description: string;
  date: string;
  category: string;
}

interface ModalProps {
  isOpen: boolean;
  onClose: () => void;
  title: string;
  children: React.ReactNode;
}

const Modal: React.FC<ModalProps> = ({ isOpen, onClose, title, children }) => {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-2xl shadow-2xl p-6 w-full max-w-lg mx-4 max-h-[90vh] overflow-y-auto transform transition-all duration-300">
        <div className="flex justify-between items-center mb-6">
          <h3 className="text-xl font-bold text-gray-800 flex items-center gap-2">
            <div className="w-8 h-8 bg-gradient-to-br from-emerald-500 to-teal-600 rounded-xl flex items-center justify-center">
              <DollarSign size={16} className="text-white" />
            </div>
            {title}
          </h3>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors p-2 hover:bg-gray-100 rounded-lg"
          >
            <X size={20} />
          </button>
        </div>
        <div className="overflow-y-auto max-h-[calc(90vh-120px)]">
          {children}
        </div>
      </div>
    </div>
  );
};

interface CashViewProps {
  setCurrentView: (view: string) => void;
  navigationItems: any[];
  currentView: string;
}

const CashView: React.FC<CashViewProps> = ({
  setCurrentView,
  navigationItems,
  currentView
}) => {
  // دالة لتنسيق الأرقام وإزالة أخطاء الدقة العشرية
  const formatNumber = (num: number): string => {
    return Number(num.toFixed(2)).toString();
  };

  // حالات المودال
  const [isIncomeModalOpen, setIsIncomeModalOpen] = useState(false);
  const [isExpenseModalOpen, setIsExpenseModalOpen] = useState(false);
  const [isReportModalOpen, setIsReportModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  
  // حالات النموذج
  const [amount, setAmount] = useState<number | ''>('');
  const [description, setDescription] = useState('');
  const [transactionDate, setTransactionDate] = useState('');
  const [currentTransactionId, setCurrentTransactionId] = useState<number | null>(null);

  // حالات فلترة التقرير
  const [filterType, setFilterType] = useState<'all' | 'month' | 'range'>('all');
  const [selectedMonth, setSelectedMonth] = useState('');
  const [startDate, setStartDate] = useState('');
  const [endDate, setEndDate] = useState('');
  
  // تحميل المعاملات المالية من localStorage
  const loadTransactions = (): CashTransaction[] => {
    try {
      const saved = localStorage.getItem('cashTransactions');
      if (saved) {
        return JSON.parse(saved);
      }
    } catch (error) {
      console.error('خطأ في تحميل المعاملات المالية:', error);
    }

    // البيانات الافتراضية في حالة عدم وجود بيانات محفوظة
    return [
      {
        id: 1,
        type: 'income',
        amount: 5000,
        description: 'بيع منتجات للعميل أ',
        date: '15/01/2025',
        category: 'مبيعات'
      },
      {
        id: 2,
        type: 'expense',
        amount: 1200,
        description: 'شراء مستلزمات',
        date: '14/01/2025',
        category: 'مشتريات'
      },
      {
        id: 3,
        type: 'income',
        amount: 3500,
        description: 'بيع منتجات للعميل ب',
        date: '13/01/2025',
        category: 'مبيعات'
      },
      {
        id: 4,
        type: 'expense',
        amount: 800,
        description: 'صيانة معدات',
        date: '12/01/2025',
        category: 'صيانة'
      }
    ];
  };

  const [transactions, setTransactions] = useState<CashTransaction[]>(loadTransactions());

  // حفظ المعاملات في localStorage
  const saveTransactions = (newTransactions: CashTransaction[]) => {
    try {
      localStorage.setItem('cashTransactions', JSON.stringify(newTransactions));
      setTransactions(newTransactions);
    } catch (error) {
      console.error('خطأ في حفظ المعاملات المالية:', error);
    }
  };

  // حفظ البيانات تلقائياً عند تغيير المعاملات
  useEffect(() => {
    try {
      localStorage.setItem('cashTransactions', JSON.stringify(transactions));
    } catch (error) {
      console.error('خطأ في حفظ المعاملات المالية:', error);
    }
  }, [transactions]);

  // دالة فلترة المعاملات حسب التاريخ
  const getFilteredTransactions = () => {
    if (filterType === 'all') {
      return transactions;
    }

    return transactions.filter(transaction => {
      // تحويل تاريخ المعاملة من dd/mm/yyyy إلى Date object
      const [day, month, year] = transaction.date.split('/').map(Number);
      const transactionDate = new Date(year, month - 1, day);

      if (filterType === 'month' && selectedMonth) {
        const [selectedYear, selectedMonthNum] = selectedMonth.split('-').map(Number);
        return transactionDate.getFullYear() === selectedYear &&
               transactionDate.getMonth() === selectedMonthNum - 1;
      }

      if (filterType === 'range' && startDate && endDate) {
        const start = new Date(startDate);
        const end = new Date(endDate);
        return transactionDate >= start && transactionDate <= end;
      }

      return true;
    });
  };

  // دالة طباعة التقرير المالي
  const handlePrintReport = () => {
    try {
      // حساب الإحصائيات من المعاملات المفلترة
      const filteredTransactions = getFilteredTransactions();
      const totalIncome = filteredTransactions.filter(t => t.type === 'income').reduce((sum, t) => sum + t.amount, 0);
      const totalExpense = filteredTransactions.filter(t => t.type === 'expense').reduce((sum, t) => sum + t.amount, 0);
      const balance = totalIncome - totalExpense;

      // إضافة CSS للطباعة
      const printStyles = `
        <style>
          @page {
            size: A4;
            margin: 1.5cm;
          }

          body {
            font-family: 'Cairo', 'Tajawal', Arial, sans-serif;
            direction: rtl;
            text-align: right;
            line-height: 1.4;
            color: #333;
            background: white;
            padding: 0;
            margin: 0;
            width: 21cm;
            min-height: 29.7cm;
            max-width: 21cm;
          }

          .page-container {
            width: 100%;
            max-width: 21cm;
            min-height: 29.7cm;
            padding: 1cm 0.8cm 1cm 1.5cm;
            margin: 0 auto;
            background: white;
            box-sizing: border-box;
          }

          .print-area {
            font-family: 'Cairo', 'Tajawal', Arial, sans-serif;
            direction: rtl;
            color: #000;
            width: 100%;
          }

          @media print {
            body {
              padding: 0;
              margin: 0;
            }

            .page-container {
              padding: 0.8cm 0.5cm 0.8cm 1cm;
              margin: 0;
              width: 100%;
              max-width: none;
            }
          }
          .print-header {
            text-align: center;
            margin-bottom: 20px;
            border-bottom: 2px solid #22c55e;
            padding-bottom: 10px;
          }
          .print-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 5px;
            color: #15803d;
          }
          .print-subtitle {
            font-size: 14px;
            color: #16a34a;
          }
          .print-summary {
            margin: 20px 0;
            border: 1px solid #e5e7eb;
            border-radius: 5px;
            overflow: hidden;
          }
          .print-item {
            display: flex;
            justify-content: space-between;
            padding: 12px 15px;
            border-bottom: 1px solid #e5e7eb;
            font-size: 14px;
            background: #f9fafb;
          }
          .print-item:last-child {
            border-bottom: none;
          }
          .print-item.income { color: #22c55e; }
          .print-item.expense { color: #ef4444; }
          .print-item.balance {
            font-weight: bold;
            color: ${balance >= 0 ? '#22c55e' : '#ef4444'};
            border-bottom: 2px solid #000;
          }
          .print-footer {
            margin-top: 30px;
            text-align: center;
            font-size: 10px;
            color: #6b7280;
            border-top: 1px solid #22c55e;
            padding-top: 15px;
          }
        </style>
      `;

      // إنشاء محتوى الطباعة
      const printContent = `
        <div class="page-container">
          <div class="print-area">
            <div class="print-header">
              <div class="print-title">🌱 شركة الشفق للزراعة الحديثة 🌱</div>
              <div class="print-subtitle">💰 التقرير المالي</div>
            </div>

          <div class="print-summary">
            <div class="print-item income">
              <span>إجمالي الدخل:</span>
              <span>${formatNumber(totalIncome)} دينار أردني</span>
            </div>
            <div class="print-item expense">
              <span>إجمالي المصروفات:</span>
              <span>${formatNumber(totalExpense)} دينار أردني</span>
            </div>
            <div class="print-item balance">
              <span>الرصيد النهائي:</span>
              <span>${formatNumber(balance)} دينار أردني</span>
            </div>
          </div>

            <div class="print-footer">
              <p>تم إنشاء هذا التقرير بواسطة نظام إدارة المخزون - شركة الشفق للزراعة الحديثة</p>
              <p>📧 البريد الإلكتروني: <EMAIL> | 📞 الهاتف: 00962793606169</p>
              <p>تصميم: شادي أبوجيش</p>
              <p style="margin-top: 10px; font-style: italic;">"نحو زراعة مستدامة ومستقبل أخضر" 🌱</p>
            </div>
          </div>
        </div>
      `;

      // إضافة المحتوى إلى الصفحة مؤقتاً
      const existingPrintArea = document.querySelector('.print-area');
      if (existingPrintArea) {
        existingPrintArea.remove();
      }

      // إضافة الـ CSS
      const existingStyles = document.querySelector('#print-styles');
      if (existingStyles) {
        existingStyles.remove();
      }

      const styleElement = document.createElement('style');
      styleElement.id = 'print-styles';
      styleElement.innerHTML = printStyles;
      document.head.appendChild(styleElement);

      // إضافة المحتوى
      const printDiv = document.createElement('div');
      printDiv.innerHTML = printContent;
      document.body.appendChild(printDiv);

      // إنشاء iframe للطباعة لتجنب التكرار
      const printIframe = document.createElement('iframe');
      printIframe.style.position = 'absolute';
      printIframe.style.top = '-9999px';
      printIframe.style.left = '-9999px';
      printIframe.style.width = '0';
      printIframe.style.height = '0';
      printIframe.style.border = '0';
      document.body.appendChild(printIframe);

      // إنشاء صفحة HTML كاملة للطباعة
      const fullPrintContent = `
        <!DOCTYPE html>
        <html lang="ar" dir="rtl">
        <head>
          <meta charset="UTF-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>التقرير المالي - شركة الشفق</title>
          ${printStyles}
        </head>
        <body>
          ${printContent}
        </body>
        </html>
      `;

      // كتابة المحتوى في iframe
      const iframeDoc = printIframe.contentDocument || printIframe.contentWindow?.document;
      if (iframeDoc) {
        iframeDoc.open();
        iframeDoc.write(fullPrintContent);
        iframeDoc.close();

        // طباعة واحدة فقط
        printIframe.onload = () => {
          try {
            console.log('📄 تم تحميل التقرير المالي، بدء الطباعة...');
            printIframe.contentWindow?.focus();
            printIframe.contentWindow?.print();
            console.log('✅ تم إرسال أمر طباعة التقرير المالي');

            // إزالة iframe بعد الطباعة
            setTimeout(() => {
              if (document.body.contains(printIframe)) {
                document.body.removeChild(printIframe);
                console.log('🗑️ تم إزالة iframe التقرير المالي');
              }
            }, 2000);
          } catch (error) {
            console.error('خطأ في الطباعة:', error);
            if (document.body.contains(printIframe)) {
              document.body.removeChild(printIframe);
            }
          }
        };
      }

      // إزالة المحتوى المؤقت
      if (printDiv) {
        printDiv.remove();
      }
      if (styleElement) {
        styleElement.remove();
      }

    } catch (error) {
      console.error('خطأ في الطباعة:', error);
      alert('حدث خطأ أثناء الطباعة. يرجى المحاولة مرة أخرى.');
    }
  };

  // دالة تصدير التقرير المالي
  const handleExportReport = async () => {
    try {
      alert('جاري إنشاء التقرير المالي، يرجى الانتظار...');

      // استيراد المكتبة
      const { createFinancialHTML } = await import('../utils/simplePDF');

      // الحصول على المعاملات المفلترة
      const filteredTransactions = getFilteredTransactions();

      // إنشاء اسم الملف بالتاريخ الميلادي
      const now = new Date();
      const currentDate = `${now.getDate().toString().padStart(2, '0')}-${(now.getMonth() + 1).toString().padStart(2, '0')}-${now.getFullYear()}`;
      const hours = now.getHours();
      const minutes = now.getMinutes();
      const ampm = hours >= 12 ? 'PM' : 'AM';
      const displayHours = hours % 12 || 12;
      const currentTime = `${displayHours.toString().padStart(2, '0')}-${minutes.toString().padStart(2, '0')}-${ampm}`;
      const filename = `التقرير-المالي-${currentDate}-${currentTime}.html`;

      // إنشاء التقرير
      await createFinancialHTML(filteredTransactions, filename);

      alert('✅ تم تصدير التقرير المالي كملف HTML بنجاح!\n📁 تم حفظ الملف في مجلد التحميلات\n🌐 يمكنك فتحه في المتصفح أو طباعته\n📄 نوع الملف: HTML (وليس JSON)');
    } catch (error) {
      console.error('خطأ في تصدير التقرير:', error);
      alert('❌ حدث خطأ أثناء تصدير التقرير');
    }
  };

  // وظائف إدارة المعاملات
  const handleAddTransaction = (type: 'income' | 'expense') => {
    if (amount === '' || description.trim() === '' || transactionDate.trim() === '') {
      alert('يرجى ملء جميع الحقول المطلوبة');
      return;
    }

    // تحويل التاريخ من yyyy-mm-dd إلى dd/mm/yyyy (ميلادي)
    const formatDate = (dateString: string) => {
      const date = new Date(dateString);
      const day = date.getDate().toString().padStart(2, '0');
      const month = (date.getMonth() + 1).toString().padStart(2, '0');
      const year = date.getFullYear();
      return `${day}/${month}/${year}`;
    };

    const newTransaction: CashTransaction = {
      id: Date.now(),
      type,
      amount: Number(amount),
      description: description.trim(),
      category: type === 'income' ? 'دخل' : 'مصروف',
      date: formatDate(transactionDate)
    };

    const updatedTransactions = [newTransaction, ...transactions];
    saveTransactions(updatedTransactions);
    resetForm();
    
    if (type === 'income') {
      setIsIncomeModalOpen(false);
    } else {
      setIsExpenseModalOpen(false);
    }
  };

  const handleEditTransaction = () => {
    if (currentTransactionId === null || amount === '' || description.trim() === '') {
      alert('يرجى ملء جميع الحقول المطلوبة');
      return;
    }

    const updatedTransactions = transactions.map(t => {
      if (t.id === currentTransactionId) {
        return {
          ...t,
          amount: Number(amount),
          description: description.trim()
        };
      }
      return t;
    });

    saveTransactions(updatedTransactions);
    resetForm();
    setIsEditModalOpen(false);
  };

  const handleDeleteTransaction = (id: number) => {
    if (window.confirm('هل أنت متأكد من حذف هذه المعاملة؟')) {
      const updatedTransactions = transactions.filter(t => t.id !== id);
      saveTransactions(updatedTransactions);
    }
  };

  const openEditModal = (transaction: CashTransaction) => {
    setCurrentTransactionId(transaction.id);
    setAmount(transaction.amount);
    setDescription(transaction.description);
    setIsEditModalOpen(true);
  };

  const resetForm = () => {
    setAmount('');
    setDescription('');
    setTransactionDate('');
    setCurrentTransactionId(null);
  };

  // حساب الإحصائيات
  const totalIncome = transactions
    .filter(t => t.type === 'income')
    .reduce((sum, t) => sum + t.amount, 0);
  
  const totalExpenses = transactions
    .filter(t => t.type === 'expense')
    .reduce((sum, t) => sum + t.amount, 0);
  
  const balance = totalIncome - totalExpenses;

  return (
    <div className="min-h-screen w-full bg-gradient-to-br from-emerald-50 via-teal-50 to-green-50 py-8 px-2 md:px-8" dir="rtl">
      {/* Header */}
      <div className="bg-gradient-to-r from-green-600 via-green-500 to-emerald-500 text-white p-6 shadow-xl rounded-2xl mb-4">
        <div className="container mx-auto">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <div className="w-12 h-12 bg-white/20 rounded-2xl flex items-center justify-center backdrop-blur-sm">
                <Leaf size={24} className="text-white" />
              </div>
              <div>
                <h1 className="text-2xl font-bold font-arabic tracking-wide drop-shadow">شركة الشفق للزراعة الحديثة</h1>
                <p className="text-green-100 text-sm font-medium tracking-wider">Al-Shafaq Modern Agriculture Company</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Navigation */}
      <div className="bg-gradient-to-r from-green-500 to-emerald-500 p-4 shadow-lg rounded-2xl mb-6">
        <div className="container mx-auto">
          <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 xl:grid-cols-9 gap-3">
            {navigationItems.map((item) => {
              const IconComponent = item.icon;
              return (
                <button
                  key={item.id}
                  onClick={() => setCurrentView(item.id)}
                  className={`p-4 rounded-2xl transition-all duration-300 transform hover:scale-105 hover:shadow-lg font-arabic font-bold text-green-600 border-2 ${
                    currentView === item.id
                      ? 'bg-white shadow-xl border-green-500'
                      : 'bg-white hover:bg-gray-50 shadow-md border-transparent'
                  }`}
                >
                  <IconComponent size={24} className="mx-auto mb-2 text-green-600" />
                  <span className="text-sm block">{item.name}</span>
                </button>
              );
            })}
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="container mx-auto p-0 md:p-6">
        {/* Page Header */}
        <div className="flex items-center justify-between mb-8">
          <div className="flex items-center gap-4">
            <div className="w-12 h-12 bg-gradient-to-br from-green-500 to-emerald-600 rounded-2xl flex items-center justify-center shadow-lg">
              <DollarSign size={24} className="text-white" />
            </div>
            <h2 className="text-3xl font-bold font-arabic text-emerald-700 tracking-wide drop-shadow">إدارة الكاش</h2>
          </div>
          
          {/* Action Buttons */}
          <div className="flex gap-3">
            <button
              onClick={() => {
                resetForm();
                setIsIncomeModalOpen(true);
              }}
              className="bg-gradient-to-r from-green-500 to-green-600 text-white px-6 py-3 rounded-xl hover:from-green-600 hover:to-green-700 transition-all duration-300 flex items-center gap-2 shadow-lg hover:shadow-xl transform hover:scale-105 font-arabic font-medium tracking-wide"
            >
              <Plus size={20} />
              إضافة دخل
            </button>
            <button
              onClick={() => {
                resetForm();
                setIsExpenseModalOpen(true);
              }}
              className="bg-gradient-to-r from-red-500 to-red-600 text-white px-6 py-3 rounded-xl hover:from-red-600 hover:to-red-700 transition-all duration-300 flex items-center gap-2 shadow-lg hover:shadow-xl transform hover:scale-105 font-arabic font-medium tracking-wide"
            >
              <Minus size={20} />
              إضافة مصروف
            </button>
            <button
              onClick={() => setIsReportModalOpen(true)}
              className="bg-gradient-to-r from-blue-500 to-blue-600 text-white px-6 py-3 rounded-xl hover:from-blue-600 hover:to-blue-700 transition-all duration-300 flex items-center gap-2 shadow-lg hover:shadow-xl transform hover:scale-105 font-arabic font-medium tracking-wide"
            >
              <FileText size={20} />
              تقرير مالي
            </button>
          </div>
        </div>

        {/* Statistics Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <div className="bg-white rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 border border-green-100">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-green-700 text-sm font-bold mb-2 font-arabic tracking-wide">الرصيد الحالي</h3>
                <p className="text-3xl font-bold text-green-600 font-arabic">{formatNumber(balance)} دينار</p>
              </div>
              <div className="w-12 h-12 bg-gradient-to-br from-green-500 to-green-600 rounded-2xl flex items-center justify-center">
                <DollarSign size={24} className="text-white" />
              </div>
            </div>
          </div>

          <div className="bg-white rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 border border-emerald-100">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-emerald-700 text-sm font-bold mb-2 font-arabic tracking-wide">إجمالي الدخل</h3>
                <p className="text-3xl font-bold text-emerald-600 font-arabic">{formatNumber(totalIncome)} دينار</p>
              </div>
              <div className="w-12 h-12 bg-gradient-to-br from-emerald-500 to-emerald-600 rounded-2xl flex items-center justify-center">
                <TrendingUp size={24} className="text-white" />
              </div>
            </div>
          </div>

          <div className="bg-white rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 border border-red-100">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-red-700 text-sm font-bold mb-2 font-arabic tracking-wide">إجمالي المصروفات</h3>
                <p className="text-3xl font-bold text-red-600 font-arabic">{formatNumber(totalExpenses)} دينار</p>
              </div>
              <div className="w-12 h-12 bg-gradient-to-br from-red-500 to-red-600 rounded-2xl flex items-center justify-center">
                <TrendingDown size={24} className="text-white" />
              </div>
            </div>
          </div>

          <div className="bg-white rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 border border-blue-100">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-blue-700 text-sm font-bold mb-2 font-arabic tracking-wide">عدد المعاملات</h3>
                <p className="text-3xl font-bold text-blue-600 font-arabic">{transactions.length}</p>
              </div>
              <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-2xl flex items-center justify-center">
                <BarChart3 size={24} className="text-white" />
              </div>
            </div>
          </div>
        </div>

        {/* Transactions Table */}
        <div className="bg-white rounded-2xl shadow-xl border border-green-100 overflow-hidden">
          <div className="p-6 bg-gradient-to-r from-green-500 to-emerald-500 text-white">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <DollarSign size={24} />
                <h3 className="text-xl font-bold font-arabic drop-shadow">سجل المعاملات المالية</h3>
              </div>
              <div className="text-sm bg-white/20 px-3 py-1 rounded-lg backdrop-blur-sm">
                {(() => {
                  const now = new Date();
                  const day = now.getDate().toString().padStart(2, '0');
                  const month = (now.getMonth() + 1).toString().padStart(2, '0');
                  const year = now.getFullYear();
                  const weekdays = ['الأحد', 'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت'];
                  const months = ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو', 'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'];
                  return `${weekdays[now.getDay()]}، ${day} ${months[now.getMonth()]} ${year}`;
                })()}
              </div>
            </div>
          </div>
          
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-4 text-right text-sm font-medium text-gray-500 tracking-wider">#</th>
                  <th className="px-6 py-4 text-right text-sm font-medium text-gray-500 tracking-wider">النوع</th>
                  <th className="px-6 py-4 text-right text-sm font-medium text-gray-500 tracking-wider">المبلغ</th>
                  <th className="px-6 py-4 text-right text-sm font-medium text-gray-500 tracking-wider">الوصف</th>
                  <th className="px-6 py-4 text-right text-sm font-medium text-gray-500 tracking-wider">التاريخ</th>
                  <th className="px-6 py-4 text-right text-sm font-medium text-gray-500 tracking-wider">إجراءات</th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-200">
                {transactions.map((transaction, index) => (
                  <tr key={transaction.id} className={index % 2 === 0 ? 'bg-white' : 'bg-gray-50'}>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{index + 1}</td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                        transaction.type === 'income'
                          ? 'bg-green-100 text-green-800'
                          : 'bg-red-100 text-red-800'
                      }`}>
                        {transaction.type === 'income' ? 'دخل' : 'مصروف'}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className={`font-medium ${
                        transaction.type === 'income'
                          ? 'text-green-600'
                          : 'text-red-600'
                      }`}>
                        {formatNumber(transaction.amount)} دينار
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{transaction.description}</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{transaction.date}</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <div className="flex space-x-2">
                        <button
                          onClick={() => openEditModal(transaction)}
                          className="text-green-600 hover:text-green-900 bg-green-100 hover:bg-green-200 p-2 rounded-lg transition-colors"
                        >
                          <Edit size={16} />
                        </button>
                        <button
                          onClick={() => handleDeleteTransaction(transaction.id)}
                          className="text-red-600 hover:text-red-900 bg-red-100 hover:bg-red-200 p-2 rounded-lg transition-colors mr-2"
                        >
                          <X size={16} />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </div>

      {/* Income Modal */}
      <Modal isOpen={isIncomeModalOpen} onClose={() => setIsIncomeModalOpen(false)} title="إضافة دخل جديد">
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">المبلغ (دينار)</label>
            <input
              type="number"
              value={amount}
              onChange={(e) => setAmount(e.target.value === '' ? '' : Number(e.target.value))}
              className="w-full p-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-all duration-300"
              placeholder="أدخل المبلغ"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">الوصف</label>
            <input
              type="text"
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              className="w-full p-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-all duration-300"
              placeholder="أدخل وصفًا للدخل"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">التاريخ</label>
            <div className="relative">
              <input
                type="date"
                value={transactionDate}
                onChange={(e) => setTransactionDate(e.target.value)}
                className="w-full p-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-all duration-300"
              />
              <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                <Calendar size={18} className="text-gray-400" />
              </div>
            </div>
          </div>
          <div className="flex gap-3 mt-6">
            <button
              onClick={() => handleAddTransaction('income')}
              className="flex-1 bg-gradient-to-r from-green-500 to-green-600 text-white py-3 rounded-xl hover:from-green-600 hover:to-green-700 transition-all duration-300 flex items-center justify-center gap-2"
            >
              <Plus size={18} />
              إضافة
            </button>
            <button
              onClick={() => setIsIncomeModalOpen(false)}
              className="flex-1 bg-gray-500 text-white py-3 rounded-xl hover:bg-gray-600 transition-all duration-300"
            >
              إلغاء
            </button>
          </div>
        </div>
      </Modal>

      {/* Expense Modal */}
      <Modal isOpen={isExpenseModalOpen} onClose={() => setIsExpenseModalOpen(false)} title="إضافة مصروف جديد">
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">المبلغ (دينار)</label>
            <input
              type="number"
              value={amount}
              onChange={(e) => setAmount(e.target.value === '' ? '' : Number(e.target.value))}
              className="w-full p-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-red-500 focus:border-red-500 transition-all duration-300"
              placeholder="أدخل المبلغ"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">الوصف</label>
            <input
              type="text"
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              className="w-full p-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-red-500 focus:border-red-500 transition-all duration-300"
              placeholder="أدخل وصفًا للمصروف"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">التاريخ</label>
            <div className="relative">
              <input
                type="date"
                value={transactionDate}
                onChange={(e) => setTransactionDate(e.target.value)}
                className="w-full p-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-red-500 focus:border-red-500 transition-all duration-300"
              />
              <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                <Calendar size={18} className="text-gray-400" />
              </div>
            </div>
          </div>
          <div className="flex gap-3 mt-6">
            <button
              onClick={() => handleAddTransaction('expense')}
              className="flex-1 bg-gradient-to-r from-red-500 to-red-600 text-white py-3 rounded-xl hover:from-red-600 hover:to-red-700 transition-all duration-300 flex items-center justify-center gap-2"
            >
              <Minus size={18} />
              إضافة
            </button>
            <button
              onClick={() => setIsExpenseModalOpen(false)}
              className="flex-1 bg-gray-500 text-white py-3 rounded-xl hover:bg-gray-600 transition-all duration-300"
            >
              إلغاء
            </button>
          </div>
        </div>
      </Modal>

      {/* Edit Transaction Modal */}
      <Modal isOpen={isEditModalOpen} onClose={() => setIsEditModalOpen(false)} title="تعديل معاملة">
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">المبلغ (دينار)</label>
            <input
              type="number"
              value={amount}
              onChange={(e) => setAmount(e.target.value === '' ? '' : Number(e.target.value))}
              className="w-full p-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-300"
              placeholder="أدخل المبلغ"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">الوصف</label>
            <input
              type="text"
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              className="w-full p-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-300"
              placeholder="أدخل وصفًا للمعاملة"
            />
          </div>
          <div className="flex gap-3 mt-6">
            <button
              onClick={handleEditTransaction}
              className="flex-1 bg-gradient-to-r from-blue-500 to-blue-600 text-white py-3 rounded-xl hover:from-blue-600 hover:to-blue-700 transition-all duration-300 flex items-center justify-center gap-2"
            >
              <Edit size={18} />
              حفظ التغييرات
            </button>
            <button
              onClick={() => setIsEditModalOpen(false)}
              className="flex-1 bg-gray-500 text-white py-3 rounded-xl hover:bg-gray-600 transition-all duration-300"
            >
              إلغاء
            </button>
          </div>
        </div>
      </Modal>

      {/* Report Modal */}
      <Modal isOpen={isReportModalOpen} onClose={() => setIsReportModalOpen(false)} title="تقرير مالي">
        <div id="financial-report" className="space-y-6">
          {/* Filter Section */}
          <div className="bg-blue-50 p-4 rounded-xl border border-blue-200">
            <h4 className="text-lg font-bold text-blue-800 mb-3">فلترة التقرير</h4>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">نوع الفلترة</label>
                <div className="flex gap-4">
                  <label className="flex items-center">
                    <input
                      type="radio"
                      name="filterType"
                      value="all"
                      checked={filterType === 'all'}
                      onChange={(e) => setFilterType(e.target.value as 'all' | 'month' | 'range')}
                      className="mr-2"
                    />
                    جميع السجلات
                  </label>
                  <label className="flex items-center">
                    <input
                      type="radio"
                      name="filterType"
                      value="month"
                      checked={filterType === 'month'}
                      onChange={(e) => setFilterType(e.target.value as 'all' | 'month' | 'range')}
                      className="mr-2"
                    />
                    شهر محدد
                  </label>
                  <label className="flex items-center">
                    <input
                      type="radio"
                      name="filterType"
                      value="range"
                      checked={filterType === 'range'}
                      onChange={(e) => setFilterType(e.target.value as 'all' | 'month' | 'range')}
                      className="mr-2"
                    />
                    فترة زمنية
                  </label>
                </div>
              </div>

              {filterType === 'month' && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">اختر الشهر</label>
                  <input
                    type="month"
                    value={selectedMonth}
                    onChange={(e) => setSelectedMonth(e.target.value)}
                    className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
              )}

              {filterType === 'range' && (
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">من تاريخ</label>
                    <input
                      type="date"
                      value={startDate}
                      onChange={(e) => setStartDate(e.target.value)}
                      className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">إلى تاريخ</label>
                    <input
                      type="date"
                      value={endDate}
                      onChange={(e) => setEndDate(e.target.value)}
                      className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    />
                  </div>
                </div>
              )}
            </div>
          </div>

          {(() => {
            // حساب الإحصائيات من المعاملات المفلترة
            const filteredTransactions = getFilteredTransactions();
            const filteredTotalIncome = filteredTransactions.filter(t => t.type === 'income').reduce((sum, t) => sum + t.amount, 0);
            const filteredTotalExpenses = filteredTransactions.filter(t => t.type === 'expense').reduce((sum, t) => sum + t.amount, 0);
            const filteredBalance = filteredTotalIncome - filteredTotalExpenses;

            return (
              <div className="bg-gray-50 p-4 rounded-xl">
                <h4 className="text-lg font-bold text-gray-800 mb-3">ملخص الحالة المالية</h4>
                {filterType !== 'all' && (
                  <div className="mb-3 text-sm text-blue-600 bg-blue-100 p-2 rounded-lg">
                    {filterType === 'month' && selectedMonth && (() => {
                      const date = new Date(selectedMonth + '-01');
                      const months = ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو', 'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'];
                      return `التقرير لشهر: ${months[date.getMonth()]} ${date.getFullYear()}`;
                    })()}
                    {filterType === 'range' && startDate && endDate && (() => {
                      const start = new Date(startDate);
                      const end = new Date(endDate);
                      const formatDate = (date: Date) => {
                        const day = date.getDate().toString().padStart(2, '0');
                        const month = (date.getMonth() + 1).toString().padStart(2, '0');
                        const year = date.getFullYear();
                        return `${day}/${month}/${year}`;
                      };
                      return `التقرير من ${formatDate(start)} إلى ${formatDate(end)}`;
                    })()}
                  </div>
                )}
                <div className="grid grid-cols-3 gap-4">
                  <div className="bg-white p-3 rounded-lg shadow-sm border border-green-100">
                    <p className="text-sm text-gray-500 mb-1">الرصيد</p>
                    <p className={`text-xl font-bold ${filteredBalance >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                      {formatNumber(filteredBalance)} دينار
                    </p>
                  </div>
                  <div className="bg-white p-3 rounded-lg shadow-sm border border-green-100">
                    <p className="text-sm text-gray-500 mb-1">الدخل</p>
                    <p className="text-xl font-bold text-green-600">{formatNumber(filteredTotalIncome)} دينار</p>
                  </div>
                  <div className="bg-white p-3 rounded-lg shadow-sm border border-green-100">
                    <p className="text-sm text-gray-500 mb-1">المصروفات</p>
                    <p className="text-xl font-bold text-red-600">{formatNumber(filteredTotalExpenses)} دينار</p>
                  </div>
                </div>
              </div>
            );
          })()}

          {(() => {
            // حساب إحصائيات التصنيفات من المعاملات المفلترة
            const filteredTransactions = getFilteredTransactions();
            const filteredTotalIncome = filteredTransactions.filter(t => t.type === 'income').reduce((sum, t) => sum + t.amount, 0);
            const filteredTotalExpenses = filteredTransactions.filter(t => t.type === 'expense').reduce((sum, t) => sum + t.amount, 0);

            return (
              <div>
                <h4 className="text-lg font-bold text-gray-800 mb-3">إحصائيات التصنيفات</h4>
                <div className="space-y-2">
                  {/* Income Categories */}
                  <div className="mb-4">
                    <h5 className="text-sm font-medium text-gray-600 mb-2">تصنيفات الدخل</h5>
                    {Array.from(new Set(filteredTransactions.filter(t => t.type === 'income').map(t => t.category))).map(category => {
                      const categoryTotal = filteredTransactions
                        .filter(t => t.type === 'income' && t.category === category)
                        .reduce((sum, t) => sum + t.amount, 0);
                      const percentage = Math.round((categoryTotal / filteredTotalIncome) * 100) || 0;
                  
                  return (
                    <div key={`income-${category}`} className="mb-2">
                      <div className="flex justify-between text-sm mb-1">
                        <span>{category}</span>
                        <span className="font-medium">{categoryTotal} دينار ({percentage}%)</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div 
                          className="bg-green-500 h-2 rounded-full" 
                          style={{ width: `${percentage}%` }}
                        ></div>
                      </div>
                    </div>
                  );
                })}
              </div>

                  {/* Expense Categories */}
                  <div>
                    <h5 className="text-sm font-medium text-gray-600 mb-2">تصنيفات المصروفات</h5>
                    {Array.from(new Set(filteredTransactions.filter(t => t.type === 'expense').map(t => t.category))).map(category => {
                      const categoryTotal = filteredTransactions
                        .filter(t => t.type === 'expense' && t.category === category)
                        .reduce((sum, t) => sum + t.amount, 0);
                      const percentage = Math.round((categoryTotal / filteredTotalExpenses) * 100) || 0;

                      return (
                        <div key={`expense-${category}`} className="mb-2">
                          <div className="flex justify-between text-sm mb-1">
                            <span>{category}</span>
                            <span className="font-medium">{categoryTotal} دينار ({percentage}%)</span>
                          </div>
                          <div className="w-full bg-gray-200 rounded-full h-2">
                            <div
                              className="bg-red-500 h-2 rounded-full"
                              style={{ width: `${percentage}%` }}
                            ></div>
                          </div>
                        </div>
                      );
                    })}
                  </div>
                </div>
              </div>
            );
          })()}

          <div className="flex justify-between items-center mt-6">
            <div className="flex gap-3">
              <button
                onClick={handlePrintReport}
                className="bg-gradient-to-r from-blue-500 to-blue-600 text-white px-4 py-2 rounded-xl hover:from-blue-600 hover:to-blue-700 transition-all duration-300 flex items-center gap-2 shadow-lg hover:shadow-xl transform hover:scale-105"
              >
                <Printer size={16} />
                طباعة
              </button>
              <button
                onClick={handleExportReport}
                className="bg-gradient-to-r from-blue-500 to-blue-600 text-white px-4 py-2 rounded-xl hover:from-blue-600 hover:to-blue-700 transition-all duration-300 flex items-center gap-2 shadow-lg hover:shadow-xl transform hover:scale-105"
              >
                <Download size={16} />
                تصدير HTML
              </button>
            </div>
            <button
              onClick={() => setIsReportModalOpen(false)}
              className="bg-gray-500 text-white px-6 py-2 rounded-xl hover:bg-gray-600 transition-all duration-300"
            >
              إغلاق
            </button>
          </div>
        </div>
      </Modal>
    </div>
  );
};

export default CashView;