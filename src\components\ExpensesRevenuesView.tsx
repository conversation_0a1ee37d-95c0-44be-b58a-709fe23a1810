import React, { useEffect, useState } from 'react';
import { getTransactions, addTransaction, updateTransaction, deleteTransaction } from '../utils/database';
import { Transaction } from '../types';
import Modal from './Modal';
import { Plus, TrendingUp, TrendingDown, Filter, Edit, Trash2, Refresh<PERSON><PERSON>, Pie<PERSON>hart } from 'lucide-react';
import { Pie } from 'react-chartjs-2';
import { Chart, ArcElement, Tooltip, Legend } from 'chart.js';
import * as XLSX from 'xlsx';
import { saveAs } from 'file-saver';
import { jsPDF } from 'jspdf';
import 'jspdf-autotable';
Chart.register(ArcElement, Tooltip, Legend);

const formatCurrency = (amount: number) =>
  new Intl.NumberFormat('ar-JO', { style: 'currency', currency: 'JOD' }).format(amount);

const formatDate = (date: string) => {
  if (!date) return '-';
  const d = new Date(date);
  return d.toLocaleDateString('en-GB');
};

const ExpensesRevenuesView: React.FC = () => {
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [editTransaction, setEditTransaction] = useState<Transaction | null>(null);
  const [filterType, setFilterType] = useState<'all' | 'income' | 'expense'>('all');
  const [search, setSearch] = useState('');

  const [form, setForm] = useState<Omit<Transaction, 'id'>>({
    type: 'expense',
    amount: 0,
    description: '',
    date: new Date().toISOString().split('T')[0],
    category: '',
    notes: ''
  });

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    setIsLoading(true);
    const data = await getTransactions();
    setTransactions(data);
    setIsLoading(false);
  };

  const handleSave = async () => {
    if (editTransaction) {
      const result = await updateTransaction({ ...editTransaction, ...form });
      if (result.success) {
        setIsModalOpen(false);
        setEditTransaction(null);
        await loadData();
      } else {
        alert(result.error);
      }
    } else {
      const result = await addTransaction(form);
      if (result.success) {
        setIsModalOpen(false);
        setForm({ type: 'expense', amount: 0, description: '', date: new Date().toISOString().split('T')[0], category: '', notes: '' });
        await loadData();
      } else {
        alert(result.error);
      }
    }
  };

  const handleEdit = (t: Transaction) => {
    setEditTransaction(t);
    setForm({ ...t });
    setIsModalOpen(true);
  };

  const handleDelete = async (id: number) => {
    if (!window.confirm('هل أنت متأكد من حذف هذه المعاملة؟')) return;
    const result = await deleteTransaction(id);
    if (result.success) await loadData();
    else alert(result.error);
  };

  // تصفية
  const filtered = transactions.filter(t =>
    (filterType === 'all' || t.type === filterType) &&
    (search === '' || t.description.includes(search) || t.category?.includes(search) || t.notes?.includes(search))
  );

  // إحصائيات
  const totalIncome = filtered.filter(t => t.type === 'income').reduce((sum, t) => sum + t.amount, 0);
  const totalExpense = filtered.filter(t => t.type === 'expense').reduce((sum, t) => sum + t.amount, 0);
  const balance = totalIncome - totalExpense;

  // بيانات الرسم البياني
  const expenseCategories = Array.from(new Set(filtered.filter(t => t.type === 'expense').map(t => t.category || 'غير مصنف')));
  const expenseData = expenseCategories.map(cat => filtered.filter(t => t.type === 'expense' && (t.category || 'غير مصنف') === cat).reduce((sum, t) => sum + t.amount, 0));
  const incomeCategories = Array.from(new Set(filtered.filter(t => t.type === 'income').map(t => t.category || 'غير مصنف')));
  const incomeData = incomeCategories.map(cat => filtered.filter(t => t.type === 'income' && (t.category || 'غير مصنف') === cat).reduce((sum, t) => sum + t.amount, 0));

  const pieColors = [
    '#059669', '#10b981', '#34d399', '#6ee7b7', '#f59e42', '#fbbf24', '#f87171', '#ef4444', '#6366f1', '#3b82f6', '#f472b6', '#a78bfa', '#facc15', '#eab308', '#f43f5e'
  ];

  // تصدير إلى Excel
  const handleExportExcel = () => {
    const ws = XLSX.utils.json_to_sheet(filtered.map(t => ({
      النوع: t.type === 'income' ? 'إيراد' : 'مصروف',
      المبلغ: t.amount,
      الوصف: t.description,
      التصنيف: t.category,
      التاريخ: formatDate(t.date),
      الملاحظات: t.notes
    })));
    const wb = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(wb, ws, 'المصروفات والإيرادات');
    const excelBuffer = XLSX.write(wb, { bookType: 'xlsx', type: 'array' });
    saveAs(new Blob([excelBuffer], { type: 'application/octet-stream' }), 'المصروفات-الإيرادات.xlsx');
  };

  // تصدير إلى PDF
  const handleExportPDF = async () => {
    const [{ default: jsPDF }, autoTable] = await Promise.all([
      import('jspdf'),
      import('jspdf-autotable')
    ]);
    const doc = new jsPDF({ orientation: 'landscape', unit: 'pt', format: 'a4' });

    // عنوان التقرير
    doc.setFont('helvetica', 'bold');
    doc.setFontSize(20);
    doc.text('تقرير المصروفات والإيرادات', doc.internal.pageSize.getWidth() - 40, 40, { align: 'right' });
    doc.setFontSize(12);
    doc.text(`تاريخ التصدير: ${new Date().toLocaleDateString('ar-EG')}`, doc.internal.pageSize.getWidth() - 40, 65, { align: 'right' });

    // بيانات الجدول
    const tableData = filtered.map(t => [
      t.type === 'income' ? 'إيراد' : 'مصروف',
      t.amount,
      t.description ?? '',
      t.category ?? '',
      formatDate(t.date) ?? '',
      t.notes ?? ''
    ]);

    const table = autoTable.default(doc, {
      head: [[
        'النوع',
        'المبلغ',
        'الوصف',
        'التصنيف',
        'التاريخ',
        'الملاحظات'
      ]],
      body: tableData,
      styles: {
        font: 'helvetica',
        fontStyle: 'normal',
        fontSize: 12,
        halign: 'right',
        cellPadding: 4,
      },
      headStyles: {
        fillColor: [16, 185, 129],
        textColor: 255,
        fontStyle: 'bold',
        halign: 'right',
      },
      startY: 90,
      margin: { left: 40, right: 40 },
      didDrawPage: (data) => {
        // شعار أو ترويسة مستقبلية
      },
    }) as any;

    // ملخص أسفل الجدول
    doc.setFontSize(14);
    doc.setFont('helvetica', 'bold');
    const summaryY = table?.finalY ?? 90;
    doc.text(`إجمالي الإيرادات: ${totalIncome.toLocaleString()} دينار`, doc.internal.pageSize.getWidth() - 40, summaryY + 30, { align: 'right' });
    doc.text(`إجمالي المصروفات: ${totalExpense.toLocaleString()} دينار`, doc.internal.pageSize.getWidth() - 40, summaryY + 55, { align: 'right' });
    doc.text(`الرصيد الحالي: ${balance.toLocaleString()} دينار`, doc.internal.pageSize.getWidth() - 40, summaryY + 80, { align: 'right' });

    doc.save('تقرير-المصروفات-الإيرادات.pdf');
  };

  return (
    <div className="min-h-screen w-full bg-gradient-to-br from-green-50 via-emerald-50 to-teal-50 py-8 px-2 md:px-8">
      {/* Header */}
      <div className="bg-gradient-to-r from-emerald-600 via-green-600 to-lime-600 text-white p-8 shadow-2xl rounded-2xl mb-6 relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-r from-emerald-500/20 to-lime-500/20"></div>
        <div className="absolute top-0 left-0 w-full h-full opacity-30 bg-gradient-to-br from-white/10 to-transparent"></div>
        <div className="container mx-auto relative z-10">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-6">
              <div className="bg-white/25 p-4 rounded-2xl shadow-xl border border-white/40 backdrop-blur-sm">
                <TrendingUp size={32} className="text-white drop-shadow-lg" />
              </div>
              <div>
                <h1 className="text-4xl font-bold font-arabic tracking-wide drop-shadow-lg mb-2">المصروفات والإيرادات</h1>
                <p className="text-emerald-100 text-lg font-medium tracking-wider">Financial Management</p>
              </div>
            </div>
            <div className="text-lg bg-white/25 px-6 py-3 rounded-2xl backdrop-blur-sm border border-white/30 shadow-lg">
              <div className="flex items-center gap-3">
                <Filter className="h-5 w-5" />
                <span className="font-arabic font-medium">{new Date().toLocaleDateString('ar-EG')}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <div className="bg-gradient-to-br from-emerald-50 to-green-100 rounded-3xl p-6 shadow-xl hover:shadow-2xl transition-all duration-300 border-2 border-emerald-200 hover:border-emerald-300 transform hover:scale-105">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-emerald-700 text-sm font-bold mb-2">إجمالي الإيرادات</h3>
              <p className="text-3xl font-bold text-emerald-600">{totalIncome.toLocaleString()} د.أ</p>
            </div>
            <div className="w-12 h-12 bg-gradient-to-br from-emerald-500 to-green-600 rounded-2xl flex items-center justify-center shadow-lg">
              <TrendingUp size={24} className="text-white" />
            </div>
          </div>
        </div>
        <div className="bg-gradient-to-br from-red-50 to-pink-100 rounded-3xl p-6 shadow-xl hover:shadow-2xl transition-all duration-300 border-2 border-red-200 hover:border-red-300 transform hover:scale-105">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-red-700 text-sm font-bold mb-2">إجمالي المصروفات</h3>
              <p className="text-3xl font-bold text-red-600">{totalExpense.toLocaleString()} د.أ</p>
            </div>
            <div className="w-12 h-12 bg-gradient-to-br from-red-500 to-pink-600 rounded-2xl flex items-center justify-center shadow-lg">
              <TrendingDown size={24} className="text-white" />
            </div>
          </div>
        </div>
        <div className="bg-gradient-to-br from-blue-50 to-cyan-100 rounded-3xl p-6 shadow-xl hover:shadow-2xl transition-all duration-300 border-2 border-blue-200 hover:border-blue-300 transform hover:scale-105">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-blue-700 text-sm font-bold mb-2">الرصيد الحالي</h3>
              <p className="text-3xl font-bold text-blue-600">{balance.toLocaleString()} د.أ</p>
            </div>
            <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-cyan-600 rounded-2xl flex items-center justify-center shadow-lg">
              <PieChart size={24} className="text-white" />
            </div>
          </div>
        </div>
      </div>

      {/* Filters */}
      <div className="bg-gradient-to-br from-white to-gray-50 rounded-3xl shadow-xl p-6 mb-8 border-2 border-emerald-100">
        <div className="flex flex-col lg:flex-row gap-6 items-center">
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 bg-gradient-to-br from-emerald-500 to-green-600 rounded-xl flex items-center justify-center shadow-lg">
              <Filter size={20} className="text-white" />
            </div>
            <span className="font-arabic font-bold text-emerald-700 text-lg">تصفية الحركات:</span>
          </div>
          <div className="flex flex-wrap gap-4">
            <select
              value={filterType}
              onChange={e => setFilterType(e.target.value as any)}
              className="px-4 py-3 border-2 border-emerald-200 rounded-xl focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 bg-white shadow-lg hover:shadow-xl transition-all duration-200 font-arabic font-medium"
            >
              <option value="all">جميع الحركات</option>
              <option value="income">إيرادات</option>
              <option value="expense">مصروفات</option>
            </select>
            <input
              type="text"
              value={search}
              onChange={e => setSearch(e.target.value)}
              placeholder="بحث..."
              className="px-4 py-3 border-2 border-emerald-200 rounded-xl focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 bg-white shadow-lg hover:shadow-xl transition-all duration-200 font-arabic font-medium"
            />
            <button
              onClick={() => { setFilterType('all'); setSearch(''); }}
              className="px-6 py-3 bg-gradient-to-r from-emerald-500 to-green-600 text-white rounded-xl hover:from-emerald-600 hover:to-green-700 transition-all duration-300 flex items-center gap-3 shadow-lg hover:shadow-xl transform hover:scale-105 font-arabic font-bold"
            >
              <RefreshCw size={18} />
              <span>إعادة تعيين</span>
            </button>
          </div>
        </div>
      </div>

      {/* باقي الكود كما هو (الجداول، النماذج، الرسوم البيانية) */}
      <div className="space-y-8">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">المصروفات والإيرادات</h1>
            <p className="text-gray-600 mt-2">سجل جميع الحركات المالية (دينار أردني)</p>
          </div>
          <button
            onClick={() => { setIsModalOpen(true); setEditTransaction(null); setForm({ type: 'expense', amount: 0, description: '', date: new Date().toISOString().split('T')[0], category: '', notes: '' }); }}
            className="bg-emerald-600 text-white px-6 py-3 rounded-lg font-semibold flex items-center gap-2 hover:bg-emerald-700"
          >
            <Plus size={20} /> إضافة معاملة
          </button>
        </div>

        {/* بطاقات إحصائية */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="bg-gradient-to-br from-green-400 via-green-500 to-green-700 rounded-3xl shadow-2xl border-t-4 border-green-300 p-8 flex items-center min-h-[140px] transition-transform duration-300 hover:scale-105 hover:shadow-green-400/40">
            <div className="flex-1 z-10">
              <p className="text-xl font-bold text-white/90 mb-3 tracking-wide drop-shadow">إجمالي الإيرادات</p>
              <p className={`text-5xl font-extrabold text-white drop-shadow-lg mb-2`}>{formatCurrency(totalIncome)}</p>
            </div>
            <div className="z-10">
              <div className="w-20 h-20 flex items-center justify-center rounded-2xl bg-white/30 shadow-xl border-4 border-white/30">
                <TrendingUp className="w-12 h-12 text-white drop-shadow" />
              </div>
            </div>
          </div>
          <div className="bg-gradient-to-br from-red-400 via-red-500 to-red-700 rounded-3xl shadow-2xl border-t-4 border-red-300 p-8 flex items-center min-h-[140px] transition-transform duration-300 hover:scale-105 hover:shadow-red-400/40">
            <div className="flex-1 z-10">
              <p className="text-xl font-bold text-white/90 mb-3 tracking-wide drop-shadow">إجمالي المصروفات</p>
              <p className={`text-5xl font-extrabold text-white drop-shadow-lg mb-2`}>{formatCurrency(totalExpense)}</p>
            </div>
            <div className="z-10">
              <div className="w-20 h-20 flex items-center justify-center rounded-2xl bg-white/30 shadow-xl border-4 border-white/30">
                <TrendingDown className="w-12 h-12 text-white drop-shadow" />
              </div>
            </div>
          </div>
          <div className="bg-gradient-to-br from-blue-400 via-blue-500 to-blue-700 rounded-3xl shadow-2xl border-t-4 border-blue-300 p-8 flex items-center min-h-[140px] transition-transform duration-300 hover:scale-105 hover:shadow-blue-400/40">
            <div className="flex-1 z-10">
              <p className="text-xl font-bold text-white/90 mb-3 tracking-wide drop-shadow">الرصيد الحالي</p>
              <p className={`text-5xl font-extrabold text-white drop-shadow-lg mb-2`}>{formatCurrency(balance)}</p>
            </div>
            <div className="z-10">
              <div className="w-20 h-20 flex items-center justify-center rounded-2xl bg-white/30 shadow-xl border-4 border-white/30">
                <Filter className="w-12 h-12 text-white drop-shadow" />
              </div>
            </div>
          </div>
        </div>

        {/* رسم بياني وتصدير */}
        <div className="flex flex-col md:flex-row gap-8 items-start mb-8">
          <div className="flex-1 bg-white rounded-xl shadow-sm p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">توزيع المصروفات حسب التصنيف</h3>
            <Pie
              data={{
                labels: expenseCategories,
                datasets: [{
                  data: expenseData,
                  backgroundColor: pieColors,
                  borderWidth: 1
                }]
              }}
              options={{ plugins: { legend: { position: 'bottom', labels: { font: { family: 'Cairo, Tajawal, Arial' } } } } }}
            />
          </div>
          <div className="flex-1 bg-white rounded-xl shadow-sm p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">توزيع الإيرادات حسب التصنيف</h3>
            <Pie
              data={{
                labels: incomeCategories,
                datasets: [{
                  data: incomeData,
                  backgroundColor: pieColors,
                  borderWidth: 1
                }]
              }}
              options={{ plugins: { legend: { position: 'bottom', labels: { font: { family: 'Cairo, Tajawal, Arial' } } } } }}
            />
          </div>
          <div className="flex flex-col gap-4">
            <button
              onClick={handleExportExcel}
              className="bg-gradient-to-r from-blue-500 to-emerald-500 text-white px-6 py-3 rounded-lg font-semibold flex items-center gap-2 hover:from-blue-600 hover:to-emerald-600 shadow-lg"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" /></svg>
              تصدير Excel
            </button>
            <button
              onClick={handleExportPDF}
              className="bg-gradient-to-r from-emerald-600 to-blue-500 text-white px-6 py-3 rounded-lg font-semibold flex items-center gap-2 hover:from-emerald-700 hover:to-blue-600 shadow-lg"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" /></svg>
              تصدير PDF
            </button>
          </div>
        </div>

        {/* فلاتر */}
        <div className="flex flex-col md:flex-row gap-4 mb-6">
          <select
            className="border rounded-lg px-4 py-2 text-sm focus:ring-emerald-500 focus:border-emerald-500"
            value={filterType}
            onChange={e => setFilterType(e.target.value as any)}
          >
            <option value="all">الكل</option>
            <option value="income">إيراد</option>
            <option value="expense">مصروف</option>
          </select>
          <input
            type="text"
            className="border rounded-lg px-4 py-2 w-full text-sm focus:ring-emerald-500 focus:border-emerald-500"
            placeholder="بحث بالوصف أو التصنيف..."
            value={search}
            onChange={e => setSearch(e.target.value)}
          />
        </div>

        {/* جدول المعاملات */}
        <div className="bg-white rounded-xl shadow-sm overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-4 py-3 text-right text-xs font-bold text-gray-700">النوع</th>
                <th className="px-4 py-3 text-right text-xs font-bold text-gray-700">المبلغ</th>
                <th className="px-4 py-3 text-right text-xs font-bold text-gray-700">الوصف</th>
                <th className="px-4 py-3 text-right text-xs font-bold text-gray-700">التصنيف</th>
                <th className="px-4 py-3 text-right text-xs font-bold text-gray-700">التاريخ</th>
                <th className="px-4 py-3 text-right text-xs font-bold text-gray-700">الملاحظات</th>
                <th className="px-4 py-3 text-right text-xs font-bold text-gray-700">الإجراءات</th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-100">
              {filtered.map((t) => (
                <tr key={t.id} className="hover:bg-gray-50 transition-colors">
                  <td className="px-4 py-3">
                    <span className={`inline-flex items-center gap-1 px-3 py-1 rounded-full text-xs font-medium ${t.type === 'income' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>{t.type === 'income' ? 'إيراد' : 'مصروف'}</span>
                  </td>
                  <td className="px-4 py-3 text-sm font-bold">{formatCurrency(t.amount)}</td>
                  <td className="px-4 py-3 text-sm text-gray-700">{t.description}</td>
                  <td className="px-4 py-3 text-sm text-gray-700">{t.category || '-'}</td>
                  <td className="px-4 py-3 text-sm text-gray-700">{formatDate(t.date)}</td>
                  <td className="px-4 py-3 text-sm text-gray-700">{t.notes || '-'}</td>
                  <td className="px-4 py-3 text-sm">
                    <button className="text-blue-600 hover:text-blue-800 p-2" title="تعديل" onClick={() => handleEdit(t)}><Edit size={16} /></button>
                    <button className="text-red-600 hover:text-red-800 p-2" title="حذف" onClick={() => handleDelete(t.id)}><Trash2 size={16} /></button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {/* نافذة إضافة/تعديل */}
        <Modal isOpen={isModalOpen} onClose={() => setIsModalOpen(false)} title={editTransaction ? 'تعديل معاملة' : 'إضافة معاملة'}>
          <form
            onSubmit={e => { e.preventDefault(); handleSave(); }}
            className="space-y-4"
          >
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block mb-1 text-sm font-medium">النوع</label>
                <select
                  className="border rounded-lg px-4 py-2 w-full text-sm focus:ring-emerald-500 focus:border-emerald-500"
                  value={form.type}
                  onChange={e => setForm(f => ({ ...f, type: e.target.value as any }))}
                  required
                >
                  <option value="income">إيراد</option>
                  <option value="expense">مصروف</option>
                </select>
              </div>
              <div>
                <label className="block mb-1 text-sm font-medium">المبلغ (دينار)</label>
                <input
                  type="number"
                  className="border rounded-lg px-4 py-2 w-full text-sm focus:ring-emerald-500 focus:border-emerald-500"
                  value={form.amount}
                  onChange={e => setForm(f => ({ ...f, amount: Number(e.target.value) }))}
                  min="0"
                  required
                />
              </div>
              <div className="md:col-span-2">
                <label className="block mb-1 text-sm font-medium">الوصف</label>
                <input
                  type="text"
                  className="border rounded-lg px-4 py-2 w-full text-sm focus:ring-emerald-500 focus:border-emerald-500"
                  value={form.description}
                  onChange={e => setForm(f => ({ ...f, description: e.target.value }))}
                  required
                />
              </div>
              <div>
                <label className="block mb-1 text-sm font-medium">التصنيف</label>
                <input
                  type="text"
                  className="border rounded-lg px-4 py-2 w-full text-sm focus:ring-emerald-500 focus:border-emerald-500"
                  value={form.category}
                  onChange={e => setForm(f => ({ ...f, category: e.target.value }))}
                />
              </div>
              <div>
                <label className="block mb-1 text-sm font-medium">التاريخ</label>
                <input
                  type="date"
                  className="border rounded-lg px-4 py-2 w-full text-sm focus:ring-emerald-500 focus:border-emerald-500"
                  value={form.date}
                  onChange={e => setForm(f => ({ ...f, date: e.target.value }))}
                  required
                />
              </div>
              <div className="md:col-span-2">
                <label className="block mb-1 text-sm font-medium">ملاحظات</label>
                <textarea
                  className="border rounded-lg px-4 py-2 w-full text-sm focus:ring-emerald-500 focus:border-emerald-500"
                  value={form.notes}
                  onChange={e => setForm(f => ({ ...f, notes: e.target.value }))}
                  rows={2}
                />
              </div>
            </div>
            <div className="flex justify-end gap-2 mt-4">
              <button
                type="button"
                className="bg-gray-100 text-gray-700 px-6 py-2 rounded-lg font-semibold hover:bg-gray-200"
                onClick={() => setIsModalOpen(false)}
              >
                إلغاء
              </button>
              <button
                type="submit"
                className="bg-emerald-600 text-white px-6 py-2 rounded-lg font-semibold hover:bg-emerald-700"
              >
                {editTransaction ? 'حفظ التعديلات' : 'إضافة'}
              </button>
            </div>
          </form>
        </Modal>
      </div>
    </div>
  );
};

export default ExpensesRevenuesView; 