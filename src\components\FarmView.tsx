import React, { useState, useEffect } from 'react';
import {
  Leaf,
  MapPin,
  Calendar,
  Droplets,
  Scissors,
  TrendingUp,
  Users,
  DollarSign,
  Clock,
  AlertCircle,
  CheckCircle,
  Eye,
  Edit,
  Plus,
  Grid,
  List,
  BarChart3,
  Settings,
  Download,
  Filter,
  Search,
  RefreshCw,
  Sprout,
  TreePine,
  Zap,
  Target,
  Activity,
  Trash2,
  Package
} from 'lucide-react';
import Modal from './Modal';
import { farmToWarehouse, getExpectedHarvests, getFarmProductionReport, syncFarmWithInventory, calculateTankCost, calculateProfitability } from '../utils/farmIntegration';
import { formatDate } from '../utils/database';

// تعريف الأنواع
interface FarmStage {
  id: number;
  name: string;
  tanks: Tank[];
  location: {
    x: number;
    y: number;
  };
  status: 'active' | 'maintenance' | 'inactive';
  supervisor: string;
  notes: string;
}

interface Tank {
  id: number;
  stageId: number;
  tankNumber: number;
  lettuceType: 'iceberg' | 'romaine';
  plantingDate: string;
  expectedHarvestDate: string;
  currentPhase: 'seeding' | 'germination' | 'growth' | 'maturation' | 'harvest' | 'empty';
  seedType: string;
  seedQuantity: number;
  waterUsage: number;
  fertilizerUsage: number;
  growthStatus: 'excellent' | 'good' | 'average' | 'poor';
  estimatedYield: number;
  actualYield?: number;
  harvestDate?: string;
  notes: string;
  irrigationSchedule: IrrigationSchedule[];
  fertilizerSchedule: FertilizerSchedule[];
}

interface IrrigationSchedule {
  id: number;
  date: string;
  time: string;
  duration: number; // بالدقائق
  waterAmount: number; // باللتر
  completed: boolean;
  notes: string;
}

interface FertilizerSchedule {
  id: number;
  date: string;
  fertilizerType: string;
  amount: number; // بالكيلو
  completed: boolean;
  notes: string;
}

interface FarmWorker {
  id: number;
  name: string;
  position: string;
  assignedStages: number[];
  salary: number;
  phone: string;
  startDate: string;
  status: 'active' | 'inactive' | 'vacation';
}

interface FarmExpense {
  id: number;
  date: string;
  type: 'seeds' | 'fertilizer' | 'water' | 'labor' | 'equipment' | 'maintenance' | 'other';
  description: string;
  amount: number;
  category: string;
  stageId?: number;
  tankId?: number;
}

interface FarmViewProps {
  setCurrentView: (view: string) => void;
  navigationItems: any[];
  currentView: string;
}

const FarmView: React.FC<FarmViewProps> = ({
  setCurrentView,
  navigationItems,
  currentView
}) => {
  // الحالات الرئيسية
  const [viewMode, setViewMode] = useState<'map' | 'table' | 'cards'>('map');
  const [selectedStage, setSelectedStage] = useState<FarmStage | null>(null);
  const [selectedTank, setSelectedTank] = useState<Tank | null>(null);
  const [farmStages, setFarmStages] = useState<FarmStage[]>([]);
  const [farmWorkers, setFarmWorkers] = useState<FarmWorker[]>([]);
  const [farmExpenses, setFarmExpenses] = useState<FarmExpense[]>([]);
  const [filterPhase, setFilterPhase] = useState<string>('all');
  const [filterLettuceType, setFilterLettuceType] = useState<string>('all');
  const [searchTerm, setSearchTerm] = useState('');

  // حالات النوافذ المنبثقة
  const [isStageModalOpen, setIsStageModalOpen] = useState(false);
  const [isTankModalOpen, setIsTankModalOpen] = useState(false);
  const [isWorkerModalOpen, setIsWorkerModalOpen] = useState(false);
  const [isExpenseModalOpen, setIsExpenseModalOpen] = useState(false);
  const [isReportModalOpen, setIsReportModalOpen] = useState(false);
  const [isIrrigationModalOpen, setIsIrrigationModalOpen] = useState(false);
  const [isFertilizerModalOpen, setIsFertilizerModalOpen] = useState(false);

  // تحميل البيانات الأولية
  useEffect(() => {
    initializeFarmData();
    loadFarmData();
  }, []);

  // تهيئة بيانات المزرعة
  const initializeFarmData = () => {
    const stages: FarmStage[] = [];
    
    // إنشاء 24 مرحلة
    for (let i = 1; i <= 24; i++) {
      const tanks: Tank[] = [];
      
      // إنشاء 5 محابس لكل مرحلة
      for (let j = 1; j <= 5; j++) {
        const lettuceType = j === 5 ? 'romaine' : 'iceberg'; // المحبس الخامس للرومين
        
        // إضافة بيانات تجريبية لجعل الجدول يظهر محتوى
        const isActive = Math.random() > 0.3; // 70% من المحابس نشطة
        const phases = ['seeding', 'germination', 'growth', 'maturation', 'harvest', 'empty'];
        const currentPhase = isActive ? phases[Math.floor(Math.random() * 5)] : 'empty';
        
        // تواريخ تجريبية
        const plantingDate = isActive ? formatDate(new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000)) : '';
        const expectedHarvestDate = isActive ? formatDate(new Date(Date.now() + Math.random() * 20 * 24 * 60 * 60 * 1000)) : '';
        
        tanks.push({
          id: parseInt(`${i}${j}`),
          stageId: i,
          tankNumber: j,
          lettuceType,
          plantingDate,
          expectedHarvestDate,
          currentPhase: currentPhase as any,
          seedType: lettuceType === 'iceberg' ? 'خس أيسبيرغ' : 'خس رومين',
          seedQuantity: isActive ? Math.floor(Math.random() * 100) + 50 : 0,
          waterUsage: isActive ? Math.floor(Math.random() * 500) + 100 : 0,
          fertilizerUsage: isActive ? Math.floor(Math.random() * 20) + 5 : 0,
          growthStatus: isActive ? ['excellent', 'good', 'average'][Math.floor(Math.random() * 3)] as any : 'good',
          estimatedYield: isActive ? Math.floor(Math.random() * 50) + 20 : 0,
          notes: isActive ? 'محبس نشط' : '',
          irrigationSchedule: [],
          fertilizerSchedule: []
        });
      }
      
      stages.push({
        id: i,
        name: `المرحلة ${i}`,
        tanks,
        location: {
          x: (i % 6) * 200 + 50,
          y: Math.floor((i - 1) / 6) * 150 + 50
        },
        status: 'active',
        supervisor: `مشرف ${i}`,
        notes: `ملاحظات المرحلة ${i}`
      });
    }
    
    setFarmStages(stages);
  };

  // تحميل البيانات المحفوظة
  const loadFarmData = () => {
    try {
      const savedStages = localStorage.getItem('farmStages');
      const savedWorkers = localStorage.getItem('farmWorkers');
      const savedExpenses = localStorage.getItem('farmExpenses');
      
      if (savedStages) {
        setFarmStages(JSON.parse(savedStages));
      }
      if (savedWorkers) {
        setFarmWorkers(JSON.parse(savedWorkers));
      }
      if (savedExpenses) {
        setFarmExpenses(JSON.parse(savedExpenses));
      }
    } catch (error) {
      console.error('خطأ في تحميل بيانات المزرعة:', error);
    }
  };

  // حفظ البيانات
  const saveFarmData = () => {
    try {
      localStorage.setItem('farmStages', JSON.stringify(farmStages));
      localStorage.setItem('farmWorkers', JSON.stringify(farmWorkers));
      localStorage.setItem('farmExpenses', JSON.stringify(farmExpenses));
    } catch (error) {
      console.error('خطأ في حفظ بيانات المزرعة:', error);
    }
  };

  // حفظ البيانات عند التغيير
  useEffect(() => {
    saveFarmData();
  }, [farmStages, farmWorkers, farmExpenses]);

  // حساب الإحصائيات
  const calculateStats = () => {
    const totalTanks = farmStages.reduce((sum, stage) => sum + stage.tanks.length, 0);
    const activeTanks = farmStages.reduce((sum, stage) => 
      sum + stage.tanks.filter(tank => tank.currentPhase !== 'empty').length, 0
    );
    const icebergTanks = farmStages.reduce((sum, stage) => 
      sum + stage.tanks.filter(tank => tank.lettuceType === 'iceberg' && tank.currentPhase !== 'empty').length, 0
    );
    const romaineTanks = farmStages.reduce((sum, stage) => 
      sum + stage.tanks.filter(tank => tank.lettuceType === 'romaine' && tank.currentPhase !== 'empty').length, 0
    );
    const readyForHarvest = farmStages.reduce((sum, stage) => 
      sum + stage.tanks.filter(tank => tank.currentPhase === 'harvest').length, 0
    );
    const totalWaterUsage = farmStages.reduce((sum, stage) => 
      sum + stage.tanks.reduce((tankSum, tank) => tankSum + tank.waterUsage, 0), 0
    );
    const totalFertilizerUsage = farmStages.reduce((sum, stage) => 
      sum + stage.tanks.reduce((tankSum, tank) => tankSum + tank.fertilizerUsage, 0), 0
    );
    const totalExpenses = farmExpenses.reduce((sum, expense) => sum + expense.amount, 0);
    const estimatedYield = farmStages.reduce((sum, stage) => 
      sum + stage.tanks.reduce((tankSum, tank) => tankSum + tank.estimatedYield, 0), 0
    );

    return {
      totalTanks,
      activeTanks,
      icebergTanks,
      romaineTanks,
      readyForHarvest,
      totalWaterUsage,
      totalFertilizerUsage,
      totalExpenses,
      estimatedYield,
      activeStages: farmStages.filter(stage => stage.status === 'active').length,
      totalWorkers: farmWorkers.length,
      activeWorkers: farmWorkers.filter(worker => worker.status === 'active').length
    };
  };

  const stats = calculateStats();

  // الحصول على لون المرحلة حسب الحالة
  const getPhaseColor = (phase: string) => {
    switch (phase) {
      case 'seeding': return 'bg-blue-500';
      case 'germination': return 'bg-green-500';
      case 'growth': return 'bg-yellow-500';
      case 'maturation': return 'bg-orange-500';
      case 'harvest': return 'bg-red-500';
      case 'empty': return 'bg-gray-300';
      default: return 'bg-gray-300';
    }
  };

  // الحصول على اسم المرحلة
  const getPhaseName = (phase: string) => {
    switch (phase) {
      case 'seeding': return 'البذر';
      case 'germination': return 'الإنبات';
      case 'growth': return 'النمو';
      case 'maturation': return 'النضج';
      case 'harvest': return 'الحصاد';
      case 'empty': return 'فارغ';
      default: return 'غير محدد';
    }
  };

  // تصفية المحابس
  const getFilteredTanks = () => {
    let allTanks: Tank[] = [];
    farmStages.forEach(stage => {
      allTanks = [...allTanks, ...stage.tanks];
    });

    return allTanks.filter(tank => {
      const matchesPhase = filterPhase === 'all' || tank.currentPhase === filterPhase;
      const matchesType = filterLettuceType === 'all' || tank.lettuceType === filterLettuceType;
      const matchesSearch = searchTerm === '' || 
        tank.seedType.toLowerCase().includes(searchTerm.toLowerCase()) ||
        tank.notes.toLowerCase().includes(searchTerm.toLowerCase());
      
      return matchesPhase && matchesType && matchesSearch;
    });
  };

  // تاريخ اليوم
  const today = new Date();
  const formattedDate = formatDate(today);

  // عدل دالة getZoneCardGradient لتدرجات عميقة وحديثة
  const getZoneCardGradient = (status: string) => {
    switch (status) {
      case 'active':
        return 'from-emerald-500 via-lime-400 to-emerald-800';
      case 'maintenance':
        return 'from-amber-300 via-orange-400 to-amber-600';
      default:
        return 'from-slate-200 via-blue-100 to-slate-400';
    }
  };

  return (
    <div className="min-h-screen w-full bg-gradient-to-br from-green-50 via-emerald-50 to-teal-50 py-8 px-2 md:px-8">
      {/* Header */}
      <div className="bg-gradient-to-r from-emerald-600 via-green-600 to-lime-600 text-white p-8 shadow-2xl rounded-2xl mb-6 relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-r from-emerald-500/20 to-lime-500/20"></div>
        <div className="absolute top-0 left-0 w-full h-full opacity-30 bg-gradient-to-br from-white/10 to-transparent"></div>
        <div className="container mx-auto relative z-10">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-6">
              <div className="bg-white/25 p-4 rounded-2xl shadow-xl border border-white/40 backdrop-blur-sm">
                <Leaf size={32} className="text-white drop-shadow-lg" />
              </div>
              <div>
                <h1 className="text-4xl font-bold font-arabic tracking-wide drop-shadow-lg mb-2">إدارة المراحل الزراعية</h1>
                <p className="text-emerald-100 text-lg font-medium tracking-wider">Advanced Farm Management System</p>
              </div>
            </div>
            <div className="text-lg bg-white/25 px-6 py-3 rounded-2xl backdrop-blur-sm border border-white/30 shadow-lg">
              <div className="flex items-center gap-3">
                <Calendar className="h-5 w-5" />
                <span className="font-arabic font-medium">{formattedDate}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Navigation */}
      <div className="bg-gradient-to-br from-emerald-50 via-blue-50 to-lime-100 p-4 shadow-2xl rounded-2xl mb-6 border border-emerald-100/60 relative">
        <div className="absolute -top-6 -left-6 w-16 h-16 bg-cyan-200 opacity-20 rounded-full blur pointer-events-none"></div>
        <div className="container mx-auto">
          <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 xl:grid-cols-9 gap-3">
            {navigationItems.map((item) => {
              const IconComponent = item.icon;
              return (
                <button
                  key={item.id}
                  onClick={() => setCurrentView(item.id)}
                  className={`p-4 rounded-2xl transition-all duration-300 transform hover:scale-105 hover:shadow-lg font-arabic font-bold border-2
                    ${currentView === item.id
                      ? 'bg-white text-blue-700 shadow-xl border-blue-500'
                      : 'bg-white text-blue-700 hover:bg-blue-50 shadow-md border-transparent'}
                  `}
                >
                  <IconComponent size={24} className="mx-auto mb-2" />
                  <span className="text-sm block">{item.name}</span>
                </button>
              );
            })}
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="container mx-auto p-0 md:p-6">
        {/* Control Panel */}
        <div className="relative bg-gradient-to-br from-emerald-50 via-blue-50 to-lime-100 rounded-2xl shadow-2xl border border-emerald-100/60 p-6 mb-6">
          <div className="absolute -top-6 -left-6 w-16 h-16 bg-cyan-200 opacity-20 rounded-full blur pointer-events-none"></div>
          <div className="flex flex-col lg:flex-row items-center justify-between gap-4">
            <div className="flex items-center gap-4">
              <div className="w-12 h-12 bg-gradient-to-br from-green-500 to-emerald-600 rounded-2xl flex items-center justify-center">
                <Leaf size={24} className="text-white" />
              </div>
              <div>
                <h2 className="text-xl font-extrabold bg-gradient-to-r from-emerald-700 to-lime-600 bg-clip-text text-transparent drop-shadow mb-2">نظام إدارة المزرعة</h2>
                <p className="text-gray-600">24 مرحلة × 5 محابس = 120 محبس زراعي</p>
              </div>
            </div>
            <div className="flex items-center gap-3">
              {/* أزرار التحكم */}
              <button
                onClick={() => setIsWorkerModalOpen(true)}
                className="bg-gradient-to-r from-blue-500 to-blue-600 text-white px-4 py-2 rounded-xl hover:from-blue-600 hover:to-blue-700 transition-all duration-300 flex items-center gap-2 shadow-lg hover:shadow-xl transform hover:scale-105"
              >
                <Users size={18} />
                <span className="font-arabic font-medium">العمالة</span>
              </button>
              
              <button
                onClick={() => setIsExpenseModalOpen(true)}
                className="bg-gradient-to-r from-purple-500 to-purple-600 text-white px-4 py-2 rounded-xl hover:from-purple-600 hover:to-purple-700 transition-all duration-300 flex items-center gap-2 shadow-lg hover:shadow-xl transform hover:scale-105"
              >
                <DollarSign size={18} />
                <span className="font-arabic font-medium">التكاليف</span>
              </button>
              
              <button
                onClick={() => setIsReportModalOpen(true)}
                className="bg-gradient-to-r from-orange-500 to-orange-600 text-white px-4 py-2 rounded-xl hover:from-orange-600 hover:to-orange-700 transition-all duration-300 flex items-center gap-2 shadow-lg hover:shadow-xl transform hover:scale-105"
              >
                <BarChart3 size={18} />
                <span className="font-arabic font-medium">التقارير</span>
              </button>

              {/* أزرار العرض */}
              <div className="flex bg-gray-100 rounded-xl p-1">
                <button
                  onClick={() => setViewMode('map')}
                  className={`p-2 rounded-lg transition-all duration-200 ${
                    viewMode === 'map' 
                      ? 'bg-green-500 text-white shadow-md' 
                      : 'text-gray-600 hover:bg-gray-200'
                  }`}
                >
                  <Grid size={18} />
                </button>
                <button
                  onClick={() => setViewMode('table')}
                  className={`p-2 rounded-lg transition-all duration-200 ${
                    viewMode === 'table' 
                      ? 'bg-green-500 text-white shadow-md' 
                      : 'text-gray-600 hover:bg-gray-200'
                  }`}
                >
                  <List size={18} />
                </button>
                <button
                  onClick={() => setViewMode('cards')}
                  className={`p-2 rounded-lg transition-all duration-200 ${
                    viewMode === 'cards' 
                      ? 'bg-green-500 text-white shadow-md' 
                      : 'text-gray-600 hover:bg-gray-200'
                  }`}
                >
                  <Eye size={18} />
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Statistics Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-6 mb-8">
          <div className="bg-gradient-to-br from-emerald-50 to-green-100 rounded-3xl p-6 shadow-xl hover:shadow-2xl transition-all duration-300 border-2 border-emerald-200 hover:border-emerald-300 transform hover:scale-105">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-emerald-700 text-sm font-bold mb-2">إجمالي المحابس</h3>
                <p className="text-3xl font-bold text-emerald-600">{stats.totalTanks}</p>
                <p className="text-emerald-500 text-xs mt-1">24 مرحلة × 5 محابس</p>
              </div>
              <div className="w-12 h-12 bg-gradient-to-br from-emerald-500 to-green-600 rounded-2xl flex items-center justify-center shadow-lg">
                <Target size={24} className="text-white" />
              </div>
            </div>
          </div>

          <div className="bg-gradient-to-br from-blue-50 to-cyan-100 rounded-3xl p-6 shadow-xl hover:shadow-2xl transition-all duration-300 border-2 border-blue-200 hover:border-blue-300 transform hover:scale-105">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-blue-700 text-sm font-bold mb-2">المحابس النشطة</h3>
                <p className="text-3xl font-bold text-blue-600">{stats.activeTanks}</p>
                <p className="text-blue-500 text-xs mt-1">قيد الزراعة</p>
              </div>
              <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-cyan-600 rounded-2xl flex items-center justify-center shadow-lg">
                <Activity size={24} className="text-white" />
              </div>
            </div>
          </div>

          <div className="bg-gradient-to-br from-purple-50 to-indigo-100 rounded-3xl p-6 shadow-xl hover:shadow-2xl transition-all duration-300 border-2 border-purple-200 hover:border-purple-300 transform hover:scale-105">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-purple-700 text-sm font-bold mb-2">أيسبيرغ نشط</h3>
                <p className="text-3xl font-bold text-purple-600">{stats.icebergTanks}</p>
                <p className="text-purple-500 text-xs mt-1">خس أيسبيرغ</p>
              </div>
              <div className="w-12 h-12 bg-gradient-to-br from-purple-500 to-indigo-600 rounded-2xl flex items-center justify-center shadow-lg">
                <Sprout size={24} className="text-white" />
              </div>
            </div>
          </div>

          <div className="bg-gradient-to-br from-orange-50 to-amber-100 rounded-3xl p-6 shadow-xl hover:shadow-2xl transition-all duration-300 border-2 border-orange-200 hover:border-orange-300 transform hover:scale-105">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-orange-700 text-sm font-bold mb-2">رومين نشط</h3>
                <p className="text-3xl font-bold text-orange-600">{stats.romaineTanks}</p>
                <p className="text-orange-500 text-xs mt-1">خس رومين</p>
              </div>
              <div className="w-12 h-12 bg-gradient-to-br from-orange-500 to-amber-600 rounded-2xl flex items-center justify-center shadow-lg">
                <TreePine size={24} className="text-white" />
              </div>
            </div>
          </div>

          <div className="bg-gradient-to-br from-red-50 to-pink-100 rounded-3xl p-6 shadow-xl hover:shadow-2xl transition-all duration-300 border-2 border-red-200 hover:border-red-300 transform hover:scale-105">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-red-700 text-sm font-bold mb-2">جاهز للحصاد</h3>
                <p className="text-3xl font-bold text-red-600">{stats.readyForHarvest}</p>
                <p className="text-red-500 text-xs mt-1">مستعد للجني</p>
              </div>
              <div className="w-12 h-12 bg-gradient-to-br from-red-500 to-pink-600 rounded-2xl flex items-center justify-center shadow-lg">
                <Scissors size={24} className="text-white" />
              </div>
            </div>
          </div>

          <div className="bg-gradient-to-br from-gray-50 to-slate-100 rounded-3xl p-6 shadow-xl hover:shadow-2xl transition-all duration-300 border-2 border-gray-200 hover:border-gray-300 transform hover:scale-105">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-gray-700 text-sm font-bold mb-2">إجمالي التكاليف</h3>
                <p className="text-3xl font-bold text-gray-600">{stats.totalExpenses.toLocaleString()}</p>
                <p className="text-gray-500 text-xs mt-1">دينار أردني</p>
              </div>
              <div className="w-12 h-12 bg-gradient-to-br from-gray-500 to-slate-600 rounded-2xl flex items-center justify-center shadow-lg">
                <DollarSign size={24} className="text-white" />
              </div>
            </div>
          </div>
        </div>

        {/* Filters */}
        <div className="bg-gradient-to-br from-white to-gray-50 rounded-3xl shadow-xl p-6 mb-8 border-2 border-emerald-100">
          <div className="flex flex-col lg:flex-row gap-6 items-center">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 bg-gradient-to-br from-emerald-500 to-green-600 rounded-xl flex items-center justify-center shadow-lg">
                <Filter size={20} className="text-white" />
              </div>
              <span className="font-arabic font-bold text-emerald-700 text-lg">تصفية المحابس:</span>
            </div>
            
            <div className="flex flex-wrap gap-4">
              <select
                value={filterPhase}
                onChange={(e) => setFilterPhase(e.target.value)}
                className="px-4 py-3 border-2 border-emerald-200 rounded-xl focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 bg-white shadow-lg hover:shadow-xl transition-all duration-200 font-arabic font-medium"
              >
                <option value="all">🌱 جميع المراحل</option>
                <option value="seeding">🌱 البذر</option>
                <option value="germination">🌿 الإنبات</option>
                <option value="growth">🌳 النمو</option>
                <option value="maturation">🍃 النضج</option>
                <option value="harvest">✂️ الحصاد</option>
                <option value="empty">⚪ فارغ</option>
              </select>

              <select
                value={filterLettuceType}
                onChange={(e) => setFilterLettuceType(e.target.value)}
                className="px-4 py-3 border-2 border-blue-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white shadow-lg hover:shadow-xl transition-all duration-200 font-arabic font-medium"
              >
                <option value="all">🥬 جميع الأنواع</option>
                <option value="iceberg">🥬 خس أيسبيرغ</option>
                <option value="romaine">🥬 خس رومين</option>
              </select>

              <div className="relative">
                <Search size={20} className="absolute right-4 top-3 text-emerald-400" />
                <input
                  type="text"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  placeholder="🔍 بحث في المحابس..."
                  className="pl-4 pr-12 py-3 border-2 border-emerald-200 rounded-xl focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 w-64 bg-white shadow-lg hover:shadow-xl transition-all duration-200 font-arabic"
                />
              </div>

              <button
                onClick={() => {
                  setFilterPhase('all');
                  setFilterLettuceType('all');
                  setSearchTerm('');
                }}
                className="px-6 py-3 bg-gradient-to-r from-emerald-500 to-green-600 text-white rounded-xl hover:from-emerald-600 hover:to-green-700 transition-all duration-300 flex items-center gap-3 shadow-lg hover:shadow-xl transform hover:scale-105 font-arabic font-bold"
              >
                <RefreshCw size={18} />
                <span>🔄 إعادة تعيين</span>
              </button>
            </div>
          </div>
        </div>

        {/* Content based on view mode */}
        {viewMode === 'map' && (
          <div className="bg-white rounded-2xl shadow-xl p-6">
            <h3 className="text-xl font-bold text-gray-800 mb-4 font-arabic">خريطة المزرعة</h3>
            <div className="relative bg-green-50 rounded-xl p-4" style={{ minHeight: '600px' }}>
              <svg width="100%" height="600" viewBox="0 0 1200 600">
                {farmStages.map((stage, index) => (
                  <g key={stage.id}>
                    {/* Stage Rectangle */}
                    <rect
                      x={stage.location.x}
                      y={stage.location.y}
                      width={180}
                      height={120}
                      fill={stage.status === 'active' ? '#dcfce7' : '#f3f4f6'}
                      stroke={stage.status === 'active' ? '#22c55e' : '#9ca3af'}
                      strokeWidth="2"
                      rx="8"
                      className="cursor-pointer hover:stroke-green-600 transition-colors"
                      onClick={() => setSelectedStage(stage)}
                    />
                    
                    {/* Stage Title */}
                    <text
                      x={stage.location.x + 90}
                      y={stage.location.y + 20}
                      textAnchor="middle"
                      className="fill-gray-800 text-sm font-bold"
                    >
                      {stage.name}
                    </text>
                    
                    {/* Tanks */}
                    {stage.tanks.map((tank, tankIndex) => (
                      <g key={tank.id}>
                        <rect
                          x={stage.location.x + 10 + (tankIndex * 32)}
                          y={stage.location.y + 30}
                          width={28}
                          height={40}
                          fill={tank.currentPhase === 'empty' ? '#f3f4f6' : '#22c55e'}
                          stroke={tank.lettuceType === 'romaine' ? '#f59e0b' : '#3b82f6'}
                          strokeWidth="2"
                          rx="4"
                          className="cursor-pointer hover:opacity-80 transition-opacity"
                          onClick={() => setSelectedTank(tank)}
                        />
                        
                        {/* Tank Number */}
                        <text
                          x={stage.location.x + 24 + (tankIndex * 32)}
                          y={stage.location.y + 55}
                          textAnchor="middle"
                          className="fill-white text-xs font-bold"
                        >
                          {tank.tankNumber}
                        </text>
                        
                        {/* Phase Indicator */}
                        <circle
                          cx={stage.location.x + 24 + (tankIndex * 32)}
                          cy={stage.location.y + 85}
                          r="4"
                          fill={tank.currentPhase === 'harvest' ? '#ef4444' : 
                                tank.currentPhase === 'maturation' ? '#f59e0b' :
                                tank.currentPhase === 'growth' ? '#eab308' :
                                tank.currentPhase === 'germination' ? '#22c55e' :
                                tank.currentPhase === 'seeding' ? '#3b82f6' : '#9ca3af'}
                        />
                      </g>
                    ))}
                    
                    {/* Active Tanks Count */}
                    <text
                      x={stage.location.x + 90}
                      y={stage.location.y + 105}
                      textAnchor="middle"
                      className="fill-gray-600 text-xs"
                    >
                      نشط: {stage.tanks.filter(t => t.currentPhase !== 'empty').length}/5
                    </text>
                  </g>
                ))}
              </svg>
            </div>
            
            {/* Legend */}
            <div className="mt-4 bg-gray-50 rounded-lg p-4">
              <h4 className="font-bold text-gray-800 mb-2">دليل الألوان:</h4>
              <div className="flex flex-wrap gap-4">
                <div className="flex items-center gap-2">
                  <div className="w-4 h-4 bg-blue-500 rounded"></div>
                  <span className="text-sm">أيسبيرغ</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-4 h-4 bg-amber-500 rounded"></div>
                  <span className="text-sm">رومين</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-4 h-4 bg-blue-500 rounded-full"></div>
                  <span className="text-sm">البذر</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-4 h-4 bg-green-500 rounded-full"></div>
                  <span className="text-sm">الإنبات</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-4 h-4 bg-yellow-500 rounded-full"></div>
                  <span className="text-sm">النمو</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-4 h-4 bg-orange-500 rounded-full"></div>
                  <span className="text-sm">النضج</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-4 h-4 bg-red-500 rounded-full"></div>
                  <span className="text-sm">الحصاد</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-4 h-4 bg-gray-400 rounded-full"></div>
                  <span className="text-sm">فارغ</span>
                </div>
              </div>
            </div>
          </div>
        )}

        {viewMode === 'table' && (
          <div className="bg-white rounded-2xl shadow-xl overflow-hidden">
            <div className="p-6">
              <h3 className="text-xl font-bold text-gray-800 mb-4 font-arabic">جدول المحابس</h3>
            </div>
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-green-50">
                  <tr>
                    <th className="px-6 py-4 text-right text-sm font-semibold text-green-700">المرحلة</th>
                    <th className="px-6 py-4 text-right text-sm font-semibold text-green-700">المحبس</th>
                    <th className="px-6 py-4 text-right text-sm font-semibold text-green-700">النوع</th>
                    <th className="px-6 py-4 text-right text-sm font-semibold text-green-700">المرحلة الحالية</th>
                    <th className="px-6 py-4 text-right text-sm font-semibold text-green-700">تاريخ الزراعة</th>
                    <th className="px-6 py-4 text-right text-sm font-semibold text-green-700">تاريخ الحصاد المتوقع</th>
                    <th className="px-6 py-4 text-right text-sm font-semibold text-green-700">الإنتاج المتوقع</th>
                    <th className="px-6 py-4 text-right text-sm font-semibold text-green-700">الإجراءات</th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-200">
                  {getFilteredTanks().map((tank) => {
                    const stage = farmStages.find(s => s.id === tank.stageId);
                    return (
                      <tr key={tank.id} className="hover:bg-gray-50">
                        <td className="px-6 py-4 text-sm text-gray-900">المرحلة {stage?.id}</td>
                        <td className="px-6 py-4 text-sm text-gray-900">محبس {tank.tankNumber}</td>
                        <td className="px-6 py-4 text-sm">
                          <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                            tank.lettuceType === 'iceberg' 
                              ? 'bg-blue-100 text-blue-800' 
                              : 'bg-orange-100 text-orange-800'
                          }`}>
                            {tank.lettuceType === 'iceberg' ? 'أيسبيرغ' : 'رومين'}
                          </span>
                        </td>
                        <td className="px-6 py-4 text-sm">
                          <span className={`px-2 py-1 rounded-full text-xs font-medium text-white ${getPhaseColor(tank.currentPhase)}`}>
                            {getPhaseName(tank.currentPhase)}
                          </span>
                        </td>
                        <td className="px-6 py-4 text-sm text-gray-900">{formatDate(tank.plantingDate)}</td>
                        <td className="px-6 py-4 text-sm text-gray-900">{formatDate(tank.expectedHarvestDate)}</td>
                        <td className="px-6 py-4 text-sm text-gray-900">{tank.estimatedYield} كجم</td>
                        <td className="px-6 py-4 text-sm">
                          <div className="flex gap-2">
                            <button
                              onClick={() => setSelectedTank(tank)}
                              className="text-blue-600 hover:text-blue-800 p-1 hover:bg-blue-50 rounded"
                            >
                              <Eye size={16} />
                            </button>
                            <button
                              onClick={() => {
                                setSelectedTank(tank);
                                setIsTankModalOpen(true);
                              }}
                              className="text-green-600 hover:text-green-800 p-1 hover:bg-green-50 rounded"
                            >
                              <Edit size={16} />
                            </button>
                          </div>
                        </td>
                      </tr>
                    );
                  })}
                </tbody>
              </table>
            </div>
          </div>
        )}

        {viewMode === 'cards' && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {farmStages.map((stage) => (
              <div key={stage.id} className={`relative bg-gradient-to-br ${getZoneCardGradient(stage.status)} rounded-3xl shadow-2xl ring-2 ring-emerald-200/40 overflow-hidden hover:scale-105 hover:shadow-emerald-400/60 transition-all duration-300 group`}>
                {/* زخرفة دائرية متدرجة شفافة مع blur */}
                <div className="absolute -top-10 -left-10 w-40 h-40 bg-gradient-to-br from-emerald-300 via-cyan-200 to-lime-200 opacity-30 rounded-full blur-2xl pointer-events-none"></div>
                {/* طبقة زجاجية شفافة */}
                <div className="absolute inset-0 bg-white/30 backdrop-blur-md border border-white/30 rounded-3xl pointer-events-none"></div>
                {/* محتوى البطاقة */}
                <div className="relative z-10 p-6">
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center gap-2">
                      <div className="w-10 h-10 rounded-full bg-gradient-to-br from-emerald-500 to-lime-400 flex items-center justify-center shadow-lg">
                        <Leaf className="text-white w-6 h-6" />
                      </div>
                      <h3 className="text-2xl font-extrabold bg-gradient-to-r from-emerald-700 to-lime-600 bg-clip-text text-transparent drop-shadow">{stage.name}</h3>
                    </div>
                    {/* مؤشر حالة دائري */}
                    <span className={`w-4 h-4 rounded-full border-2 border-white shadow ${stage.status === 'active' ? 'bg-emerald-400' : stage.status === 'maintenance' ? 'bg-amber-400' : 'bg-slate-400'}`}></span>
                  </div>
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-600">المحابس النشطة:</span>
                      <span className="font-medium">{stage.tanks.filter(t => t.currentPhase !== 'empty').length}/5</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-600">أيسبيرغ:</span>
                      <span className="font-medium">{stage.tanks.filter(t => t.lettuceType === 'iceberg' && t.currentPhase !== 'empty').length}</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-600">رومين:</span>
                      <span className="font-medium">{stage.tanks.filter(t => t.lettuceType === 'romaine' && t.currentPhase !== 'empty').length}</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-600">جاهز للحصاد:</span>
                      <span className="font-medium text-red-600">{stage.tanks.filter(t => t.currentPhase === 'harvest').length}</span>
                    </div>
                  </div>
                  
                  <div className="mt-4 pt-4 border-t border-gray-200">
                    <button
                      onClick={() => setSelectedStage(stage)}
                      className="w-full bg-gradient-to-r from-green-500 to-emerald-500 text-white py-2 px-4 rounded-lg hover:from-green-600 hover:to-emerald-600 transition-all duration-200 font-medium"
                    >
                      عرض التفاصيل
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Tank Detail Modal */}
      {selectedTank && (
        <Modal
          isOpen={!!selectedTank}
          onClose={() => setSelectedTank(null)}
          title={`تفاصيل المحبس ${selectedTank.tankNumber} - المرحلة ${selectedTank.stageId}`}
        >
          <div className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">نوع الخس</label>
                <p className="text-sm text-gray-900">{selectedTank.lettuceType === 'iceberg' ? 'أيسبيرغ' : 'رومين'}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">المرحلة الحالية</label>
                <span className={`px-2 py-1 rounded-full text-xs font-medium text-white ${getPhaseColor(selectedTank.currentPhase)}`}>
                  {getPhaseName(selectedTank.currentPhase)}
                </span>
              </div>
            </div>
            
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">تاريخ الزراعة</label>
                <p className="text-sm text-gray-900">{formatDate(selectedTank.plantingDate) || 'غير محدد'}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">تاريخ الحصاد المتوقع</label>
                <p className="text-sm text-gray-900">{formatDate(selectedTank.expectedHarvestDate) || 'غير محدد'}</p>
              </div>
            </div>
            
            <div className="grid grid-cols-3 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">كمية البذور</label>
                <p className="text-sm text-gray-900">{selectedTank.seedQuantity} جرام</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">استهلاك الماء</label>
                <p className="text-sm text-gray-900">{selectedTank.waterUsage} لتر</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">استهلاك السماد</label>
                <p className="text-sm text-gray-900">{selectedTank.fertilizerUsage} كجم</p>
              </div>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">الإنتاج المتوقع</label>
              <p className="text-sm text-gray-900">{selectedTank.estimatedYield} كجم</p>
            </div>
            
            {selectedTank.notes && (
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">الملاحظات</label>
                <p className="text-sm text-gray-900">{selectedTank.notes}</p>
              </div>
            )}
            
            <div className="flex gap-2 pt-4">
              <button
                onClick={() => {
                  setIsTankModalOpen(true);
                }}
                className="flex-1 bg-gradient-to-r from-green-500 to-emerald-500 text-white py-2 px-4 rounded-lg hover:from-green-600 hover:to-emerald-600 transition-all duration-200 font-medium"
              >
                تعديل
              </button>
              <button
                onClick={() => setIsIrrigationModalOpen(true)}
                className="flex-1 bg-gradient-to-r from-blue-500 to-blue-600 text-white py-2 px-4 rounded-lg hover:from-blue-600 hover:to-blue-700 transition-all duration-200 font-medium"
              >
                جدولة الري
              </button>
              <button
                onClick={() => setIsFertilizerModalOpen(true)}
                className="flex-1 bg-gradient-to-r from-purple-500 to-purple-600 text-white py-2 px-4 rounded-lg hover:from-purple-600 hover:to-purple-700 transition-all duration-200 font-medium"
              >
                جدولة التسميد
              </button>
            </div>
            
            {/* Harvest to Warehouse Button */}
            {selectedTank.currentPhase === 'harvest' && (
              <div className="mt-4 pt-4 border-t">
                <button
                  onClick={async () => {
                    try {
                      const stage = farmStages.find(s => s.id === selectedTank.stageId);
                      if (!stage) return;
                      
                      const harvestData = {
                        tankId: selectedTank.id,
                        stageName: stage.name,
                        lettuceType: selectedTank.lettuceType,
                        harvestDate: new Date().toISOString().split('T')[0],
                        actualYield: selectedTank.estimatedYield,
                        quality: selectedTank.growthStatus,
                        notes: selectedTank.notes || ''
                      };
                      
                      const result = await farmToWarehouse(harvestData);
                      
                      if (result.success) {
                        alert(`✅ ${result.message}`);
                        
                        // تحديث حالة المحبس إلى فارغ
                        const updatedTank = {
                          ...selectedTank,
                          currentPhase: 'empty' as const,
                          actualYield: selectedTank.estimatedYield,
                          harvestDate: new Date().toISOString().split('T')[0],
                          plantingDate: '',
                          expectedHarvestDate: '',
                          seedQuantity: 0,
                          waterUsage: 0,
                          fertilizerUsage: 0,
                          estimatedYield: 0,
                          notes: ''
                        };
                        
                        setSelectedTank(updatedTank);
                        
                        const updatedStages = farmStages.map(stage => ({
                          ...stage,
                          tanks: stage.tanks.map(tank => 
                            tank.id === selectedTank.id ? updatedTank : tank
                          )
                        }));
                        setFarmStages(updatedStages);
                      } else {
                        alert(`❌ ${result.message}`);
                      }
                    } catch (error) {
                      console.error('خطأ في نقل الحصاد:', error);
                      alert('حدث خطأ أثناء نقل الحصاد للمخزون');
                    }
                  }}
                  className="w-full bg-gradient-to-r from-orange-500 to-red-500 text-white py-3 px-4 rounded-lg hover:from-orange-600 hover:to-red-600 transition-all duration-200 font-medium flex items-center justify-center gap-2"
                >
                  <Package size={20} />
                  نقل الحصاد للمخزون
                </button>
              </div>
            )}
            
            {/* Cost and Profitability Info */}
            <div className="mt-4 pt-4 border-t">
              <h4 className="font-medium text-gray-700 mb-2">التكلفة والربحية</h4>
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="text-gray-600">التكلفة المقدرة:</span>
                  <p className="font-bold text-red-600">{calculateTankCost(selectedTank).toFixed(2)} د.أ</p>
                </div>
                <div>
                  <span className="text-gray-600">الربح المتوقع:</span>
                  <p className="font-bold text-green-600">
                    {calculateProfitability(selectedTank).profit.toFixed(2)} د.أ
                  </p>
                </div>
              </div>
            </div>
          </div>
        </Modal>
      )}

      {/* Tank Edit Modal */}
      {isTankModalOpen && selectedTank && (
        <Modal
          isOpen={isTankModalOpen}
          onClose={() => setIsTankModalOpen(false)}
          title={`تعديل المحبس ${selectedTank.tankNumber} - المرحلة ${selectedTank.stageId}`}
        >
          <div className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">المرحلة الحالية</label>
                <select
                  value={selectedTank.currentPhase}
                  onChange={(e) => {
                    const updatedTank = { ...selectedTank, currentPhase: e.target.value as any };
                    setSelectedTank(updatedTank);
                    
                    // تحديث المرحلة في farmStages
                    const updatedStages = farmStages.map(stage => ({
                      ...stage,
                      tanks: stage.tanks.map(tank => 
                        tank.id === selectedTank.id ? updatedTank : tank
                      )
                    }));
                    setFarmStages(updatedStages);
                  }}
                  className="w-full p-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-green-500 focus:border-green-500"
                >
                  <option value="empty">فارغ</option>
                  <option value="seeding">البذر</option>
                  <option value="germination">الإنبات</option>
                  <option value="growth">النمو</option>
                  <option value="maturation">النضج</option>
                  <option value="harvest">الحصاد</option>
                </select>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">نوع البذور</label>
                <input
                  type="text"
                  value={selectedTank.seedType}
                  onChange={(e) => {
                    const updatedTank = { ...selectedTank, seedType: e.target.value };
                    setSelectedTank(updatedTank);
                    
                    const updatedStages = farmStages.map(stage => ({
                      ...stage,
                      tanks: stage.tanks.map(tank => 
                        tank.id === selectedTank.id ? updatedTank : tank
                      )
                    }));
                    setFarmStages(updatedStages);
                  }}
                  className="w-full p-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-green-500 focus:border-green-500"
                  placeholder="اكتب نوع البذور"
                />
              </div>
            </div>
            
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">تاريخ الزراعة</label>
                <input
                  type="date"
                  value={selectedTank.plantingDate}
                  onChange={(e) => {
                    const updatedTank = { ...selectedTank, plantingDate: e.target.value };
                    setSelectedTank(updatedTank);
                    
                    // حساب تاريخ الحصاد المتوقع (45 يوم للأيسبيرغ، 60 يوم للرومين)
                    if (e.target.value) {
                      const plantingDate = new Date(e.target.value);
                      const daysToHarvest = selectedTank.lettuceType === 'iceberg' ? 45 : 60;
                      const harvestDate = new Date(plantingDate.getTime() + daysToHarvest * 24 * 60 * 60 * 1000);
                      updatedTank.expectedHarvestDate = harvestDate.toISOString().split('T')[0];
                    }
                    
                    const updatedStages = farmStages.map(stage => ({
                      ...stage,
                      tanks: stage.tanks.map(tank => 
                        tank.id === selectedTank.id ? updatedTank : tank
                      )
                    }));
                    setFarmStages(updatedStages);
                  }}
                  className="w-full p-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-green-500 focus:border-green-500"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">تاريخ الحصاد المتوقع</label>
                <input
                  type="date"
                  value={selectedTank.expectedHarvestDate}
                  onChange={(e) => {
                    const updatedTank = { ...selectedTank, expectedHarvestDate: e.target.value };
                    setSelectedTank(updatedTank);
                    
                    const updatedStages = farmStages.map(stage => ({
                      ...stage,
                      tanks: stage.tanks.map(tank => 
                        tank.id === selectedTank.id ? updatedTank : tank
                      )
                    }));
                    setFarmStages(updatedStages);
                  }}
                  className="w-full p-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-green-500 focus:border-green-500"
                />
              </div>
            </div>
            
            <div className="grid grid-cols-3 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">كمية البذور (جرام)</label>
                <input
                  type="number"
                  value={selectedTank.seedQuantity}
                  onChange={(e) => {
                    const updatedTank = { ...selectedTank, seedQuantity: Number(e.target.value) };
                    setSelectedTank(updatedTank);
                    
                    const updatedStages = farmStages.map(stage => ({
                      ...stage,
                      tanks: stage.tanks.map(tank => 
                        tank.id === selectedTank.id ? updatedTank : tank
                      )
                    }));
                    setFarmStages(updatedStages);
                  }}
                  className="w-full p-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-green-500 focus:border-green-500"
                  placeholder="0"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">الإنتاج المتوقع (كجم)</label>
                <input
                  type="number"
                  value={selectedTank.estimatedYield}
                  onChange={(e) => {
                    const updatedTank = { ...selectedTank, estimatedYield: Number(e.target.value) };
                    setSelectedTank(updatedTank);
                    
                    const updatedStages = farmStages.map(stage => ({
                      ...stage,
                      tanks: stage.tanks.map(tank => 
                        tank.id === selectedTank.id ? updatedTank : tank
                      )
                    }));
                    setFarmStages(updatedStages);
                  }}
                  className="w-full p-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-green-500 focus:border-green-500"
                  placeholder="0"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">حالة النمو</label>
                <select
                  value={selectedTank.growthStatus}
                  onChange={(e) => {
                    const updatedTank = { ...selectedTank, growthStatus: e.target.value as any };
                    setSelectedTank(updatedTank);
                    
                    const updatedStages = farmStages.map(stage => ({
                      ...stage,
                      tanks: stage.tanks.map(tank => 
                        tank.id === selectedTank.id ? updatedTank : tank
                      )
                    }));
                    setFarmStages(updatedStages);
                  }}
                  className="w-full p-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-green-500 focus:border-green-500"
                >
                  <option value="excellent">ممتاز</option>
                  <option value="good">جيد</option>
                  <option value="average">متوسط</option>
                  <option value="poor">ضعيف</option>
                </select>
              </div>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">الملاحظات</label>
              <textarea
                value={selectedTank.notes}
                onChange={(e) => {
                  const updatedTank = { ...selectedTank, notes: e.target.value };
                  setSelectedTank(updatedTank);
                  
                  const updatedStages = farmStages.map(stage => ({
                    ...stage,
                    tanks: stage.tanks.map(tank => 
                      tank.id === selectedTank.id ? updatedTank : tank
                    )
                  }));
                  setFarmStages(updatedStages);
                }}
                className="w-full p-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-green-500 focus:border-green-500"
                rows={3}
                placeholder="اكتب ملاحظات حول هذا المحبس"
              />
            </div>
            
            <div className="flex gap-2 pt-4">
              <button
                onClick={() => setIsTankModalOpen(false)}
                className="flex-1 bg-gradient-to-r from-green-500 to-emerald-500 text-white py-2 px-4 rounded-lg hover:from-green-600 hover:to-emerald-600 transition-all duration-200 font-medium"
              >
                حفظ التغييرات
              </button>
              <button
                onClick={() => setIsTankModalOpen(false)}
                className="flex-1 bg-gray-500 text-white py-2 px-4 rounded-lg hover:bg-gray-600 transition-all duration-200 font-medium"
              >
                إلغاء
              </button>
            </div>
          </div>
        </Modal>
      )}

      {/* Stage Detail Modal */}
      {selectedStage && (
        <Modal
          isOpen={!!selectedStage}
          onClose={() => setSelectedStage(null)}
          title={`تفاصيل ${selectedStage.name}`}
        >
          <div className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">حالة المرحلة</label>
                <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                  selectedStage.status === 'active' ? 'bg-green-100 text-green-800' :
                  selectedStage.status === 'maintenance' ? 'bg-yellow-100 text-yellow-800' :
                  'bg-gray-100 text-gray-800'
                }`}>
                  {selectedStage.status === 'active' ? 'نشط' : 
                   selectedStage.status === 'maintenance' ? 'صيانة' : 'غير نشط'}
                </span>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">المشرف</label>
                <p className="text-sm text-gray-900">{selectedStage.supervisor || 'غير محدد'}</p>
              </div>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">المحابس</label>
              <div className="grid grid-cols-5 gap-2">
                {selectedStage.tanks.map((tank) => (
                  <div
                    key={tank.id}
                    className={`p-3 rounded-lg border-2 text-center cursor-pointer hover:opacity-80 transition-opacity ${
                      tank.currentPhase === 'empty' ? 'bg-gray-100 border-gray-300' :
                      tank.lettuceType === 'romaine' ? 'bg-orange-100 border-orange-500' :
                      'bg-blue-100 border-blue-500'
                    }`}
                    onClick={() => setSelectedTank(tank)}
                  >
                    <div className="text-xs font-bold">{tank.tankNumber}</div>
                    <div className="text-xs mt-1">{tank.lettuceType === 'iceberg' ? 'أيسبيرغ' : 'رومين'}</div>
                    <div className={`text-xs mt-1 px-1 py-0.5 rounded text-white ${getPhaseColor(tank.currentPhase)}`}>
                      {getPhaseName(tank.currentPhase)}
                    </div>
                  </div>
                ))}
              </div>
            </div>
            
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">المحابس النشطة</label>
                <p className="text-sm text-gray-900">{selectedStage.tanks.filter(t => t.currentPhase !== 'empty').length}/5</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">جاهز للحصاد</label>
                <p className="text-sm text-red-600 font-medium">{selectedStage.tanks.filter(t => t.currentPhase === 'harvest').length}</p>
              </div>
            </div>
            
            {selectedStage.notes && (
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">الملاحظات</label>
                <p className="text-sm text-gray-900">{selectedStage.notes}</p>
              </div>
            )}
            
            <div className="flex gap-2 pt-4">
              <button
                onClick={() => setSelectedStage(null)}
                className="flex-1 bg-gradient-to-r from-green-500 to-emerald-500 text-white py-2 px-4 rounded-lg hover:from-green-600 hover:to-emerald-600 transition-all duration-200 font-medium"
              >
                تعديل المرحلة
              </button>
            </div>
          </div>
        </Modal>
      )}

      {/* Worker Management Modal */}
      {isWorkerModalOpen && (
        <Modal
          isOpen={isWorkerModalOpen}
          onClose={() => setIsWorkerModalOpen(false)}
          title="إدارة العمالة"
        >
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <h3 className="text-lg font-bold text-gray-800">قائمة العمالة</h3>
              <button
                onClick={() => {
                  const newWorker: FarmWorker = {
                    id: Date.now(),
                    name: 'عامل جديد',
                    position: 'عامل زراعي',
                    assignedStages: [],
                    salary: 0,
                    phone: '',
                    startDate: new Date().toISOString().split('T')[0],
                    status: 'active'
                  };
                  setFarmWorkers([...farmWorkers, newWorker]);
                }}
                className="bg-gradient-to-r from-green-500 to-emerald-500 text-white px-4 py-2 rounded-lg hover:from-green-600 hover:to-emerald-600 transition-all duration-200 font-medium"
              >
                <Plus size={16} className="inline mr-2" />
                إضافة عامل
              </button>
            </div>
            
            <div className="max-h-96 overflow-y-auto">
              {farmWorkers.length === 0 ? (
                <div className="text-center py-8 text-gray-500">
                  <Users size={48} className="mx-auto mb-4 text-gray-300" />
                  <p>لا يوجد عمالة مسجلة</p>
                </div>
              ) : (
                <div className="space-y-3">
                  {farmWorkers.map((worker) => (
                    <div key={worker.id} className="bg-gray-50 rounded-lg p-4">
                      <div className="flex justify-between items-start">
                        <div className="flex-1">
                          <div className="grid grid-cols-2 gap-3">
                            <input
                              type="text"
                              value={worker.name}
                              onChange={(e) => {
                                const updatedWorkers = farmWorkers.map(w => 
                                  w.id === worker.id ? { ...w, name: e.target.value } : w
                                );
                                setFarmWorkers(updatedWorkers);
                              }}
                              className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 font-medium"
                              placeholder="اسم العامل"
                            />
                            <input
                              type="text"
                              value={worker.position}
                              onChange={(e) => {
                                const updatedWorkers = farmWorkers.map(w => 
                                  w.id === worker.id ? { ...w, position: e.target.value } : w
                                );
                                setFarmWorkers(updatedWorkers);
                              }}
                              className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500"
                              placeholder="المنصب"
                            />
                            <input
                              type="number"
                              value={worker.salary}
                              onChange={(e) => {
                                const updatedWorkers = farmWorkers.map(w => 
                                  w.id === worker.id ? { ...w, salary: Number(e.target.value) } : w
                                );
                                setFarmWorkers(updatedWorkers);
                              }}
                              className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500"
                              placeholder="الراتب"
                            />
                            <input
                              type="text"
                              value={worker.phone}
                              onChange={(e) => {
                                const updatedWorkers = farmWorkers.map(w => 
                                  w.id === worker.id ? { ...w, phone: e.target.value } : w
                                );
                                setFarmWorkers(updatedWorkers);
                              }}
                              className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500"
                              placeholder="رقم الهاتف"
                            />
                          </div>
                        </div>
                        <button
                          onClick={() => {
                            const updatedWorkers = farmWorkers.filter(w => w.id !== worker.id);
                            setFarmWorkers(updatedWorkers);
                          }}
                          className="text-red-600 hover:text-red-800 p-2 hover:bg-red-50 rounded-lg transition-all duration-200"
                        >
                          <Trash2 size={16} />
                        </button>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
            
            <div className="pt-4 border-t">
              <div className="grid grid-cols-3 gap-4 text-center">
                <div>
                  <p className="text-sm text-gray-600">إجمالي العمالة</p>
                  <p className="text-lg font-bold text-green-600">{farmWorkers.length}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-600">العمالة النشطة</p>
                  <p className="text-lg font-bold text-blue-600">{farmWorkers.filter(w => w.status === 'active').length}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-600">إجمالي الرواتب</p>
                  <p className="text-lg font-bold text-purple-600">{farmWorkers.reduce((sum, w) => sum + w.salary, 0)} د.أ</p>
                </div>
              </div>
            </div>
          </div>
        </Modal>
      )}

      {/* Expense Management Modal */}
      {isExpenseModalOpen && (
        <Modal
          isOpen={isExpenseModalOpen}
          onClose={() => setIsExpenseModalOpen(false)}
          title="إدارة التكاليف"
        >
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <h3 className="text-lg font-bold text-gray-800">سجل التكاليف</h3>
              <button
                onClick={() => {
                  const newExpense: FarmExpense = {
                    id: Date.now(),
                    date: new Date().toISOString().split('T')[0],
                    type: 'seeds',
                    description: 'مصروف جديد',
                    amount: 0,
                    category: 'عام'
                  };
                  setFarmExpenses([...farmExpenses, newExpense]);
                }}
                className="bg-gradient-to-r from-purple-500 to-purple-600 text-white px-4 py-2 rounded-lg hover:from-purple-600 hover:to-purple-700 transition-all duration-200 font-medium"
              >
                <Plus size={16} className="inline mr-2" />
                إضافة مصروف
              </button>
            </div>
            
            <div className="max-h-96 overflow-y-auto">
              {farmExpenses.length === 0 ? (
                <div className="text-center py-8 text-gray-500">
                  <DollarSign size={48} className="mx-auto mb-4 text-gray-300" />
                  <p>لا توجد تكاليف مسجلة</p>
                </div>
              ) : (
                <div className="space-y-3">
                  {farmExpenses.map((expense) => (
                    <div key={expense.id} className="bg-gray-50 rounded-lg p-4">
                      <div className="flex justify-between items-start">
                        <div className="flex-1">
                          <div className="grid grid-cols-2 gap-3">
                            <input
                              type="date"
                              value={expense.date}
                              onChange={(e) => {
                                const updatedExpenses = farmExpenses.map(exp => 
                                  exp.id === expense.id ? { ...exp, date: e.target.value } : exp
                                );
                                setFarmExpenses(updatedExpenses);
                              }}
                              className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
                            />
                            <select
                              value={expense.type}
                              onChange={(e) => {
                                const updatedExpenses = farmExpenses.map(exp => 
                                  exp.id === expense.id ? { ...exp, type: e.target.value as any } : exp
                                );
                                setFarmExpenses(updatedExpenses);
                              }}
                              className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
                            >
                              <option value="seeds">البذور</option>
                              <option value="fertilizer">الأسمدة</option>
                              <option value="water">المياه</option>
                              <option value="labor">العمالة</option>
                              <option value="equipment">المعدات</option>
                              <option value="maintenance">الصيانة</option>
                              <option value="other">أخرى</option>
                            </select>
                            <input
                              type="text"
                              value={expense.description}
                              onChange={(e) => {
                                const updatedExpenses = farmExpenses.map(exp => 
                                  exp.id === expense.id ? { ...exp, description: e.target.value } : exp
                                );
                                setFarmExpenses(updatedExpenses);
                              }}
                              className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
                              placeholder="وصف المصروف"
                            />
                            <input
                              type="number"
                              value={expense.amount}
                              onChange={(e) => {
                                const updatedExpenses = farmExpenses.map(exp => 
                                  exp.id === expense.id ? { ...exp, amount: Number(e.target.value) } : exp
                                );
                                setFarmExpenses(updatedExpenses);
                              }}
                              className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
                              placeholder="المبلغ"
                            />
                          </div>
                        </div>
                        <button
                          onClick={() => {
                            const updatedExpenses = farmExpenses.filter(exp => exp.id !== expense.id);
                            setFarmExpenses(updatedExpenses);
                          }}
                          className="text-red-600 hover:text-red-800 p-2 hover:bg-red-50 rounded-lg transition-all duration-200"
                        >
                          <Trash2 size={16} />
                        </button>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
            
            <div className="pt-4 border-t">
              <div className="grid grid-cols-2 gap-4 text-center">
                <div>
                  <p className="text-sm text-gray-600">إجمالي التكاليف</p>
                  <p className="text-lg font-bold text-red-600">{farmExpenses.reduce((sum, exp) => sum + exp.amount, 0).toLocaleString()} د.أ</p>
                </div>
                <div>
                  <p className="text-sm text-gray-600">تكاليف هذا الشهر</p>
                  <p className="text-lg font-bold text-orange-600">
                    {farmExpenses
                      .filter(exp => exp.date.startsWith(new Date().toISOString().substring(0, 7)))
                      .reduce((sum, exp) => sum + exp.amount, 0)
                      .toLocaleString()} د.أ
                  </p>
                </div>
              </div>
            </div>
          </div>
        </Modal>
      )}

      {/* Reports Modal */}
      {isReportModalOpen && (
        <Modal
          isOpen={isReportModalOpen}
          onClose={() => setIsReportModalOpen(false)}
          title="تقارير المزرعة"
        >
          <div className="space-y-6">
            {/* Production Report */}
            <div className="bg-green-50 rounded-lg p-4">
              <h3 className="text-lg font-bold text-green-800 mb-4">تقرير الإنتاج</h3>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <p className="text-sm text-gray-600">المحابس النشطة</p>
                  <p className="text-xl font-bold text-green-600">{stats.activeTanks}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-600">جاهز للحصاد</p>
                  <p className="text-xl font-bold text-red-600">{stats.readyForHarvest}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-600">خس أيسبيرغ</p>
                  <p className="text-xl font-bold text-blue-600">{stats.icebergTanks}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-600">خس رومين</p>
                  <p className="text-xl font-bold text-orange-600">{stats.romaineTanks}</p>
                </div>
              </div>
            </div>

            {/* Resource Usage Report */}
            <div className="bg-blue-50 rounded-lg p-4">
              <h3 className="text-lg font-bold text-blue-800 mb-4">تقرير استهلاك الموارد</h3>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <p className="text-sm text-gray-600">استهلاك الماء</p>
                  <p className="text-xl font-bold text-blue-600">{stats.totalWaterUsage.toLocaleString()} لتر</p>
                </div>
                <div>
                  <p className="text-sm text-gray-600">استهلاك الأسمدة</p>
                  <p className="text-xl font-bold text-green-600">{stats.totalFertilizerUsage.toLocaleString()} كجم</p>
                </div>
              </div>
            </div>

            {/* Financial Report */}
            <div className="bg-purple-50 rounded-lg p-4">
              <h3 className="text-lg font-bold text-purple-800 mb-4">التقرير المالي</h3>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <p className="text-sm text-gray-600">إجمالي التكاليف</p>
                  <p className="text-xl font-bold text-red-600">{stats.totalExpenses.toLocaleString()} د.أ</p>
                </div>
                <div>
                  <p className="text-sm text-gray-600">الإنتاج المتوقع</p>
                  <p className="text-xl font-bold text-green-600">{stats.estimatedYield.toLocaleString()} كجم</p>
                </div>
              </div>
            </div>

            {/* Export Button */}
            <div className="flex justify-center pt-4">
              <button
                onClick={() => {
                  const reportData = {
                    date: new Date().toISOString(),
                    stats,
                    stages: farmStages,
                    workers: farmWorkers,
                    expenses: farmExpenses
                  };
                  const blob = new Blob([JSON.stringify(reportData, null, 2)], { type: 'application/json' });
                  const url = URL.createObjectURL(blob);
                  const a = document.createElement('a');
                  a.href = url;
                  a.download = `farm_report_${new Date().toISOString().split('T')[0]}.json`;
                  a.click();
                  URL.revokeObjectURL(url);
                }}
                className="bg-gradient-to-r from-orange-500 to-orange-600 text-white px-6 py-3 rounded-lg hover:from-orange-600 hover:to-orange-700 transition-all duration-200 font-medium flex items-center gap-2"
              >
                <Download size={20} />
                تصدير التقرير
              </button>
            </div>
          </div>
        </Modal>
      )}

      {/* Irrigation Schedule Modal */}
      {isIrrigationModalOpen && selectedTank && (
        <Modal
          isOpen={isIrrigationModalOpen}
          onClose={() => setIsIrrigationModalOpen(false)}
          title={`جدولة الري - المحبس ${selectedTank.tankNumber}`}
        >
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <h3 className="text-lg font-bold text-gray-800">جدول الري</h3>
              <button
                onClick={() => {
                  const newIrrigation: IrrigationSchedule = {
                    id: Date.now(),
                    date: new Date().toISOString().split('T')[0],
                    time: '08:00',
                    duration: 30,
                    waterAmount: 100,
                    completed: false,
                    notes: ''
                  };
                  
                  const updatedTank = {
                    ...selectedTank,
                    irrigationSchedule: [...selectedTank.irrigationSchedule, newIrrigation]
                  };
                  
                  setSelectedTank(updatedTank);
                  
                  const updatedStages = farmStages.map(stage => ({
                    ...stage,
                    tanks: stage.tanks.map(tank => 
                      tank.id === selectedTank.id ? updatedTank : tank
                    )
                  }));
                  setFarmStages(updatedStages);
                }}
                className="bg-gradient-to-r from-blue-500 to-blue-600 text-white px-4 py-2 rounded-lg hover:from-blue-600 hover:to-blue-700 transition-all duration-200 font-medium"
              >
                <Plus size={16} className="inline mr-2" />
                إضافة جلسة ري
              </button>
            </div>
            
            <div className="max-h-96 overflow-y-auto">
              {selectedTank.irrigationSchedule.length === 0 ? (
                <div className="text-center py-8 text-gray-500">
                  <Droplets size={48} className="mx-auto mb-4 text-gray-300" />
                  <p>لا يوجد جدول ري</p>
                </div>
              ) : (
                <div className="space-y-3">
                  {selectedTank.irrigationSchedule.map((irrigation) => (
                    <div key={irrigation.id} className="bg-gray-50 rounded-lg p-4">
                      <div className="flex justify-between items-start">
                        <div className="flex-1">
                          <div className="grid grid-cols-2 gap-3">
                            <input
                              type="date"
                              value={irrigation.date}
                              onChange={(e) => {
                                const updatedSchedule = selectedTank.irrigationSchedule.map(irr => 
                                  irr.id === irrigation.id ? { ...irr, date: e.target.value } : irr
                                );
                                const updatedTank = { ...selectedTank, irrigationSchedule: updatedSchedule };
                                setSelectedTank(updatedTank);
                                
                                const updatedStages = farmStages.map(stage => ({
                                  ...stage,
                                  tanks: stage.tanks.map(tank => 
                                    tank.id === selectedTank.id ? updatedTank : tank
                                  )
                                }));
                                setFarmStages(updatedStages);
                              }}
                              className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                            />
                            <input
                              type="time"
                              value={irrigation.time}
                              onChange={(e) => {
                                const updatedSchedule = selectedTank.irrigationSchedule.map(irr => 
                                  irr.id === irrigation.id ? { ...irr, time: e.target.value } : irr
                                );
                                const updatedTank = { ...selectedTank, irrigationSchedule: updatedSchedule };
                                setSelectedTank(updatedTank);
                                
                                const updatedStages = farmStages.map(stage => ({
                                  ...stage,
                                  tanks: stage.tanks.map(tank => 
                                    tank.id === selectedTank.id ? updatedTank : tank
                                  )
                                }));
                                setFarmStages(updatedStages);
                              }}
                              className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                            />
                            <input
                              type="number"
                              value={irrigation.duration}
                              onChange={(e) => {
                                const updatedSchedule = selectedTank.irrigationSchedule.map(irr => 
                                  irr.id === irrigation.id ? { ...irr, duration: Number(e.target.value) } : irr
                                );
                                const updatedTank = { ...selectedTank, irrigationSchedule: updatedSchedule };
                                setSelectedTank(updatedTank);
                                
                                const updatedStages = farmStages.map(stage => ({
                                  ...stage,
                                  tanks: stage.tanks.map(tank => 
                                    tank.id === selectedTank.id ? updatedTank : tank
                                  )
                                }));
                                setFarmStages(updatedStages);
                              }}
                              className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                              placeholder="المدة (دقيقة)"
                            />
                            <input
                              type="number"
                              value={irrigation.waterAmount}
                              onChange={(e) => {
                                const updatedSchedule = selectedTank.irrigationSchedule.map(irr => 
                                  irr.id === irrigation.id ? { ...irr, waterAmount: Number(e.target.value) } : irr
                                );
                                const updatedTank = { ...selectedTank, irrigationSchedule: updatedSchedule };
                                setSelectedTank(updatedTank);
                                
                                const updatedStages = farmStages.map(stage => ({
                                  ...stage,
                                  tanks: stage.tanks.map(tank => 
                                    tank.id === selectedTank.id ? updatedTank : tank
                                  )
                                }));
                                setFarmStages(updatedStages);
                              }}
                              className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                              placeholder="كمية الماء (لتر)"
                            />
                          </div>
                          <div className="mt-2 flex items-center gap-2">
                            <input
                              type="checkbox"
                              checked={irrigation.completed}
                              onChange={(e) => {
                                const updatedSchedule = selectedTank.irrigationSchedule.map(irr => 
                                  irr.id === irrigation.id ? { ...irr, completed: e.target.checked } : irr
                                );
                                const updatedTank = { ...selectedTank, irrigationSchedule: updatedSchedule };
                                setSelectedTank(updatedTank);
                                
                                const updatedStages = farmStages.map(stage => ({
                                  ...stage,
                                  tanks: stage.tanks.map(tank => 
                                    tank.id === selectedTank.id ? updatedTank : tank
                                  )
                                }));
                                setFarmStages(updatedStages);
                              }}
                              className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                            />
                            <label className="text-sm text-gray-700">مكتمل</label>
                          </div>
                        </div>
                        <button
                          onClick={() => {
                            const updatedSchedule = selectedTank.irrigationSchedule.filter(irr => irr.id !== irrigation.id);
                            const updatedTank = { ...selectedTank, irrigationSchedule: updatedSchedule };
                            setSelectedTank(updatedTank);
                            
                            const updatedStages = farmStages.map(stage => ({
                              ...stage,
                              tanks: stage.tanks.map(tank => 
                                tank.id === selectedTank.id ? updatedTank : tank
                              )
                            }));
                            setFarmStages(updatedStages);
                          }}
                          className="text-red-600 hover:text-red-800 p-2 hover:bg-red-50 rounded-lg transition-all duration-200"
                        >
                          <Trash2 size={16} />
                        </button>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        </Modal>
      )}

      {/* Fertilizer Schedule Modal */}
      {isFertilizerModalOpen && selectedTank && (
        <Modal
          isOpen={isFertilizerModalOpen}
          onClose={() => setIsFertilizerModalOpen(false)}
          title={<span className="text-xl font-extrabold bg-gradient-to-r from-green-600 via-blue-600 to-purple-600 bg-clip-text text-transparent drop-shadow">جدولة التسميد - المحبس {selectedTank.tankNumber}</span>}
        >
          <div className="space-y-4 bg-gradient-to-br from-green-50 via-blue-50 to-purple-50 rounded-2xl p-2">
            <div className="flex justify-between items-center mb-2">
              <h3 className="text-lg font-bold bg-gradient-to-r from-green-600 via-blue-600 to-purple-600 bg-clip-text text-transparent">جدول التسميد</h3>
              <button
                onClick={() => {
                  const newFertilizer: FertilizerSchedule = {
                    id: Date.now(),
                    date: new Date().toISOString().split('T')[0],
                    fertilizerType: 'سماد نيتروجيني',
                    amount: 1,
                    completed: false,
                    notes: ''
                  };
                  const updatedTank = {
                    ...selectedTank,
                    fertilizerSchedule: [...selectedTank.fertilizerSchedule, newFertilizer]
                  };
                  setSelectedTank(updatedTank);
                  const updatedStages = farmStages.map(stage => ({
                    ...stage,
                    tanks: stage.tanks.map(tank => 
                      tank.id === selectedTank.id ? updatedTank : tank
                    )
                  }));
                  setFarmStages(updatedStages);
                }}
                className="bg-gradient-to-r from-purple-500 via-blue-500 to-green-500 text-white px-5 py-2 rounded-xl shadow-lg hover:from-green-600 hover:to-purple-700 transition-all duration-300 font-bold text-base flex items-center gap-2"
              >
                <Plus size={18} className="inline mr-2 animate-spin-slow" />
                إضافة جلسة تسميد
              </button>
            </div>
            <div className="max-h-96 overflow-y-auto">
              {selectedTank.fertilizerSchedule.length === 0 ? (
                <div className="text-center py-8 text-gray-500">
                  <Zap size={48} className="mx-auto mb-4 text-gray-300" />
                  <p>لا يوجد جدول تسميد</p>
                </div>
              ) : (
                <div className="space-y-4">
                  {selectedTank.fertilizerSchedule.map((fertilizer) => (
                    <div key={fertilizer.id} className="bg-gradient-to-r from-green-100 via-blue-100 to-purple-100 rounded-2xl p-4 shadow-md border-2 border-green-200 hover:shadow-lg transition-all duration-200">
                      <div className="flex justify-between items-start gap-2">
                        <div className="flex-1">
                          <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
                            <input
                              type="date"
                              value={fertilizer.date}
                              onChange={(e) => {
                                const updatedSchedule = selectedTank.fertilizerSchedule.map(fert => 
                                  fert.id === fertilizer.id ? { ...fert, date: e.target.value } : fert
                                );
                                const updatedTank = { ...selectedTank, fertilizerSchedule: updatedSchedule };
                                setSelectedTank(updatedTank);
                                const updatedStages = farmStages.map(stage => ({
                                  ...stage,
                                  tanks: stage.tanks.map(tank => 
                                    tank.id === selectedTank.id ? updatedTank : tank
                                  )
                                }));
                                setFarmStages(updatedStages);
                              }}
                              className="input-field rounded-xl border-2 border-blue-200 shadow focus:border-blue-400 focus:ring-2 focus:ring-blue-200 transition-all duration-200"
                            />
                            <input
                              type="text"
                              value={fertilizer.fertilizerType}
                              onChange={(e) => {
                                const updatedSchedule = selectedTank.fertilizerSchedule.map(fert => 
                                  fert.id === fertilizer.id ? { ...fert, fertilizerType: e.target.value } : fert
                                );
                                const updatedTank = { ...selectedTank, fertilizerSchedule: updatedSchedule };
                                setSelectedTank(updatedTank);
                                const updatedStages = farmStages.map(stage => ({
                                  ...stage,
                                  tanks: stage.tanks.map(tank => 
                                    tank.id === selectedTank.id ? updatedTank : tank
                                  )
                                }));
                                setFarmStages(updatedStages);
                              }}
                              className="input-field rounded-xl border-2 border-green-200 shadow focus:border-green-400 focus:ring-2 focus:ring-green-200 transition-all duration-200"
                              placeholder="نوع السماد"
                            />
                            <input
                              type="number"
                              value={fertilizer.amount}
                              onChange={(e) => {
                                const updatedSchedule = selectedTank.fertilizerSchedule.map(fert => 
                                  fert.id === fertilizer.id ? { ...fert, amount: Number(e.target.value) } : fert
                                );
                                const updatedTank = { ...selectedTank, fertilizerSchedule: updatedSchedule };
                                setSelectedTank(updatedTank);
                                const updatedStages = farmStages.map(stage => ({
                                  ...stage,
                                  tanks: stage.tanks.map(tank => 
                                    tank.id === selectedTank.id ? updatedTank : tank
                                  )
                                }));
                                setFarmStages(updatedStages);
                              }}
                              className="input-field rounded-xl border-2 border-indigo-200 shadow focus:border-indigo-400 focus:ring-2 focus:ring-indigo-200 transition-all duration-200"
                              placeholder="الكمية (كجم)"
                            />
                            <input
                              type="text"
                              value={fertilizer.notes}
                              onChange={(e) => {
                                const updatedSchedule = selectedTank.fertilizerSchedule.map(fert => 
                                  fert.id === fertilizer.id ? { ...fert, notes: e.target.value } : fert
                                );
                                const updatedTank = { ...selectedTank, fertilizerSchedule: updatedSchedule };
                                setSelectedTank(updatedTank);
                                const updatedStages = farmStages.map(stage => ({
                                  ...stage,
                                  tanks: stage.tanks.map(tank => 
                                    tank.id === selectedTank.id ? updatedTank : tank
                                  )
                                }));
                                setFarmStages(updatedStages);
                              }}
                              className="input-field rounded-xl border-2 border-purple-200 shadow focus:border-purple-400 focus:ring-2 focus:ring-purple-200 transition-all duration-200"
                              placeholder="ملاحظات"
                            />
                          </div>
                          <div className="mt-2 flex items-center gap-2">
                            <input
                              type="checkbox"
                              checked={fertilizer.completed}
                              onChange={(e) => {
                                const updatedSchedule = selectedTank.fertilizerSchedule.map(fert => 
                                  fert.id === fertilizer.id ? { ...fert, completed: e.target.checked } : fert
                                );
                                const updatedTank = { ...selectedTank, fertilizerSchedule: updatedSchedule };
                                setSelectedTank(updatedTank);
                                const updatedStages = farmStages.map(stage => ({
                                  ...stage,
                                  tanks: stage.tanks.map(tank => 
                                    tank.id === selectedTank.id ? updatedTank : tank
                                  )
                                }));
                                setFarmStages(updatedStages);
                              }}
                              className="w-4 h-4 text-purple-600 border-gray-300 rounded focus:ring-purple-500"
                            />
                            <label className="text-sm text-gray-700">مكتمل</label>
                          </div>
                        </div>
                        <button
                          onClick={() => {
                            const updatedSchedule = selectedTank.fertilizerSchedule.filter(fert => fert.id !== fertilizer.id);
                            const updatedTank = { ...selectedTank, fertilizerSchedule: updatedSchedule };
                            setSelectedTank(updatedTank);
                            const updatedStages = farmStages.map(stage => ({
                              ...stage,
                              tanks: stage.tanks.map(tank => 
                                tank.id === selectedTank.id ? updatedTank : tank
                              )
                            }));
                            setFarmStages(updatedStages);
                          }}
                          className="text-red-600 hover:text-red-800 p-2 hover:bg-red-100 rounded-lg transition-all duration-200 shadow"
                        >
                          <Trash2 size={16} />
                        </button>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        </Modal>
      )}
    </div>
  );
};

export default FarmView;