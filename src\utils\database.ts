// ===== إدارة قاعدة البيانات =====

import {
  Zone,
  IrrigationValve,
  Spraying,
  Fertilization,
  InventoryItem,
  InventoryMovement,
  Employee,
  Supplier,
  Advance,
  LocalSale,
  IrrigationSchedule,
  DashboardStats,
  Transaction
} from '../types';

// بيانات تجريبية للمراحل الزراعية - 24 مرحلة
const sampleZones: Zone[] = [
  {
    id: 1,
    name: 'المرحلة الأولى',
    status: 'active',
    plantingDate: '2024-01-15',
    harvestDate: '2024-03-15',
    lettuceType: 'iceberg',
    irrigationStatus: 'scheduled',
    lastIrrigation: '2024-01-20',
    nextIrrigation: '2024-01-25',
    notes: 'مرحلة تجريبية للخس الأيسبيرغ',
    valves: [
      { id: 1, zoneId: 1, valveNumber: 1, lettuceType: 'iceberg', status: 'active', lastIrrigation: '2024-01-20', nextIrrigation: '2024-01-25' },
      { id: 2, zoneId: 1, valveNumber: 2, lettuceType: 'iceberg', status: 'inactive', lastIrrigation: '2024-01-18', nextIrrigation: '2024-01-26' }
    ]
  },
  {
    id: 2,
    name: 'المرحلة الثانية',
    status: 'active',
    plantingDate: '2024-01-20',
    harvestDate: '2024-03-20',
    lettuceType: 'romaine',
    irrigationStatus: 'completed',
    lastIrrigation: '2024-01-25',
    nextIrrigation: '2024-01-30',
    notes: 'مرحلة خس رومين عالي الجودة',
    valves: [
      { id: 3, zoneId: 2, valveNumber: 1, lettuceType: 'romaine', status: 'active', lastIrrigation: '2024-01-25', nextIrrigation: '2024-01-30' },
      { id: 4, zoneId: 2, valveNumber: 2, lettuceType: 'romaine', status: 'active', lastIrrigation: '2024-01-24', nextIrrigation: '2024-01-31' }
    ]
  },
  // {
  //   id: 3,
  //   name: 'المرحلة الثالثة',
  //   cropType: 'خس أيسبيرغ',
  //   area: 480,
  //   status: 'active',
  //   plantingDate: '2024-02-01',
  //   expectedHarvestDate: '2024-04-01',
  //   notes: 'مرحلة خس أيسبيرغ للتصدير',
  //   valves: [
  //     { id: 'valve-3-1', number: 1, type: 'iceberg', isActive: true },
  //     { id: 'valve-3-2', number: 2, type: 'iceberg', isActive: true },
  //     { id: 'valve-3-3', number: 3, type: 'iceberg', isActive: true },
  //     { id: 'valve-3-4', number: 4, type: 'iceberg', isActive: false },
  //     { id: 'valve-3-5', number: 5, type: 'romaine', isActive: true }
  //   ]
  // },
  // {
  //   id: 'zone-4',
  //   name: 'المرحلة الرابعة',
  //   cropType: 'خس رومين',
  //   area: 520,
  //   status: 'active',
  //   plantingDate: '2024-02-10',
  //   expectedHarvestDate: '2024-04-10',
  //   notes: 'مرحلة رومين للأسواق المحلية',
  //   valves: [
  //     { id: 'valve-4-1', number: 1, type: 'romaine', isActive: true },
  //     { id: 'valve-4-2', number: 2, type: 'romaine', isActive: false },
  //     { id: 'valve-4-3', number: 3, type: 'romaine', isActive: true },
  //     { id: 'valve-4-4', number: 4, type: 'iceberg', isActive: true },
  //     { id: 'valve-4-5', number: 5, type: 'romaine', isActive: true }
  //   ]
  // },
  // {
  //   id: 'zone-5',
  //   name: 'المرحلة الخامسة',
  //   cropType: 'خس أيسبيرغ',
  //   area: 490,
  //   status: 'active',
  //   plantingDate: '2024-02-15',
  //   expectedHarvestDate: '2024-04-15',
  //   notes: 'مرحلة أيسبيرغ عالي الجودة',
  //   valves: [
  //     { id: 'valve-5-1', number: 1, type: 'iceberg', isActive: true },
  //     { id: 'valve-5-2', number: 2, type: 'iceberg', isActive: true },
  //     { id: 'valve-5-3', number: 3, type: 'iceberg', isActive: false },
  //     { id: 'valve-5-4', number: 4, type: 'iceberg', isActive: true },
  //     { id: 'valve-5-5', number: 5, type: 'romaine', isActive: false }
  //   ]
  // },
  // {
  //   id: 'zone-6',
  //   name: 'المرحلة السادسة',
  //   cropType: 'خس رومين',
  //   area: 510,
  //   status: 'active',
  //   plantingDate: '2024-02-20',
  //   expectedHarvestDate: '2024-04-20',
  //   notes: 'مرحلة رومين للتصدير',
  //   valves: [
  //     { id: 'valve-6-1', number: 1, type: 'romaine', isActive: true },
  //     { id: 'valve-6-2', number: 2, type: 'romaine', isActive: true },
  //     { id: 'valve-6-3', number: 3, type: 'romaine', isActive: true },
  //     { id: 'valve-6-4', number: 4, type: 'romaine', isActive: false },
  //     { id: 'valve-6-5', number: 5, type: 'iceberg', isActive: true }
  //   ]
  // },
  // {
  //   id: 'zone-7',
  //   name: 'المرحلة السابعة',
  //   cropType: 'خس أيسبيرغ',
  //   area: 470,
  //   status: 'active',
  //   plantingDate: '2024-03-01',
  //   expectedHarvestDate: '2024-05-01',
  //   notes: 'مرحلة أيسبيرغ للأسواق المحلية',
  //   valves: [
  //     { id: 'valve-7-1', number: 1, type: 'iceberg', isActive: true },
  //     { id: 'valve-7-2', number: 2, type: 'iceberg', isActive: false },
  //     { id: 'valve-7-3', number: 3, type: 'iceberg', isActive: true },
  //     { id: 'valve-7-4', number: 4, type: 'iceberg', isActive: true },
  //     { id: 'valve-7-5', number: 5, type: 'romaine', isActive: true }
  //   ]
  // },
  // {
  //   id: 'zone-8',
  //   name: 'المرحلة الثامنة',
  //   cropType: 'خس رومين',
  //   area: 530,
  //   status: 'active',
  //   plantingDate: '2024-03-05',
  //   expectedHarvestDate: '2024-05-05',
  //   notes: 'مرحلة رومين عالي الجودة',
  //   valves: [
  //     { id: 'valve-8-1', number: 1, type: 'romaine', isActive: true },
  //     { id: 'valve-8-2', number: 2, type: 'romaine', isActive: true },
  //     { id: 'valve-8-3', number: 3, type: 'romaine', isActive: false },
  //     { id: 'valve-8-4', number: 4, type: 'romaine', isActive: true },
  //     { id: 'valve-8-5', number: 5, type: 'iceberg', isActive: false }
  //   ]
  // },
  // {
  //   id: 'zone-9',
  //   name: 'المرحلة التاسعة',
  //   cropType: 'خس أيسبيرغ',
  //   area: 480,
  //   status: 'active',
  //   plantingDate: '2024-03-10',
  //   expectedHarvestDate: '2024-05-10',
  //   notes: 'مرحلة أيسبيرغ للتصدير',
  //   valves: [
  //     { id: 'valve-9-1', number: 1, type: 'iceberg', isActive: true },
  //     { id: 'valve-9-2', number: 2, type: 'iceberg', isActive: true },
  //     { id: 'valve-9-3', number: 3, type: 'iceberg', isActive: true },
  //     { id: 'valve-9-4', number: 4, type: 'iceberg', isActive: false },
  //     { id: 'valve-9-5', number: 5, type: 'romaine', isActive: true }
  //   ]
  // },
  // {
  //   id: 'zone-10',
  //   name: 'المرحلة العاشرة',
  //   cropType: 'خس رومين',
  //   area: 500,
  //   status: 'active',
  //   plantingDate: '2024-03-15',
  //   expectedHarvestDate: '2024-05-15',
  //   notes: 'مرحلة رومين للأسواق المحلية',
  //   valves: [
  //     { id: 'valve-10-1', number: 1, type: 'romaine', isActive: true },
  //     { id: 'valve-10-2', number: 2, type: 'romaine', isActive: false },
  //     { id: 'valve-10-3', number: 3, type: 'romaine', isActive: true },
  //     { id: 'valve-10-4', number: 4, type: 'romaine', isActive: true },
  //     { id: 'valve-10-5', number: 5, type: 'iceberg', isActive: true }
  //   ]
  // },
  // {
  //   id: 'zone-11',
  //   name: 'المرحلة الحادية عشرة',
  //   cropType: 'خس أيسبيرغ',
  //   area: 490,
  //   status: 'active',
  //   plantingDate: '2024-03-20',
  //   expectedHarvestDate: '2024-05-20',
  //   notes: 'مرحلة أيسبيرغ عالي الجودة',
  //   valves: [
  //     { id: 'valve-11-1', number: 1, type: 'iceberg', isActive: true },
  //     { id: 'valve-11-2', number: 2, type: 'iceberg', isActive: true },
  //     { id: 'valve-11-3', number: 3, type: 'iceberg', isActive: false },
  //     { id: 'valve-11-4', number: 4, type: 'iceberg', isActive: true },
  //     { id: 'valve-11-5', number: 5, type: 'romaine', isActive: false }
  //   ]
  // },
  // {
  //   id: 'zone-12',
  //   name: 'المرحلة الثانية عشرة',
  //   cropType: 'خس رومين',
  //   area: 520,
  //   status: 'active',
  //   plantingDate: '2024-03-25',
  //   expectedHarvestDate: '2024-05-25',
  //   notes: 'مرحلة رومين للتصدير',
  //   valves: [
  //     { id: 'valve-12-1', number: 1, type: 'romaine', isActive: true },
  //     { id: 'valve-12-2', number: 2, type: 'romaine', isActive: true },
  //     { id: 'valve-12-3', number: 3, type: 'romaine', isActive: true },
  //     { id: 'valve-12-4', number: 4, type: 'romaine', isActive: false },
  //     { id: 'valve-12-5', number: 5, type: 'iceberg', isActive: true }
  //   ]
  // },
  // {
  //   id: 'zone-13',
  //   name: 'المرحلة الثالثة عشرة',
  //   cropType: 'خس أيسبيرغ',
  //   area: 470,
  //   status: 'active',
  //   plantingDate: '2024-04-01',
  //   expectedHarvestDate: '2024-06-01',
  //   notes: 'مرحلة أيسبيرغ للأسواق المحلية',
  //   valves: [
  //     { id: 'valve-13-1', number: 1, type: 'iceberg', isActive: true },
  //     { id: 'valve-13-2', number: 2, type: 'iceberg', isActive: false },
  //     { id: 'valve-13-3', number: 3, type: 'iceberg', isActive: true },
  //     { id: 'valve-13-4', number: 4, type: 'iceberg', isActive: true },
  //     { id: 'valve-13-5', number: 5, type: 'romaine', isActive: true }
  //   ]
  // },
  // {
  //   id: 'zone-14',
  //   name: 'المرحلة الرابعة عشرة',
  //   cropType: 'خس رومين',
  //   area: 510,
  //   status: 'active',
  //   plantingDate: '2024-04-05',
  //   expectedHarvestDate: '2024-06-05',
  //   notes: 'مرحلة رومين عالي الجودة',
  //   valves: [
  //     { id: 'valve-14-1', number: 1, type: 'romaine', isActive: true },
  //     { id: 'valve-14-2', number: 2, type: 'romaine', isActive: true },
  //     { id: 'valve-14-3', number: 3, type: 'romaine', isActive: false },
  //     { id: 'valve-14-4', number: 4, type: 'romaine', isActive: true },
  //     { id: 'valve-14-5', number: 5, type: 'iceberg', isActive: false }
  //   ]
  // },
  // {
  //   id: 'zone-15',
  //   name: 'المرحلة الخامسة عشرة',
  //   cropType: 'خس أيسبيرغ',
  //   area: 480,
  //   status: 'active',
  //   plantingDate: '2024-04-10',
  //   expectedHarvestDate: '2024-06-10',
  //   notes: 'مرحلة أيسبيرغ للتصدير',
  //   valves: [
  //     { id: 'valve-15-1', number: 1, type: 'iceberg', isActive: true },
  //     { id: 'valve-15-2', number: 2, type: 'iceberg', isActive: true },
  //     { id: 'valve-15-3', number: 3, type: 'iceberg', isActive: true },
  //     { id: 'valve-15-4', number: 4, type: 'iceberg', isActive: false },
  //     { id: 'valve-15-5', number: 5, type: 'romaine', isActive: true }
  //   ]
  // },
  // {
  //   id: 'zone-16',
  //   name: 'المرحلة السادسة عشرة',
  //   cropType: 'خس رومين',
  //   area: 500,
  //   status: 'active',
  //   plantingDate: '2024-04-15',
  //   expectedHarvestDate: '2024-06-15',
  //   notes: 'مرحلة رومين للأسواق المحلية',
  //   valves: [
  //     { id: 'valve-16-1', number: 1, type: 'romaine', isActive: true },
  //     { id: 'valve-16-2', number: 2, type: 'romaine', isActive: false },
  //     { id: 'valve-16-3', number: 3, type: 'romaine', isActive: true },
  //     { id: 'valve-16-4', number: 4, type: 'romaine', isActive: true },
  //     { id: 'valve-16-5', number: 5, type: 'iceberg', isActive: true }
  //   ]
  // },
  // {
  //   id: 'zone-17',
  //   name: 'المرحلة السابعة عشرة',
  //   cropType: 'خس أيسبيرغ',
  //   area: 490,
  //   status: 'active',
  //   plantingDate: '2024-04-20',
  //   expectedHarvestDate: '2024-06-20',
  //   notes: 'مرحلة أيسبيرغ عالي الجودة',
  //   valves: [
  //     { id: 'valve-17-1', number: 1, type: 'iceberg', isActive: true },
  //     { id: 'valve-17-2', number: 2, type: 'iceberg', isActive: true },
  //     { id: 'valve-17-3', number: 3, type: 'iceberg', isActive: false },
  //     { id: 'valve-17-4', number: 4, type: 'iceberg', isActive: true },
  //     { id: 'valve-17-5', number: 5, type: 'romaine', isActive: false }
  //   ]
  // },
  // {
  //   id: 'zone-18',
  //   name: 'المرحلة الثامنة عشرة',
  //   cropType: 'خس رومين',
  //   area: 520,
  //   status: 'active',
  //   plantingDate: '2024-04-25',
  //   expectedHarvestDate: '2024-06-25',
  //   notes: 'مرحلة رومين للتصدير',
  //   valves: [
  //     { id: 'valve-18-1', number: 1, type: 'romaine', isActive: true },
  //     { id: 'valve-18-2', number: 2, type: 'romaine', isActive: true },
  //     { id: 'valve-18-3', number: 3, type: 'romaine', isActive: true },
  //     { id: 'valve-18-4', number: 4, type: 'romaine', isActive: false },
  //     { id: 'valve-18-5', number: 5, type: 'iceberg', isActive: true }
  //   ]
  // },
  // {
  //   id: 'zone-19',
  //   name: 'المرحلة التاسعة عشرة',
  //   cropType: 'خس أيسبيرغ',
  //   area: 470,
  //   status: 'active',
  //   plantingDate: '2024-05-01',
  //   expectedHarvestDate: '2024-07-01',
  //   notes: 'مرحلة أيسبيرغ للأسواق المحلية',
  //   valves: [
  //     { id: 'valve-19-1', number: 1, type: 'iceberg', isActive: true },
  //     { id: 'valve-19-2', number: 2, type: 'iceberg', isActive: false },
  //     { id: 'valve-19-3', number: 3, type: 'iceberg', isActive: true },
  //     { id: 'valve-19-4', number: 4, type: 'iceberg', isActive: true },
  //     { id: 'valve-19-5', number: 5, type: 'romaine', isActive: true }
  //   ]
  // },
  // {
  //   id: 'zone-20',
  //   name: 'المرحلة العشرون',
  //   cropType: 'خس رومين',
  //   area: 510,
  //   status: 'active',
  //   plantingDate: '2024-05-05',
  //   expectedHarvestDate: '2024-07-05',
  //   notes: 'مرحلة رومين عالي الجودة',
  //   valves: [
  //     { id: 'valve-20-1', number: 1, type: 'romaine', isActive: true },
  //     { id: 'valve-20-2', number: 2, type: 'romaine', isActive: true },
  //     { id: 'valve-20-3', number: 3, type: 'romaine', isActive: false },
  //     { id: 'valve-20-4', number: 4, type: 'romaine', isActive: true },
  //     { id: 'valve-20-5', number: 5, type: 'iceberg', isActive: false }
  //   ]
  // },
  // {
  //   id: 'zone-21',
  //   name: 'المرحلة الحادية والعشرون',
  //   cropType: 'خس أيسبيرغ',
  //   area: 480,
  //   status: 'active',
  //   plantingDate: '2024-05-10',
  //   expectedHarvestDate: '2024-07-10',
  //   notes: 'مرحلة أيسبيرغ للتصدير',
  //   valves: [
  //     { id: 'valve-21-1', number: 1, type: 'iceberg', isActive: true },
  //     { id: 'valve-21-2', number: 2, type: 'iceberg', isActive: true },
  //     { id: 'valve-21-3', number: 3, type: 'iceberg', isActive: true },
  //     { id: 'valve-21-4', number: 4, type: 'iceberg', isActive: false },
  //     { id: 'valve-21-5', number: 5, type: 'romaine', isActive: true }
  //   ]
  // },
  // {
  //   id: 'zone-22',
  //   name: 'المرحلة الثانية والعشرون',
  //   cropType: 'خس رومين',
  //   area: 500,
  //   status: 'active',
  //   plantingDate: '2024-05-15',
  //   expectedHarvestDate: '2024-07-15',
  //   notes: 'مرحلة رومين للأسواق المحلية',
  //   valves: [
  //     { id: 'valve-22-1', number: 1, type: 'romaine', isActive: true },
  //     { id: 'valve-22-2', number: 2, type: 'romaine', isActive: false },
  //     { id: 'valve-22-3', number: 3, type: 'romaine', isActive: true },
  //     { id: 'valve-22-4', number: 4, type: 'romaine', isActive: true },
  //     { id: 'valve-22-5', number: 5, type: 'iceberg', isActive: true }
  //   ]
  // },
  // {
  //   id: 'zone-23',
  //   name: 'المرحلة الثالثة والعشرون',
  //   cropType: 'خس أيسبيرغ',
  //   area: 490,
  //   status: 'active',
  //   plantingDate: '2024-05-20',
  //   expectedHarvestDate: '2024-07-20',
  //   notes: 'مرحلة أيسبيرغ عالي الجودة',
  //   valves: [
  //     { id: 'valve-23-1', number: 1, type: 'iceberg', isActive: true },
  //     { id: 'valve-23-2', number: 2, type: 'iceberg', isActive: true },
  //     { id: 'valve-23-3', number: 3, type: 'iceberg', isActive: false },
  //     { id: 'valve-23-4', number: 4, type: 'iceberg', isActive: true },
  //     { id: 'valve-23-5', number: 5, type: 'romaine', isActive: false }
  //   ]
  // },
  // {
  //   id: 'zone-24',
  //   name: 'المرحلة الرابعة والعشرون',
  //   cropType: 'خس رومين',
  //   area: 520,
  //   status: 'active',
  //   plantingDate: '2024-05-25',
  //   expectedHarvestDate: '2024-07-25',
  //   notes: 'مرحلة رومين للتصدير - آخر مرحلة',
  //   valves: [
  //     { id: 'valve-24-1', number: 1, type: 'romaine', isActive: true },
  //     { id: 'valve-24-2', number: 2, type: 'romaine', isActive: true },
  //     { id: 'valve-24-3', number: 3, type: 'romaine', isActive: true },
  //     { id: 'valve-24-4', number: 4, type: 'romaine', isActive: false },
  //     { id: 'valve-24-5', number: 5, type: 'iceberg', isActive: true }
  //   ]
  // }
];

// تهيئة البيانات في localStorage إذا لم تكن موجودة
const initializeData = () => {
  if (!localStorage.getItem('farm_zones')) {
    localStorage.setItem('farm_zones', JSON.stringify(sampleZones));
  }
  if (!localStorage.getItem('spraying_records')) {
    localStorage.setItem('spraying_records', JSON.stringify([]));
  }
  if (!localStorage.getItem('fertilization_records')) {
    localStorage.setItem('fertilization_records', JSON.stringify([]));
  }
  if (!localStorage.getItem('inventory_items')) {
    localStorage.setItem('inventory_items', JSON.stringify([]));
  }
};

// استدعاء التهيئة عند تحميل الملف
initializeData();

// وظائف إدارة المراحل الزراعية
export const getZones = async (): Promise<Zone[]> => {
  try {
    const zonesData = localStorage.getItem('farm_zones');
    return zonesData ? JSON.parse(zonesData) : [];
  } catch (error) {
    console.error('خطأ في قراءة المراحل:', error);
    return [];
  }
};

export const updateZone = async (zoneId: number | string, updatedZone: Zone): Promise<{ success: boolean; error?: string }> => {
  try {
    const zones = await getZones();
    const updatedZones = zones.map(zone => 
      zone.id == zoneId ? updatedZone : zone
    );
    localStorage.setItem('farm_zones', JSON.stringify(updatedZones));
    return { success: true };
  } catch (error) {
    console.error('خطأ في تحديث المرحلة:', error);
    return { success: false, error: 'فشل في تحديث المرحلة' };
  }
};

export const addZone = async (zone: Omit<Zone, 'id'>): Promise<{ success: boolean; zone?: Zone; error?: string }> => {
  try {
    const zones = await getZones();
    const newZone: Zone = {
      ...zone,
      id: Date.now(),
      valves: zone.valves || []
    };
    zones.push(newZone);
    localStorage.setItem('farm_zones', JSON.stringify(zones));
    return { success: true, zone: newZone };
  } catch (error) {
    console.error('خطأ في إضافة المرحلة:', error);
    return { success: false, error: 'فشل في إضافة المرحلة' };
  }
};

export const deleteZone = async (zoneId: number | string): Promise<{ success: boolean; error?: string }> => {
  try {
    const zones = await getZones();
    const zoneExists = zones.some(zone => zone.id == zoneId);
    
    if (!zoneExists) {
      return { success: false, error: 'المرحلة غير موجودة' };
    }

    const filteredZones = zones.filter(zone => zone.id != zoneId);
    localStorage.setItem('farm_zones', JSON.stringify(filteredZones));
    
    // حذف البيانات المرتبطة بالمرحلة
    await deleteZoneRelatedData(zoneId);
    
    return { success: true };
  } catch (error) {
    console.error('خطأ في حذف المرحلة:', error);
    return { success: false, error: 'فشل في حذف المرحلة' };
  }
};

// حذف البيانات المرتبطة بالمرحلة
const deleteZoneRelatedData = async (zoneId: number | string): Promise<void> => {
  try {
    // حذف عمليات الرش المرتبطة
    const sprayings = await getSprayings();
    const filteredSprayings = sprayings.filter(s => s.targetZone != zoneId);
    localStorage.setItem('sprayings', JSON.stringify(filteredSprayings));

    // حذف عمليات التسميد المرتبطة
    const fertilizations = await getFertilizations();
    const filteredFertilizations = fertilizations.filter(f => f.targetZone != zoneId);
    localStorage.setItem('fertilizations', JSON.stringify(filteredFertilizations));

    // حذف محابس الري المرتبطة
    const valves = await getIrrigationValves();
    const filteredValves = valves.filter(v => v.zoneId != zoneId);
    localStorage.setItem('irrigation_valves', JSON.stringify(filteredValves));
  } catch (error) {
    console.error('خطأ في حذف البيانات المرتبطة:', error);
  }
};

// ===== وظائف محابس الري =====

export async function getIrrigationValves(): Promise<IrrigationValve[]> {
  try {
    const saved = localStorage.getItem('irrigation_valves');
    if (saved) {
      return JSON.parse(saved);
    } else {
      // إنشاء 120 محبس ري (24 مرحلة × 5 محابس)
      const defaultValves: IrrigationValve[] = [];
      for (let zoneId = 1; zoneId <= 24; zoneId++) {
        for (let valveNumber = 1; valveNumber <= 5; valveNumber++) {
          defaultValves.push({
            id: (zoneId - 1) * 5 + valveNumber,
            zoneId,
            valveNumber,
            lettuceType: valveNumber <= 4 ? 'iceberg' : 'romaine',
            status: 'inactive',
            lastIrrigation: '',
            nextIrrigation: ''
          });
        }
      }
      localStorage.setItem('irrigation_valves', JSON.stringify(defaultValves));
      return defaultValves;
    }
  } catch (error) {
    console.error('خطأ في جلب محابس الري:', error);
    return [];
  }
}

// ===== وظائف الرش =====

export async function getSprayings(): Promise<Spraying[]> {
  try {
    const saved = localStorage.getItem('sprayings');
    return saved ? JSON.parse(saved) : [];
  } catch (error) {
    console.error('خطأ في جلب بيانات الرش:', error);
    return [];
  }
}

export async function addSpraying(spraying: Omit<Spraying, 'id' | 'createdAt'>): Promise<{ success: boolean; id?: number; error?: string }> {
  try {
    const sprayings = await getSprayings();
    const newId = Math.max(...sprayings.map(s => s.id), 0) + 1;
    
    const newSpraying: Spraying = {
      ...spraying,
      id: newId,
      createdAt: new Date().toISOString()
    };
    
    sprayings.push(newSpraying);
    localStorage.setItem('sprayings', JSON.stringify(sprayings));
    
    return { success: true, id: newId };
  } catch (error) {
    console.error('خطأ في إضافة الرش:', error);
    return { success: false, error: 'خطأ في حفظ الرش' };
  }
}

export async function updateSpraying(spraying: Spraying): Promise<{ success: boolean; error?: string }> {
  try {
    const sprayings = await getSprayings();
    const index = sprayings.findIndex(s => s.id === spraying.id);
    if (index === -1) {
      return { success: false, error: 'العملية غير موجودة' };
    }
    sprayings[index] = spraying;
    localStorage.setItem('sprayings', JSON.stringify(sprayings));
    return { success: true };
  } catch (error) {
    return { success: false, error: 'خطأ في تحديث بيانات الرش' };
  }
}

export async function deleteSpraying(id: number): Promise<{ success: boolean; error?: string }> {
  try {
    const sprayings = await getSprayings();
    const newSprayings = sprayings.filter(s => s.id !== id);
    if (sprayings.length === newSprayings.length) {
      return { success: false, error: 'العملية غير موجودة' };
    }
    localStorage.setItem('sprayings', JSON.stringify(newSprayings));
    return { success: true };
  } catch (error) {
    return { success: false, error: 'خطأ في حذف بيانات الرش' };
  }
}

// ===== وظائف التسميد =====

export async function getFertilizations(): Promise<Fertilization[]> {
  try {
    const saved = localStorage.getItem('fertilizations');
    return saved ? JSON.parse(saved) : [];
  } catch (error) {
    console.error('خطأ في جلب بيانات التسميد:', error);
    return [];
  }
}

export async function addFertilization(fertilization: Omit<Fertilization, 'id' | 'createdAt'>): Promise<{ success: boolean; id?: number; error?: string }> {
  try {
    const fertilizations = await getFertilizations();
    const newId = Math.max(...fertilizations.map(f => f.id), 0) + 1;
    
    const newFertilization: Fertilization = {
      ...fertilization,
      id: newId,
      createdAt: new Date().toISOString()
    };
    
    fertilizations.push(newFertilization);
    localStorage.setItem('fertilizations', JSON.stringify(fertilizations));
    
    return { success: true, id: newId };
  } catch (error) {
    console.error('خطأ في إضافة التسميد:', error);
    return { success: false, error: 'خطأ في حفظ التسميد' };
  }
}

export async function updateFertilization(fertilization: Fertilization): Promise<{ success: boolean; error?: string }> {
  try {
    const fertilizations = await getFertilizations();
    const index = fertilizations.findIndex(f => f.id === fertilization.id);
    if (index === -1) {
      return { success: false, error: 'العملية غير موجودة' };
    }
    fertilizations[index] = fertilization;
    localStorage.setItem('fertilizations', JSON.stringify(fertilizations));
    return { success: true };
  } catch (error) {
    return { success: false, error: 'خطأ في تحديث بيانات التسميد' };
  }
}

export async function deleteFertilization(id: number): Promise<{ success: boolean; error?: string }> {
  try {
    const fertilizations = await getFertilizations();
    const newFertilizations = fertilizations.filter(f => f.id !== id);
    if (fertilizations.length === newFertilizations.length) {
      return { success: false, error: 'العملية غير موجودة' };
    }
    localStorage.setItem('fertilizations', JSON.stringify(newFertilizations));
    return { success: true };
  } catch (error) {
    return { success: false, error: 'خطأ في حذف بيانات التسميد' };
  }
}

// ===== وظائف المخزون =====

export async function getInventoryItems(): Promise<InventoryItem[]> {
  try {
    const saved = localStorage.getItem('inventory_items');
    return saved ? JSON.parse(saved) : [];
  } catch (error) {
    console.error('خطأ في جلب المخزون:', error);
    return [];
  }
}

export async function addInventoryItem(item: Omit<InventoryItem, 'id'>): Promise<{ success: boolean; id?: number; error?: string }> {
  try {
    const items = await getInventoryItems();
    const newId = Math.max(...items.map(i => i.id), 0) + 1;
    
    const newItem: InventoryItem = {
      ...item,
      id: newId
    };
    
    items.push(newItem);
    localStorage.setItem('inventory_items', JSON.stringify(items));
    
    return { success: true, id: newId };
  } catch (error) {
    console.error('خطأ في إضافة المخزون:', error);
    return { success: false, error: 'خطأ في حفظ المخزون' };
  }
}

export async function updateInventoryItem(itemId: number, updates: Partial<InventoryItem>): Promise<{ success: boolean; error?: string }> {
  try {
    const items = await getInventoryItems();
    const itemIndex = items.findIndex(item => item.id === itemId);
    
    if (itemIndex === -1) {
      return { success: false, error: 'العنصر غير موجود' };
    }

    items[itemIndex] = { ...items[itemIndex], ...updates };
    localStorage.setItem('inventory_items', JSON.stringify(items));
    
    return { success: true };
  } catch (error) {
    console.error('خطأ في تحديث المخزون:', error);
    return { success: false, error: 'خطأ في حفظ التحديث' };
  }
}

export async function deleteInventoryItem(itemId: number): Promise<{ success: boolean; error?: string }> {
  try {
    const items = await getInventoryItems();
    const newItems = items.filter(item => item.id !== itemId);
    if (items.length === newItems.length) {
      return { success: false, error: 'العنصر غير موجود' };
    }
    localStorage.setItem('inventory_items', JSON.stringify(newItems));
    return { success: true };
  } catch (error) {
    console.error('خطأ في حذف المخزون:', error);
    return { success: false, error: 'خطأ في حذف المخزون' };
  }
}

export async function getInventoryMovements(): Promise<InventoryMovement[]> {
  try {
    const saved = localStorage.getItem('inventory_movements');
    return saved ? JSON.parse(saved) : [];
  } catch (error) {
    console.error('خطأ في جلب حركات المخزون:', error);
    return [];
  }
}

export async function addInventoryMovement(movement: Omit<InventoryMovement, 'id' | 'createdAt'>): Promise<{ success: boolean; id?: number; error?: string }> {
  try {
    const movements = await getInventoryMovements();
    const newId = Math.max(...movements.map(m => m.id), 0) + 1;
    
    const newMovement: InventoryMovement = {
      ...movement,
      id: newId,
      createdAt: new Date().toISOString()
    };
    
    movements.push(newMovement);
    localStorage.setItem('inventory_movements', JSON.stringify(movements));
    
    // تحديث كمية المخزون
    const items = await getInventoryItems();
    const itemIndex = items.findIndex(item => item.id === movement.itemId);
    if (itemIndex !== -1) {
      if (movement.type === 'in') {
        items[itemIndex].quantity += movement.quantity;
      } else {
        items[itemIndex].quantity -= movement.quantity;
      }
      localStorage.setItem('inventory_items', JSON.stringify(items));
    }
    
    return { success: true, id: newId };
  } catch (error) {
    console.error('خطأ في إضافة حركة المخزون:', error);
    return { success: false, error: 'خطأ في حفظ الحركة' };
  }
}

// ===== وظائف الموظفين =====

export async function getEmployees(): Promise<Employee[]> {
  try {
    const saved = localStorage.getItem('employees');
    return saved ? JSON.parse(saved) : [];
  } catch (error) {
    console.error('خطأ في جلب الموظفين:', error);
    return [];
  }
}

export async function addEmployee(employee: Omit<Employee, 'id' | 'createdAt'>): Promise<{ success: boolean; id?: number; error?: string }> {
  try {
    const employees = await getEmployees();
    const newId = Math.max(...employees.map(e => e.id), 0) + 1;
    
    const newEmployee: Employee = {
      ...employee,
      id: newId,
      createdAt: new Date().toISOString()
    };
    
    employees.push(newEmployee);
    localStorage.setItem('employees', JSON.stringify(employees));
    
    return { success: true, id: newId };
  } catch (error) {
    console.error('خطأ في إضافة الموظف:', error);
    return { success: false, error: 'خطأ في حفظ الموظف' };
  }
}

// ===== وظائف الموردين =====

export async function getSuppliers(): Promise<Supplier[]> {
  try {
    const saved = localStorage.getItem('suppliers');
    return saved ? JSON.parse(saved) : [];
  } catch (error) {
    console.error('خطأ في جلب الموردين:', error);
    return [];
  }
}

export async function addSupplier(supplier: Omit<Supplier, 'id' | 'createdAt'>): Promise<{ success: boolean; id?: number; error?: string }> {
  try {
    const suppliers = await getSuppliers();
    const newId = Math.max(...suppliers.map(s => s.id), 0) + 1;
    
    const newSupplier: Supplier = {
      ...supplier,
      id: newId,
      createdAt: new Date().toISOString()
    };
    
    suppliers.push(newSupplier);
    localStorage.setItem('suppliers', JSON.stringify(suppliers));
    
    return { success: true, id: newId };
  } catch (error) {
    console.error('خطأ في إضافة المورد:', error);
    return { success: false, error: 'خطأ في حفظ المورد' };
  }
}

// ===== وظائف السلف =====

export async function getAdvances(): Promise<Advance[]> {
  try {
    const saved = localStorage.getItem('advances');
    return saved ? JSON.parse(saved) : [];
  } catch (error) {
    console.error('خطأ في جلب السلف:', error);
    return [];
  }
}

export async function addAdvance(advance: Omit<Advance, 'id' | 'createdAt'>): Promise<{ success: boolean; id?: number; error?: string }> {
  try {
    const advances = await getAdvances();
    const newId = Math.max(...advances.map(a => a.id), 0) + 1;
    
    const newAdvance: Advance = {
      ...advance,
      id: newId,
      createdAt: new Date().toISOString()
    };
    
    advances.push(newAdvance);
    localStorage.setItem('advances', JSON.stringify(advances));
    
    return { success: true, id: newId };
  } catch (error) {
    console.error('خطأ في إضافة السلفة:', error);
    return { success: false, error: 'خطأ في حفظ السلفة' };
  }
}

// ===== وظائف المبيعات المحلية =====

export async function getLocalSales(): Promise<LocalSale[]> {
  try {
    const saved = localStorage.getItem('local_sales');
    return saved ? JSON.parse(saved) : [];
  } catch (error) {
    console.error('خطأ في جلب المبيعات:', error);
    return [];
  }
}

export async function addLocalSale(sale: Omit<LocalSale, 'id' | 'createdAt'>): Promise<{ success: boolean; id?: number; error?: string }> {
  try {
    const sales = await getLocalSales();
    const newId = Math.max(...sales.map(s => s.id), 0) + 1;
    
    const newSale: LocalSale = {
      ...sale,
      id: newId,
      createdAt: new Date().toISOString()
    };
    
    sales.push(newSale);
    localStorage.setItem('local_sales', JSON.stringify(sales));
    
    return { success: true, id: newId };
  } catch (error) {
    console.error('خطأ في إضافة المبيعات:', error);
    return { success: false, error: 'خطأ في حفظ المبيعات' };
  }
}

// ===== وظائف جدول الري =====

export async function getIrrigationSchedules(): Promise<IrrigationSchedule[]> {
  try {
    const saved = localStorage.getItem('irrigation_schedules');
    return saved ? JSON.parse(saved) : [];
  } catch (error) {
    console.error('خطأ في جلب جدول الري:', error);
    return [];
  }
}

export async function addIrrigationSchedule(schedule: Omit<IrrigationSchedule, 'id' | 'createdAt'>): Promise<{ success: boolean; id?: number; error?: string }> {
  try {
    const schedules = await getIrrigationSchedules();
    const newId = Math.max(...schedules.map(s => s.id), 0) + 1;
    
    const newSchedule: IrrigationSchedule = {
      ...schedule,
      id: newId,
      createdAt: new Date().toISOString()
    };
    
    schedules.push(newSchedule);
    localStorage.setItem('irrigation_schedules', JSON.stringify(schedules));
    
    return { success: true, id: newId };
  } catch (error) {
    console.error('خطأ في إضافة جدول الري:', error);
    return { success: false, error: 'خطأ في حفظ جدول الري' };
  }
}

// ===== وظائف الإحصائيات =====

export async function getDashboardStats(): Promise<DashboardStats> {
  try {
    const zones = await getZones();
    const valves = await getIrrigationValves();
    const employees = await getEmployees();
    const sales = await getLocalSales();
    const inventory = await getInventoryItems();
    const advances = await getAdvances();
    const schedules = await getIrrigationSchedules();

    const today = new Date().toISOString().split('T')[0];
    const currentMonth = new Date().getMonth();
    const currentYear = new Date().getFullYear();

    const todayIrrigations = schedules.filter(s => s.date === today && s.status === 'completed').length;
    const pendingIrrigations = schedules.filter(s => s.date >= today && s.status === 'scheduled').length;
    
    const monthlySales = sales
      .filter(s => {
        const saleDate = new Date(s.date);
        return saleDate.getMonth() === currentMonth && saleDate.getFullYear() === currentYear;
      })
      .reduce((sum, sale) => sum + sale.amount, 0);

    const lowStockItems = inventory.filter(item => item.quantity <= item.minQuantity).length;
    const pendingAdvances = advances.filter(a => a.status === 'pending').length;

    return {
      totalZones: zones.length,
      activeZones: zones.filter(z => z.status === 'active').length,
      harvestingZones: zones.filter(z => z.status === 'harvesting').length,
      totalIrrigationValves: valves.length,
      activeValves: valves.filter(v => v.status === 'active').length,
      todayIrrigations,
      pendingIrrigations,
      totalEmployees: employees.length,
      activeEmployees: employees.filter(e => e.status === 'active').length,
      totalSales: sales.reduce((sum, sale) => sum + sale.amount, 0),
      monthlySales,
      lowStockItems,
      pendingAdvances
    };
  } catch (error) {
    console.error('خطأ في حساب الإحصائيات:', error);
    return {
      totalZones: 0,
      activeZones: 0,
      harvestingZones: 0,
      totalIrrigationValves: 0,
      activeValves: 0,
      todayIrrigations: 0,
      pendingIrrigations: 0,
      totalEmployees: 0,
      activeEmployees: 0,
      totalSales: 0,
      monthlySales: 0,
      lowStockItems: 0,
      pendingAdvances: 0
    };
  }
}

// ===== وظائف النسخ الاحتياطي =====

export async function exportBackup(): Promise<string> {
  try {
    const backup = {
      zones: await getZones(),
      valves: await getIrrigationValves(),
      sprayings: await getSprayings(),
      fertilizations: await getFertilizations(),
      inventory: await getInventoryItems(),
      movements: await getInventoryMovements(),
      employees: await getEmployees(),
      suppliers: await getSuppliers(),
      advances: await getAdvances(),
      sales: await getLocalSales(),
      schedules: await getIrrigationSchedules(),
      exportDate: new Date().toISOString()
    };
    
    return JSON.stringify(backup, null, 2);
  } catch (error) {
    console.error('خطأ في تصدير النسخة الاحتياطية:', error);
    throw new Error('فشل في تصدير النسخة الاحتياطية');
  }
}

export async function importBackup(backupData: string): Promise<{ success: boolean; error?: string }> {
  try {
    const backup = JSON.parse(backupData);
    
    if (backup.zones) localStorage.setItem('farm_zones', JSON.stringify(backup.zones));
    if (backup.valves) localStorage.setItem('irrigation_valves', JSON.stringify(backup.valves));
    if (backup.sprayings) localStorage.setItem('sprayings', JSON.stringify(backup.sprayings));
    if (backup.fertilizations) localStorage.setItem('fertilizations', JSON.stringify(backup.fertilizations));
    if (backup.inventory) localStorage.setItem('inventory_items', JSON.stringify(backup.inventory));
    if (backup.movements) localStorage.setItem('inventory_movements', JSON.stringify(backup.movements));
    if (backup.employees) localStorage.setItem('employees', JSON.stringify(backup.employees));
    if (backup.suppliers) localStorage.setItem('suppliers', JSON.stringify(backup.suppliers));
    if (backup.advances) localStorage.setItem('advances', JSON.stringify(backup.advances));
    if (backup.sales) localStorage.setItem('local_sales', JSON.stringify(backup.sales));
    if (backup.schedules) localStorage.setItem('irrigation_schedules', JSON.stringify(backup.schedules));
    
    return { success: true };
  } catch (error) {
    console.error('خطأ في استيراد النسخة الاحتياطية:', error);
    return { success: false, error: 'فشل في استيراد النسخة الاحتياطية' };
  }
}

// وظائف النسخ الاحتياطي والاستيراد
export const backupData = async (): Promise<void> => {
  try {
    const backup = {
      zones: await getZones(),
      sprayingRecords: await getSprayings(),
      fertilizationRecords: await getFertilizations(),
      inventoryItems: await getInventoryItems(),
      timestamp: new Date().toISOString()
    };
    
    const blob = new Blob([JSON.stringify(backup, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `farm-backup-${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  } catch (error) {
    console.error('خطأ في إنشاء النسخة الاحتياطية:', error);
    throw error;
  }
};

export const importData = async (file: File): Promise<void> => {
  try {
    const text = await file.text();
    const data = JSON.parse(text);
    
    if (data.zones) {
      localStorage.setItem('farm_zones', JSON.stringify(data.zones));
    }
    if (data.sprayingRecords) {
      localStorage.setItem('spraying_records', JSON.stringify(data.sprayingRecords));
    }
    if (data.fertilizationRecords) {
      localStorage.setItem('fertilization_records', JSON.stringify(data.fertilizationRecords));
    }
    if (data.inventoryItems) {
      localStorage.setItem('inventory_items', JSON.stringify(data.inventoryItems));
    }
  } catch (error) {
    console.error('خطأ في استيراد البيانات:', error);
    throw error;
  }
};

// إدارة المصروفات والإيرادات
export async function getTransactions(): Promise<Transaction[]> {
  try {
    const saved = localStorage.getItem('transactions');
    return saved ? JSON.parse(saved) : [];
  } catch (error) {
    return [];
  }
}

export async function addTransaction(transaction: Omit<Transaction, 'id'>): Promise<{ success: boolean; id?: number; error?: string }> {
  try {
    const transactions = await getTransactions();
    const newId = Math.max(...transactions.map(t => t.id), 0) + 1;
    const newTransaction: Transaction = { ...transaction, id: newId };
    transactions.push(newTransaction);
    localStorage.setItem('transactions', JSON.stringify(transactions));
    return { success: true, id: newId };
  } catch (error) {
    return { success: false, error: 'خطأ في حفظ المعاملة' };
  }
}

export async function updateTransaction(transaction: Transaction): Promise<{ success: boolean; error?: string }> {
  try {
    const transactions = await getTransactions();
    const index = transactions.findIndex(t => t.id === transaction.id);
    if (index === -1) return { success: false, error: 'المعاملة غير موجودة' };
    transactions[index] = transaction;
    localStorage.setItem('transactions', JSON.stringify(transactions));
    return { success: true };
  } catch (error) {
    return { success: false, error: 'خطأ في تحديث المعاملة' };
  }
}

export async function deleteTransaction(id: number): Promise<{ success: boolean; error?: string }> {
  try {
    const transactions = await getTransactions();
    const newTransactions = transactions.filter(t => t.id !== id);
    if (transactions.length === newTransactions.length) return { success: false, error: 'المعاملة غير موجودة' };
    localStorage.setItem('transactions', JSON.stringify(newTransactions));
    return { success: true };
  } catch (error) {
    return { success: false, error: 'خطأ في حذف المعاملة' };
  }
}

// إعادة تعيين أصناف المخزون إلى القيم الافتراضية (خس فقط)
export async function resetItemsToDefault(): Promise<void> {
  // أصناف خس افتراضية
  const defaultItems = [
    {
      id: 1,
      name: 'خس أيسبيرغ',
      type: 'iceberg',
      quantity: 0,
      unit: 'كرتونة',
      minStock: 0,
      notes: 'صنف افتراضي'
    },
    {
      id: 2,
      name: 'خس رومين',
      type: 'romaine',
      quantity: 0,
      unit: 'كرتونة',
      minStock: 0,
      notes: 'صنف افتراضي'
    }
  ];
  localStorage.setItem('warehouseInventory', JSON.stringify(defaultItems));
  localStorage.setItem('warehouseAvailableItems', JSON.stringify(defaultItems));
  localStorage.setItem('warehouseMovements', JSON.stringify([]));
}

// حذف جميع بيانات المخزون والحركات فقط
export async function clearAllInventoryData(): Promise<void> {
  localStorage.setItem('warehouseInventory', JSON.stringify([]));
  localStorage.setItem('warehouseAvailableItems', JSON.stringify([]));
  localStorage.setItem('warehouseMovements', JSON.stringify([]));
}

// دالة موحدة لتنسيق التاريخ بصيغة dd-mm-yyyy
export function formatDate(dateInput: string | Date): string {
  let date: Date;
  if (typeof dateInput === 'string') {
    date = new Date(dateInput);
  } else {
    date = dateInput;
  }
  if (isNaN(date.getTime())) return '';
  const day = date.getDate().toString().padStart(2, '0');
  const month = (date.getMonth() + 1).toString().padStart(2, '0');
  const year = date.getFullYear();
  return `${day}-${month}-${year}`;
}
