# 📝 نافذة اختيار اسم المرحلة المنبثقة

## 🎯 **الميزة الجديدة:**
تحويل حقل اسم المرحلة إلى نافذة منبثقة ذكية مع اقتراحات واختيارات متقدمة

---

## ✨ **المميزات المضافة:**

### **1. 🖱️ زر اختيار تفاعلي:**
- **بدلاً من حقل نص عادي** → زر أنيق قابل للنقر
- **عرض الاسم المختار** أو رسالة "اختر اسم المرحلة..."
- **أيقونة وتسمية واضحة** مع تأثيرات بصرية

### **2. 🪟 نافذة منبثقة متقدمة:**
- **تصميم احترافي** مع رأس ملون وأزرار تحكم
- **حجم متجاوب** يتكيف مع الشاشة
- **إغلاق سهل** بزر X أو النقر خارج النافذة

### **3. 🖊️ حقل اسم مخصص:**
- **إدخال حر** لأسماء مخصصة
- **فحص التكرار** مع تحذير بصري
- **حفظ بالضغط على Enter** أو زر الحفظ
- **تعطيل الزر** إذا كان الاسم مكرراً

### **4. 💡 اقتراحات ذكية:**
- **16 اقتراح متنوع** للأسماء الشائعة
- **فلترة تلقائية** لإزالة الأسماء المستخدمة
- **عرض شبكي منظم** سهل التصفح
- **عداد الاقتراحات** المتاحة

### **5. 🛡️ حماية من التكرار:**
- **فحص فوري** للأسماء المكررة
- **تحذير بصري** باللون الأحمر
- **منع الحفظ** للأسماء المكررة
- **رسائل واضحة** للمستخدم

---

## 🎨 **الواجهة الجديدة:**

### **الزر الرئيسي:**
```
┌─────────────────────────────────────┐
│ اسم المرحلة *                      │
│ ┌─────────────────────────────────┐ │
│ │ اختر اسم المرحلة...      📝 اختر │ │
│ └─────────────────────────────────┘ │
└─────────────────────────────────────┘
```

### **النافذة المنبثقة:**
```
┌─────────────────────────────────────────────┐
│ 📝 اختيار اسم المرحلة                  × │
│ اختر من الاقتراحات أو أدخل اسماً مخصصاً   │
├─────────────────────────────────────────────┤
│                                             │
│ 🖊️ اسم مخصص:                              │
│ ┌─────────────────────────────┐ ┌─────────┐ │
│ │ أدخل اسم المرحلة...        │ │ ✅ حفظ │ │
│ └─────────────────────────────┘ └─────────┘ │
│                                             │
│ ────── أو اختر من الاقتراحات ──────        │
│                                             │
│ 💡 اقتراحات الأسماء:           16 اقتراح │
│                                             │
│ ┌─────────────────┐ ┌─────────────────┐     │
│ │ المرحلة الأولى  → │ │ المرحلة الثانية → │     │
│ └─────────────────┘ └─────────────────┘     │
│ ┌─────────────────┐ ┌─────────────────┐     │
│ │ مرحلة الإنتاج   → │ │ مرحلة التجريب  → │     │
│ └─────────────────┘ └─────────────────┘     │
│                                             │
├─────────────────────────────────────────────┤
│                                      إلغاء │
└─────────────────────────────────────────────┘
```

---

## 🔧 **التفاصيل التقنية:**

### **1. State Management:**
```typescript
const [showNameModal, setShowNameModal] = useState(false);
const [nameInput, setNameInput] = useState('');

// فتح النافذة مع الاسم الحالي
const openModal = () => {
  setNameInput(formData.name);
  setShowNameModal(true);
};
```

### **2. اقتراحات الأسماء:**
```typescript
const getNameSuggestions = () => {
  const existingNames = zones?.map(z => z.name) || [];
  const suggestions = [
    'المرحلة الأولى', 'المرحلة الثانية', 'المرحلة الثالثة',
    'مرحلة الإنتاج الرئيسية', 'مرحلة التجريب',
    'مرحلة الموسم الشتوي', 'مرحلة الموسم الصيفي',
    'مرحلة البيوت المحمية', 'مرحلة الزراعة المكشوفة',
    'مرحلة الخس الأيسبيرغ', 'مرحلة الخس الرومين',
    'مرحلة الإنتاج المكثف', 'مرحلة الإنتاج العضوي'
    // ... المزيد
  ];
  
  return suggestions.filter(name => !existingNames.includes(name));
};
```

### **3. فحص التكرار:**
```typescript
const isNameDuplicate = (name: string) => {
  return zones.some(z => z.name === name);
};

const handleCustomNameSave = () => {
  if (nameInput.trim()) {
    if (isNameDuplicate(nameInput.trim())) {
      alert('⚠️ هذا الاسم مستخدم بالفعل، يرجى اختيار اسم آخر');
      return;
    }
    setFormData({...formData, name: nameInput.trim()});
    setShowNameModal(false);
  }
};
```

### **4. التحذير البصري:**
```typescript
<input
  className={`w-full px-4 py-3 border-2 rounded-xl ${
    nameInput.trim() && isNameDuplicate(nameInput.trim())
      ? 'border-red-300 focus:ring-red-500' // أحمر للمكرر
      : 'border-gray-200 focus:ring-blue-500' // عادي
  }`}
/>

{nameInput.trim() && isNameDuplicate(nameInput.trim()) && (
  <div className="absolute top-full left-0 right-0 mt-1 p-2 bg-red-50 border border-red-200 rounded-lg text-red-600">
    ⚠️ هذا الاسم مستخدم بالفعل
  </div>
)}
```

---

## 📋 **قائمة الاقتراحات الكاملة:**

### **أسماء المراحل الأساسية:**
1. المرحلة الأولى
2. المرحلة الثانية  
3. المرحلة الثالثة
4. المرحلة الرابعة
5. المرحلة الخامسة
6. المرحلة السادسة

### **أسماء حسب الغرض:**
7. مرحلة الإنتاج الرئيسية
8. مرحلة التجريب
9. مرحلة الإنتاج المكثف
10. مرحلة الإنتاج العضوي

### **أسماء حسب الموسم:**
11. مرحلة الموسم الشتوي
12. مرحلة الموسم الصيفي

### **أسماء حسب نوع الزراعة:**
13. مرحلة البيوت المحمية
14. مرحلة الزراعة المكشوفة

### **أسماء حسب نوع الخس:**
15. مرحلة الخس الأيسبيرغ
16. مرحلة الخس الرومين

---

## 🧪 **كيفية الاستخدام:**

### **اختيار اسم من الاقتراحات:**
1. **اضغط** على زر "اختر اسم المرحلة..."
2. **تصفح** الاقتراحات في الشبكة
3. **اضغط** على الاقتراح المطلوب
4. **النتيجة:** الاسم يُحفظ تلقائياً والنافذة تُغلق

### **إدخال اسم مخصص:**
1. **اضغط** على زر "اختر اسم المرحلة..."
2. **اكتب** في حقل "اسم مخصص"
3. **إذا كان مكرراً:** ستظهر رسالة تحذير حمراء
4. **إذا كان صحيحاً:** اضغط "حفظ" أو Enter
5. **النتيجة:** الاسم يُحفظ والنافذة تُغلق

### **تعديل اسم موجود:**
1. **اضغط** على الزر (سيظهر الاسم الحالي)
2. **عدّل** في حقل الاسم المخصص
3. **احفظ** الاسم الجديد

---

## 🎯 **الفوائد:**

### **✅ للمستخدم:**
- **سهولة الاختيار** من اقتراحات جاهزة
- **منع الأخطاء** والأسماء المكررة
- **واجهة جذابة** ومنظمة
- **توفير الوقت** في التفكير بالأسماء

### **✅ للنظام:**
- **تنظيم أفضل** لأسماء المراحل
- **منع التضارب** في قاعدة البيانات
- **تجربة مستخدم محسنة**
- **مرونة في الاختيار**

### **✅ للصيانة:**
- **كود منظم** وقابل للتطوير
- **اقتراحات قابلة للتخصيص**
- **فحص شامل** للتكرار
- **واجهة متسقة** مع باقي النظام

---

## 🧪 **اختبر الميزة الآن:**

1. **افتح:** http://localhost:5173
2. **انتقل إلى:** "إدارة المراحل الزراعية" 🌱
3. **اضغط:** "إضافة مرحلة جديدة" ➕
4. **اضغط:** على زر "اختر اسم المرحلة..." 📝
5. **جرب:**
   - **اختيار من الاقتراحات** 💡
   - **إدخال اسم مخصص** 🖊️
   - **اختبار اسم مكرر** ⚠️

---

## 🎉 **النتيجة النهائية:**

**🌟 نافذة اختيار اسم المرحلة الذكية جاهزة!**

### **✅ المميزات المكتملة:**
- **واجهة منبثقة احترافية** 🪟
- **اقتراحات ذكية ومتنوعة** 💡
- **حماية من التكرار** 🛡️
- **إدخال مخصص مرن** 🖊️
- **تصميم متجاوب وجذاب** 🎨

### **🚀 تجربة المستخدم:**
- **سهولة الاستخدام** والتنقل
- **وضوح الخيارات** والاقتراحات
- **منع الأخطاء** والتكرار
- **مرونة في الاختيار** والتخصيص

**🎯 استمتع بتجربة اختيار أسماء المراحل الجديدة والمحسنة!** 💪