import React, { useState } from 'react';
import { Users, Plus, Search, Package, Phone, Mail, MapPin, CheckCircle, XCircle } from 'lucide-react';
import { Supplier } from '../types';

interface SuppliersViewProps {
  suppliers: Supplier[];
  onSupplierUpdate: (supplierId: number, updatedSupplier: Supplier) => void;
  onSupplierAdd: (supplier: Omit<Supplier, 'id' | 'createdAt'>) => void;
  onSupplierDelete: (supplierId: number) => void;
}

const SuppliersView: React.FC<SuppliersViewProps> = ({ 
  suppliers, 
  onSupplierUpdate, 
  onSupplierAdd, 
  onSupplierDelete 
}) => {
  const [showAddModal, setShowAddModal] = useState(false);
  const [editingSupplier, setEditingSupplier] = useState<Supplier | null>(null);
  const [searchTerm, setSearchTerm] = useState('');

  const filteredSuppliers = suppliers.filter(supplier =>
    supplier.name.includes(searchTerm) ||
    supplier.phone.includes(searchTerm) ||
    (supplier.materials && supplier.materials.join(',').includes(searchTerm))
  );

  const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    const formData = new FormData(e.currentTarget);
    const materials = (formData.get('materials') as string).split(',').map(m => m.trim()).filter(Boolean);
    const supplierData = {
      name: formData.get('name') as string,
      phone: formData.get('phone') as string,
      email: formData.get('email') as string,
      address: formData.get('address') as string,
      materials,
      paymentMethod: formData.get('paymentMethod') as string,
      notes: formData.get('notes') as string,
    };
    if (editingSupplier) {
      onSupplierUpdate(editingSupplier.id, { ...editingSupplier, ...supplierData });
      setEditingSupplier(null);
    } else {
      onSupplierAdd(supplierData);
      setShowAddModal(false);
    }
  };

  // إحصائيات
  const totalSuppliers = suppliers.length;

  return (
    <div className="min-h-screen w-full bg-gradient-to-br from-purple-50 via-pink-50 to-blue-50 py-8 px-2 md:px-8">
      {/* Header مع التدرج */}
      <div className="bg-gradient-to-r from-purple-600 to-pink-500 text-white p-6 shadow-lg rounded-2xl mb-4">
        <div className="max-w-7xl mx-auto">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <div className="bg-white/20 p-3 rounded-2xl flex items-center justify-center backdrop-blur-sm">
                <Package className="h-8 w-8 text-white" />
              </div>
              <div>
                <h1 className="text-3xl font-bold drop-shadow">إدارة الموردين</h1>
                <p className="text-pink-100 mt-1">بيانات الموردين والمنتجات</p>
              </div>
            </div>
            <button
              onClick={() => setShowAddModal(true)}
              className="bg-white text-purple-600 hover:bg-purple-50 px-6 py-3 rounded-xl font-semibold flex items-center gap-2 transition-colors shadow-md hover:shadow-lg border-2 border-purple-400 hover:border-purple-500 transform hover:scale-105"
            >
              <Plus size={20} />
              <span>إضافة مورد</span>
            </button>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto p-0 md:p-6">
        {/* شريط التنقل */}
        <div className="bg-white rounded-2xl shadow-md p-4 mb-6 border border-purple-100 flex flex-wrap gap-3 justify-center">
          <button 
            onClick={() => {
              alert(`إجمالي عدد الموردين: ${totalSuppliers}`);
            }}
            className="bg-purple-600 text-white px-6 py-3 rounded-xl font-semibold flex items-center gap-2 shadow hover:bg-purple-700 transition-colors"
          > 
            <Package size={20} /> 
            <span>الموردين</span> 
          </button>
          <button 
            onClick={() => {
              const phoneNumbers = suppliers.map(s => s.phone).join('\n');
              if (phoneNumbers) {
                alert(`أرقام هواتف الموردين:\n${phoneNumbers}`);
              } else {
                alert('لا توجد أرقام هواتف متاحة');
              }
            }}
            className="bg-blue-600 text-white px-6 py-3 rounded-xl font-semibold flex items-center gap-2 shadow hover:bg-blue-700 transition-colors"
          > 
            <Phone size={20} /> 
            <span>الاتصالات</span> 
          </button>
          <button 
            onClick={() => {
              const emails = suppliers.map(s => s.email).filter(Boolean).join('\n');
              if (emails) {
                alert(`عناوين البريد الإلكتروني:\n${emails}`);
              } else {
                alert('لا توجد عناوين بريد إلكتروني متاحة');
              }
            }}
            className="bg-green-600 text-white px-6 py-3 rounded-xl font-semibold flex items-center gap-2 shadow hover:bg-green-700 transition-colors"
          > 
            <Mail size={20} /> 
            <span>البريد الإلكتروني</span> 
          </button>
          <button 
            onClick={() => {
              const addresses = suppliers.map(s => s.address).filter(Boolean).join('\n');
              if (addresses) {
                alert(`عناوين الموردين:\n${addresses}`);
              } else {
                alert('لا توجد عناوين متاحة');
              }
            }}
            className="bg-pink-600 text-white px-6 py-3 rounded-xl font-semibold flex items-center gap-2 shadow hover:bg-pink-700 transition-colors"
          > 
            <MapPin size={20} /> 
            <span>العناوين</span> 
          </button>
        </div>

        {/* بطاقات الإحصائيات */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
          <div className="bg-white rounded-2xl shadow-lg p-6 border border-purple-200 transition-transform duration-300 hover:scale-[1.02] hover:shadow-purple-200/60">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-purple-700 text-sm font-bold mb-2">إجمالي الموردين</p>
                <p className="text-3xl font-bold text-purple-600">{totalSuppliers}</p>
              </div>
              <div className="bg-purple-100 p-3 rounded-xl">
                <Users className="h-8 w-8 text-purple-600" />
              </div>
            </div>
          </div>
        </div>

        {/* شريط البحث */}
        <div className="bg-white rounded-2xl shadow-md p-4 mb-6 border border-purple-100 flex items-center justify-between">
          <div className="relative flex-1 max-w-md">
            <input
              type="text"
              placeholder="البحث في الموردين..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-transparent"
            />
            <Search className="absolute left-3 top-3 h-5 w-5 text-gray-400" />
          </div>
          <button
            onClick={() => setShowAddModal(true)}
            className="bg-gradient-to-r from-purple-400 to-purple-600 text-white px-6 py-3 rounded-xl font-semibold flex items-center gap-2 shadow-md hover:shadow-lg border-2 border-purple-400 hover:border-purple-500 transform hover:scale-105 transition-all"
          >
            <Plus size={20} />
            <span>إضافة مورد جديد</span>
          </button>
        </div>

        {/* جدول الموردين */}
        <div className="bg-white rounded-2xl shadow-xl border border-purple-100 overflow-hidden">
          <div className="px-6 py-4 border-b border-purple-200 bg-gradient-to-r from-purple-50 to-pink-50">
            <h3 className="text-lg font-bold text-purple-700 drop-shadow">قائمة الموردين</h3>
          </div>

          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-4 text-right text-sm font-bold text-purple-700">المورد</th>
                  <th className="px-6 py-4 text-right text-sm font-bold text-purple-700">الهاتف</th>
                  <th className="px-6 py-4 text-right text-sm font-bold text-purple-700">البريد الإلكتروني</th>
                  <th className="px-6 py-4 text-right text-sm font-bold text-purple-700">المنتجات</th>
                  <th className="px-6 py-4 text-right text-sm font-bold text-purple-700">طريقة الدفع</th>
                  <th className="px-6 py-4 text-right text-sm font-bold text-purple-700">الإجراءات</th>
                </tr>
              </thead>
              <tbody className="divide-y divide-purple-50">
                {filteredSuppliers.map((supplier) => (
                  <tr key={supplier.id} className="hover:bg-purple-50 transition-colors">
                    <td className="px-6 py-4">
                      <div className="flex items-center">
                        <div className="w-10 h-10 bg-purple-100 rounded-full flex items-center justify-center">
                          <span className="text-purple-600 font-semibold">
                            {supplier.name.charAt(0)}
                          </span>
                        </div>
                        <div className="mr-4">
                          <div className="text-sm font-bold text-purple-800">{supplier.name}</div>
                          <div className="text-sm text-purple-400">{supplier.address}</div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 text-sm text-purple-800">{supplier.phone}</td>
                    <td className="px-6 py-4 text-sm text-purple-800">{supplier.email}</td>
                    <td className="px-6 py-4 text-sm text-purple-800">
                      <div className="max-w-xs truncate" title={supplier.materials?.join(', ')}>
                        {supplier.materials?.join(', ')}
                      </div>
                    </td>
                    <td className="px-6 py-4 text-sm text-purple-800">{supplier.paymentMethod}</td>
                    <td className="px-6 py-4">
                      <div className="flex items-center gap-2">
                        <button
                          onClick={() => setEditingSupplier(supplier)}
                          className="bg-purple-100 text-purple-700 px-3 py-1 rounded-xl text-xs font-bold hover:bg-purple-200 transition-colors shadow"
                        >
                          تعديل
                        </button>
                        <button
                          onClick={() => onSupplierDelete(supplier.id)}
                          className="bg-red-100 text-red-700 px-3 py-1 rounded-xl text-xs font-bold hover:bg-red-200 transition-colors shadow"
                        >
                          حذف
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>

        {/* نافذة إضافة/تعديل مورد */}
        {(showAddModal || editingSupplier) && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
            <div className="bg-white rounded-2xl shadow-2xl max-w-md w-full border-2 border-purple-200">
              <div className="bg-gradient-to-r from-purple-600 to-pink-500 text-white p-6 rounded-t-2xl">
                <h2 className="text-xl font-bold">
                  {editingSupplier ? 'تعديل مورد' : 'إضافة مورد جديد'}
                </h2>
              </div>
              
              <form onSubmit={handleSubmit} className="p-6 space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">اسم الشركة</label>
                  <input
                    type="text"
                    name="name"
                    defaultValue={editingSupplier?.name}
                    required
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">رقم الهاتف</label>
                  <input
                    type="tel"
                    name="phone"
                    defaultValue={editingSupplier?.phone}
                    required
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">البريد الإلكتروني</label>
                  <input
                    type="email"
                    name="email"
                    defaultValue={editingSupplier?.email}
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">العنوان</label>
                  <input
                    type="text"
                    name="address"
                    defaultValue={editingSupplier?.address}
                    required
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">المنتجات/الخدمات (افصل بينها بفاصلة)</label>
                  <textarea
                    name="materials"
                    defaultValue={editingSupplier?.materials?.join(', ')}
                    required
                    rows={2}
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                    placeholder="مثال: أسمدة، مبيدات، صناديق تعبئة"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">طريقة الدفع</label>
                  <input
                    type="text"
                    name="paymentMethod"
                    defaultValue={editingSupplier?.paymentMethod}
                    required
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">ملاحظات</label>
                  <textarea
                    name="notes"
                    defaultValue={editingSupplier?.notes}
                    rows={2}
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                  />
                </div>
                <div className="flex gap-3 pt-4">
                  <button
                    type="submit"
                    className="flex-1 bg-gradient-to-r from-purple-400 to-purple-600 text-white py-3 px-4 rounded-xl font-bold hover:from-purple-500 hover:to-purple-700 transition-all shadow"
                  >
                    {editingSupplier ? 'تحديث' : 'إضافة'}
                  </button>
                  <button
                    type="button"
                    onClick={() => {
                      setShowAddModal(false);
                      setEditingSupplier(null);
                    }}
                    className="flex-1 bg-gray-200 text-gray-700 py-3 px-4 rounded-xl font-bold hover:bg-gray-300 transition-all shadow"
                  >
                    إلغاء
                  </button>
                </div>
              </form>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default SuppliersView;