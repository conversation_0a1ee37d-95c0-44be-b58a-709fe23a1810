// ===== نظام التحقق من صحة البيانات =====

export interface ValidationResult {
  isValid: boolean;
  errors: string[];
}

// التحقق من البريد الإلكتروني
export const validateEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

// التحقق من رقم الهاتف (أردني)
export const validatePhone = (phone: string): boolean => {
  const phoneRegex = /^(\+962|0)?[7-9][0-9]{8}$/;
  return phoneRegex.test(phone.replace(/\s/g, ''));
};

// التحقق من الأرقام الموجبة
export const validatePositiveNumber = (value: number): boolean => {
  return !isNaN(value) && value > 0;
};

// التحقق من التاريخ
export const validateDate = (date: string): boolean => {
  const dateObj = new Date(date);
  return dateObj instanceof Date && !isNaN(dateObj.getTime());
};

// التحقق من النص المطلوب
export const validateRequiredText = (text: string, minLength = 1): boolean => {
  return text.trim().length >= minLength;
};

// التحقق من بيانات الموظف
export const validateEmployee = (employee: {
  name: string;
  phone: string;
  position: string;
  salary: number;
  hireDate: string;
}): ValidationResult => {
  const errors: string[] = [];

  if (!validateRequiredText(employee.name, 2)) {
    errors.push('اسم الموظف مطلوب ويجب أن يكون أكثر من حرفين');
  }

  if (!validatePhone(employee.phone)) {
    errors.push('رقم الهاتف غير صحيح');
  }

  if (!validateRequiredText(employee.position)) {
    errors.push('المنصب مطلوب');
  }

  if (!validatePositiveNumber(employee.salary)) {
    errors.push('الراتب يجب أن يكون رقم موجب');
  }

  if (!validateDate(employee.hireDate)) {
    errors.push('تاريخ التوظيف غير صحيح');
  }

  return {
    isValid: errors.length === 0,
    errors
  };
};

// التحقق من بيانات المورد
export const validateSupplier = (supplier: {
  name: string;
  phone: string;
  email?: string;
}): ValidationResult => {
  const errors: string[] = [];

  if (!validateRequiredText(supplier.name, 2)) {
    errors.push('اسم المورد مطلوب ويجب أن يكون أكثر من حرفين');
  }

  if (!validatePhone(supplier.phone)) {
    errors.push('رقم الهاتف غير صحيح');
  }

  if (supplier.email && !validateEmail(supplier.email)) {
    errors.push('البريد الإلكتروني غير صحيح');
  }

  return {
    isValid: errors.length === 0,
    errors
  };
};

// التحقق من بيانات المبيعات
export const validateSale = (sale: {
  customerName: string;
  customerPhone: string;
  amount: number;
  quantity: number;
  date: string;
}): ValidationResult => {
  const errors: string[] = [];

  if (!validateRequiredText(sale.customerName, 2)) {
    errors.push('اسم العميل مطلوب');
  }

  if (!validatePhone(sale.customerPhone)) {
    errors.push('رقم هاتف العميل غير صحيح');
  }

  if (!validatePositiveNumber(sale.amount)) {
    errors.push('المبلغ يجب أن يكون رقم موجب');
  }

  if (!validatePositiveNumber(sale.quantity)) {
    errors.push('الكمية يجب أن تكون رقم موجب');
  }

  if (!validateDate(sale.date)) {
    errors.push('تاريخ البيع غير صحيح');
  }

  return {
    isValid: errors.length === 0,
    errors
  };
};

// التحقق من بيانات المخزون
export const validateInventoryItem = (item: {
  name: string;
  quantity: number;
  minQuantity: number;
  supplier: string;
}): ValidationResult => {
  const errors: string[] = [];

  if (!validateRequiredText(item.name, 2)) {
    errors.push('اسم المادة مطلوب');
  }

  if (!validatePositiveNumber(item.quantity)) {
    errors.push('الكمية يجب أن تكون رقم موجب');
  }

  if (!validatePositiveNumber(item.minQuantity)) {
    errors.push('الحد الأدنى للكمية يجب أن يكون رقم موجب');
  }

  if (!validateRequiredText(item.supplier)) {
    errors.push('اسم المورد مطلوب');
  }

  return {
    isValid: errors.length === 0,
    errors
  };
};

// دالة عامة لعرض أخطاء التحقق
export const showValidationErrors = (errors: string[], showToast?: (title: string, message: string) => void) => {
  if (errors.length > 0 && showToast) {
    showToast('خطأ في البيانات', errors.join('\n'));
  }
  return errors.length === 0;
};