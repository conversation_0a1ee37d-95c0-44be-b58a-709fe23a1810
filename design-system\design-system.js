/**
 * نظام التصميم - Lettuce Farm Management System
 * Design System for React Applications
 */

// ========================================
// 1. لوحة الألوان
// ========================================
export const colors = {
  // الألوان الأساسية
  primary: {
    50: '#ecfdf5',
    100: '#d1fae5',
    200: '#a7f3d0',
    300: '#6ee7b7',
    400: '#34d399',
    500: '#10b981',
    600: '#059669',
    700: '#047857',
    800: '#065f46',
    900: '#064e3b',
    950: '#022c22',
  },
  secondary: {
    50: '#eff6ff',
    100: '#dbeafe',
    200: '#bfdbfe',
    300: '#93c5fd',
    400: '#60a5fa',
    500: '#3b82f6',
    600: '#2563eb',
    700: '#1d4ed8',
    800: '#1e40af',
    900: '#1e3a8a',
    950: '#172554',
  },
  accent: {
    50: '#fffbeb',
    100: '#fef3c7',
    200: '#fde68a',
    300: '#fcd34d',
    400: '#fbbf24',
    500: '#f59e0b',
    600: '#d97706',
    700: '#b45309',
    800: '#92400e',
    900: '#78350f',
    950: '#451a03',
  },
  success: {
    50: '#f0fdf4',
    100: '#dcfce7',
    200: '#bbf7d0',
    300: '#86efac',
    400: '#4ade80',
    500: '#22c55e',
    600: '#16a34a',
    700: '#15803d',
    800: '#166534',
    900: '#14532d',
    950: '#052e16',
  },
  warning: {
    50: '#fffbeb',
    100: '#fef3c7',
    200: '#fde68a',
    300: '#fcd34d',
    400: '#fbbf24',
    500: '#f59e0b',
    600: '#d97706',
    700: '#b45309',
    800: '#92400e',
    900: '#78350f',
    950: '#451a03',
  },
  error: {
    50: '#fef2f2',
    100: '#fee2e2',
    200: '#fecaca',
    300: '#fca5a5',
    400: '#f87171',
    500: '#ef4444',
    600: '#dc2626',
    700: '#b91c1c',
    800: '#991b1b',
    900: '#7f1d1d',
    950: '#450a0a',
  },
  background: {
    primary: '#f8fafc',
    secondary: '#ffffff',
  },
  text: {
    primary: '#1e293b',
    secondary: '#64748b',
  },
  border: {
    primary: '#e2e8f0',
    secondary: '#d1d5db',
  },
  status: {
    active: {
      bg: '#dcfce7',
      text: '#166534',
    },
    inactive: {
      bg: '#f1f5f9',
      text: '#1e293b',
    },
    warning: {
      bg: '#fef3c7',
      text: '#92400e',
    },
    error: {
      bg: '#fee2e2',
      text: '#991b1b',
    },
  },
};

// ========================================
// 2. الخطوط
// ========================================
export const fonts = {
  family: {
    arabic: ['Cairo', 'Tajawal', 'Noto Sans Arabic', 'Amiri', 'system-ui', 'sans-serif'],
    cairo: ['Cairo', 'system-ui', 'sans-serif'],
    tajawal: ['Tajawal', 'system-ui', 'sans-serif'],
    amiri: ['Amiri', 'system-ui', 'serif'],
    notoArabic: ['Noto Sans Arabic', 'system-ui', 'sans-serif'],
    sans: ['Cairo', 'Tajawal', 'Noto Sans Arabic', 'system-ui', '-apple-system', 'BlinkMacSystemFont', 'Segoe UI', 'Roboto', 'sans-serif'],
  },
  weight: {
    extralight: 200,
    light: 300,
    normal: 400,
    medium: 500,
    semibold: 600,
    bold: 700,
    extrabold: 800,
    black: 900,
  },
  size: {
    xs: '0.75rem',
    sm: '0.875rem',
    base: '1rem',
    lg: '1.125rem',
    xl: '1.25rem',
    '2xl': '1.5rem',
    '3xl': '1.875rem',
    '4xl': '2.25rem',
    '5xl': '3rem',
    '6xl': '3.75rem',
  },
  lineHeight: {
    xs: '1rem',
    sm: '1.25rem',
    base: '1.5rem',
    lg: '1.75rem',
    xl: '1.75rem',
    '2xl': '2rem',
    '3xl': '2.25rem',
    '4xl': '2.5rem',
  },
  letterSpacing: {
    tighter: '-0.02em',
    tight: '-0.01em',
    normal: '0',
    wide: '0.01em',
    wider: '0.02em',
    widest: '0.03em',
  },
};

// ========================================
// 3. المسافات والتباعد
// ========================================
export const spacing = {
  px: '1px',
  0: '0',
  0.5: '0.125rem',
  1: '0.25rem',
  1.5: '0.375rem',
  2: '0.5rem',
  2.5: '0.625rem',
  3: '0.75rem',
  3.5: '0.875rem',
  4: '1rem',
  5: '1.25rem',
  6: '1.5rem',
  7: '1.75rem',
  8: '2rem',
  9: '2.25rem',
  10: '2.5rem',
  11: '2.75rem',
  12: '3rem',
  14: '3.5rem',
  16: '4rem',
  20: '5rem',
  24: '6rem',
  28: '7rem',
  32: '8rem',
  36: '9rem',
  40: '10rem',
  44: '11rem',
  48: '12rem',
  52: '13rem',
  56: '14rem',
  60: '15rem',
  64: '16rem',
  72: '18rem',
  80: '20rem',
  96: '24rem',
  full: '100%',
  screen: '100vw',
  min: 'min-content',
  max: 'max-content',
  fit: 'fit-content',
};

// ========================================
// 4. الحواف والزوايا
// ========================================
export const borderRadius = {
  none: '0',
  sm: '0.125rem',
  default: '0.25rem',
  md: '0.375rem',
  lg: '0.5rem',
  xl: '0.75rem',
  '2xl': '1rem',
  '3xl': '1.5rem',
  full: '9999px',
};

// ========================================
// 5. الظلال
// ========================================
export const shadows = {
  sm: '0 1px 2px 0 rgba(0, 0, 0, 0.05)',
  default: '0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)',
  md: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
  lg: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
  xl: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
  '2xl': '0 25px 50px -12px rgba(0, 0, 0, 0.25)',
  inner: 'inset 0 2px 4px 0 rgba(0, 0, 0, 0.06)',
  none: 'none',
  primary: '0 0 0 3px rgba(5, 150, 105, 0.1)',
  secondary: '0 0 0 3px rgba(37, 99, 235, 0.1)',
  accent: '0 0 0 3px rgba(245, 158, 11, 0.1)',
  success: '0 0 0 3px rgba(16, 185, 129, 0.1)',
  warning: '0 0 0 3px rgba(245, 158, 11, 0.1)',
  error: '0 0 0 3px rgba(239, 68, 68, 0.1)',
};

// ========================================
// 6. المؤثرات الحركية والانتقالات
// ========================================
export const animations = {
  fadeIn: 'fadeIn 0.3s ease-out',
  slideIn: 'slideIn 0.3s ease-out',
  pulse: 'pulse 2s infinite',
  bounce: 'bounce 1s infinite',
  spin: 'spin 1s linear infinite',
  ping: 'ping 1s cubic-bezier(0, 0, 0.2, 1) infinite',
};

export const transitions = {
  duration: {
    150: '150ms',
    200: '200ms',
    300: '300ms',
    500: '500ms',
    700: '700ms',
    1000: '1000ms',
  },
  timing: {
    ease: 'cubic-bezier(0.25, 0.46, 0.45, 0.94)',
    easeIn: 'cubic-bezier(0.4, 0, 1, 1)',
    easeOut: 'cubic-bezier(0, 0, 0.2, 1)',
    easeInOut: 'cubic-bezier(0.4, 0, 0.2, 1)',
    linear: 'linear',
  },
  property: {
    all: 'all',
    colors: 'color, background-color, border-color, text-decoration-color, fill, stroke',
    opacity: 'opacity',
    shadow: 'box-shadow',
    transform: 'transform',
  },
};

// ========================================
// 7. أنماط الأزرار
// ========================================
export const buttonStyles = {
  base: {
    display: 'inline-flex',
    alignItems: 'center',
    justifyContent: 'center',
    fontWeight: 500,
    padding: '0.5rem 1rem',
    borderRadius: '0.5rem',
    transition: 'all 0.2s',
    cursor: 'pointer',
    fontSize: '0.875rem',
    lineHeight: '1.25rem',
    border: 'none',
    textDecoration: 'none',
  },
  primary: {
    backgroundColor: colors.primary[600],
    color: 'white',
    boxShadow: shadows.sm,
  },
  primaryHover: {
    backgroundColor: colors.primary[700],
    boxShadow: shadows.md,
  },
  secondary: {
    backgroundColor: colors.secondary[600],
    color: 'white',
    boxShadow: shadows.sm,
  },
  secondaryHover: {
    backgroundColor: colors.secondary[700],
    boxShadow: shadows.md,
  },
  success: {
    backgroundColor: colors.success[600],
    color: 'white',
    boxShadow: shadows.sm,
  },
  successHover: {
    backgroundColor: colors.success[700],
    boxShadow: shadows.md,
  },
  warning: {
    backgroundColor: colors.warning[500],
    color: 'white',
    boxShadow: shadows.sm,
  },
  warningHover: {
    backgroundColor: colors.warning[600],
    boxShadow: shadows.md,
  },
  danger: {
    backgroundColor: colors.error[600],
    color: 'white',
    boxShadow: shadows.sm,
  },
  dangerHover: {
    backgroundColor: colors.error[700],
    boxShadow: shadows.md,
  },
  outline: {
    border: '2px solid',
    borderColor: colors.border.secondary,
    color: colors.text.primary,
    backgroundColor: 'transparent',
  },
  outlineHover: {
    borderColor: colors.text.secondary,
    color: colors.text.primary,
  },
};

// ========================================
// 8. أنماط البطاقات
// ========================================
export const cardStyles = {
  base: {
    backgroundColor: colors.background.secondary,
    borderRadius: borderRadius.xl,
    boxShadow: shadows.sm,
    border: `1px solid ${colors.border.primary}`,
    padding: spacing[6],
  },
  hover: {
    boxShadow: shadows.md,
    transition: 'box-shadow 0.2s',
  },
  dashboard: {
    backgroundColor: colors.background.secondary,
    borderRadius: borderRadius.xl,
    boxShadow: shadows.sm,
    border: `1px solid ${colors.border.primary}`,
    padding: spacing[6],
    transition: 'all 0.2s',
  },
  zone: {
    backgroundColor: colors.background.secondary,
    borderRadius: borderRadius.xl,
    boxShadow: shadows.sm,
    border: `1px solid ${colors.border.primary}`,
    padding: spacing[4],
    transition: 'all 0.2s',
    cursor: 'pointer',
  },
};

// ========================================
// 9. أنماط حقول الإدخال
// ========================================
export const inputStyles = {
  base: {
    width: '100%',
    padding: '0.5rem 0.75rem',
    border: `1px solid ${colors.border.secondary}`,
    borderRadius: borderRadius.lg,
    transition: 'all 0.2s',
    fontSize: fonts.size.sm,
    lineHeight: fonts.lineHeight.sm,
    backgroundColor: colors.background.secondary,
    color: colors.text.primary,
  },
  focus: {
    outline: 'none',
    borderColor: colors.primary[600],
    boxShadow: shadows.primary,
  },
  error: {
    borderColor: colors.error[500],
    boxShadow: shadows.error,
  },
};

// ========================================
// 10. أنماط النوافذ المنبثقة
// ========================================
export const modalStyles = {
  overlay: {
    position: 'fixed',
    inset: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    backdropFilter: 'blur(4px)',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    zIndex: 50,
    padding: spacing[4],
  },
  content: {
    backgroundColor: colors.background.secondary,
    borderRadius: borderRadius['2xl'],
    boxShadow: shadows['2xl'],
    padding: spacing[6],
    width: '100%',
    maxWidth: '32rem',
    margin: `0 ${spacing[4]}`,
    maxHeight: '90vh',
    overflowY: 'auto',
    transform: 'translateZ(0)',
    transition: 'all 0.3s',
  },
};

// ========================================
// 11. أنماط الجداول
// ========================================
export const tableStyles = {
  container: {
    overflowX: 'auto',
    boxShadow: shadows.sm,
    borderRadius: borderRadius.lg,
    border: `1px solid ${colors.border.primary}`,
  },
  table: {
    minWidth: '100%',
    borderCollapse: 'collapse',
  },
  header: {
    backgroundColor: colors.background.primary,
  },
  headerCell: {
    padding: '0.75rem 1.5rem',
    textAlign: 'right',
    fontSize: fonts.size.xs,
    fontWeight: fonts.weight.medium,
    color: colors.text.secondary,
    textTransform: 'uppercase',
    letterSpacing: fonts.letterSpacing.wide,
  },
  body: {
    backgroundColor: colors.background.secondary,
  },
  row: {
    transition: 'background-color 0.15s',
  },
  rowHover: {
    backgroundColor: colors.background.primary,
  },
  cell: {
    padding: '1rem 1.5rem',
    whiteSpace: 'nowrap',
    fontSize: fonts.size.sm,
    color: colors.text.primary,
  },
};

// ========================================
// 12. أنماط التنقل
// ========================================
export const navigationStyles = {
  item: {
    display: 'flex',
    alignItems: 'center',
    padding: '0.75rem 1rem',
    color: colors.text.primary,
    borderRadius: borderRadius.lg,
    transition: 'all 0.2s',
    cursor: 'pointer',
    textDecoration: 'none',
  },
  itemHover: {
    backgroundColor: colors.primary[50],
    color: colors.primary[600],
  },
  itemActive: {
    backgroundColor: colors.primary[100],
    color: colors.primary[600],
    fontWeight: fonts.weight.medium,
  },
  icon: {
    width: '1.25rem',
    height: '1.25rem',
    marginLeft: '0.75rem',
  },
};

// ========================================
// 13. أنماط حالات الحالة
// ========================================
export const statusStyles = {
  active: {
    backgroundColor: colors.status.active.bg,
    color: colors.status.active.text,
    padding: '0.25rem 0.5rem',
    borderRadius: borderRadius.full,
    fontSize: fonts.size.sm,
    fontWeight: fonts.weight.medium,
  },
  inactive: {
    backgroundColor: colors.status.inactive.bg,
    color: colors.status.inactive.text,
    padding: '0.25rem 0.5rem',
    borderRadius: borderRadius.full,
    fontSize: fonts.size.sm,
    fontWeight: fonts.weight.medium,
  },
  warning: {
    backgroundColor: colors.status.warning.bg,
    color: colors.status.warning.text,
    padding: '0.25rem 0.5rem',
    borderRadius: borderRadius.full,
    fontSize: fonts.size.sm,
    fontWeight: fonts.weight.medium,
  },
  error: {
    backgroundColor: colors.status.error.bg,
    color: colors.status.error.text,
    padding: '0.25rem 0.5rem',
    borderRadius: borderRadius.full,
    fontSize: fonts.size.sm,
    fontWeight: fonts.weight.medium,
  },
};

// ========================================
// 14. الخلفيات المتدرجة
// ========================================
export const gradients = {
  primary: 'linear-gradient(135deg, #059669 0%, #10b981 100%)',
  secondary: 'linear-gradient(135deg, #2563eb 0%, #3b82f6 100%)',
  success: 'linear-gradient(135deg, #16a34a 0%, #22c55e 100%)',
  warning: 'linear-gradient(135deg, #eab308 0%, #f59e0b 100%)',
  danger: 'linear-gradient(135deg, #dc2626 0%, #ef4444 100%)',
  toBr: 'linear-gradient(to bottom right, var(--tw-gradient-stops))',
  toR: 'linear-gradient(to right, var(--tw-gradient-stops))',
};

// ========================================
// 15. نقاط التوقف للتصميم المتجاوب
// ========================================
export const breakpoints = {
  xs: '475px',
  sm: '640px',
  md: '768px',
  lg: '1024px',
  xl: '1280px',
  '2xl': '1536px',
};

// ========================================
// 16. الأيقونات المستخدمة
// ========================================
export const icons = {
  // من مكتبة Lucide React
  navigation: [
    'BarChart3', 'Map', 'Leaf', 'Droplets', 'FlaskConical', 'Package', 
    'Users', 'Truck', 'DollarSign', 'ShoppingCart', 'ClipboardList', 
    'Settings', 'TrendingUp', 'Home', 'Calendar', 'FileText', 'Printer',
    'Download', 'Upload', 'Search', 'Filter', 'Eye', 'Edit', 'Plus',
    'Minus', 'Trash2', 'Save', 'X', 'CheckCircle', 'AlertTriangle',
    'Clock', 'Activity', 'Target', 'Zap', 'TreePine', 'Sprout'
  ],
  source: 'lucide-react',
  size: {
    xs: '0.75rem',
    sm: '1rem',
    md: '1.25rem',
    lg: '1.5rem',
    xl: '2rem',
    '2xl': '2.5rem',
  },
};

// ========================================
// 17. نظام التصميم الكامل
// ========================================
export const designSystem = {
  colors,
  fonts,
  spacing,
  borderRadius,
  shadows,
  animations,
  transitions,
  buttonStyles,
  cardStyles,
  inputStyles,
  modalStyles,
  tableStyles,
  navigationStyles,
  statusStyles,
  gradients,
  breakpoints,
  icons,
  
  // دوال مساعدة
  getColor: (colorPath) => {
    const path = colorPath.split('.');
    let result = colors;
    for (const key of path) {
      result = result[key];
    }
    return result;
  },
  
  getSpacing: (size) => spacing[size] || size,
  
  getFontSize: (size) => fonts.size[size] || size,
  
  getBorderRadius: (size) => borderRadius[size] || size,
  
  getShadow: (type) => shadows[type] || shadows.default,
  
  // توليد أنماط CSS
  generateCSS: () => {
    return `
      :root {
        --primary-color: ${colors.primary[600]};
        --primary-dark: ${colors.primary[700]};
        --primary-light: ${colors.primary[500]};
        --secondary-color: ${colors.secondary[600]};
        --accent-color: ${colors.accent[500]};
        --success-color: ${colors.success[600]};
        --warning-color: ${colors.warning[500]};
        --error-color: ${colors.error[600]};
        --background-color: ${colors.background.primary};
        --surface-color: ${colors.background.secondary};
        --text-primary: ${colors.text.primary};
        --text-secondary: ${colors.text.secondary};
        --border-color: ${colors.border.primary};
      }
    `;
  },
};

export default designSystem; 