@echo off
echo ========================================
echo    نظام إدارة مزرعة الخس
echo    شركة الشفق للزراعة الحديثة
echo ========================================
echo.

echo جاري تشغيل المشروع...
echo.

REM التحقق من وجود Node.js
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo خطأ: Node.js غير مثبت
    echo يرجى تثبيت Node.js من https://nodejs.org/
    pause
    exit /b 1
)

REM التحقق من وجود التبعيات
if not exist "node_modules" (
    echo تثبيت التبعيات...
    npm install
    if %errorlevel% neq 0 (
        echo خطأ في تثبيت التبعيات
        pause
        exit /b 1
    )
)

echo تشغيل المشروع في وضع التطوير...
echo.
echo سيتم فتح المتصفح تلقائياً على العنوان:
echo http://localhost:5173
echo.
echo للخروج من المشروع، اضغط Ctrl+C
echo.

npm run dev

pause 