import React from 'react';
import { Loader2, Leaf } from 'lucide-react';

interface LoadingSpinnerProps {
  size?: 'sm' | 'md' | 'lg' | 'xl';
  message?: string;
  type?: 'spinner' | 'pulse' | 'farm' | 'dots';
  color?: 'emerald' | 'blue' | 'gray';
  overlay?: boolean;
  className?: string;
}

export const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({
  size = 'md',
  message,
  type = 'spinner',
  color = 'emerald',
  overlay = false,
  className = ''
}) => {
  // أحجام مختلفة
  const sizeClasses = {
    sm: 'w-4 h-4',
    md: 'w-8 h-8',
    lg: 'w-12 h-12',
    xl: 'w-16 h-16'
  };

  // ألوان مختلفة
  const colorClasses = {
    emerald: 'text-emerald-600',
    blue: 'text-blue-600',
    gray: 'text-gray-600'
  };

  // أنواع مختلفة من الـ Loading
  const renderSpinner = () => {
    const spinnerClass = `${sizeClasses[size]} ${colorClasses[color]}`;

    switch (type) {
      case 'spinner':
        return <Loader2 className={`${spinnerClass} animate-spin`} />;
      
      case 'pulse':
        return (
          <div className={`${sizeClasses[size]} ${colorClasses[color]} bg-current rounded-full animate-pulse opacity-75`} />
        );
      
      case 'farm':
        return <Leaf className={`${spinnerClass} animate-bounce`} />;
      
      case 'dots':
        return (
          <div className="flex space-x-1">
            {[0, 1, 2].map((i) => (
              <div
                key={i}
                className={`w-2 h-2 ${colorClasses[color]} bg-current rounded-full animate-bounce`}
                style={{ animationDelay: `${i * 0.1}s` }}
              />
            ))}
          </div>
        );
      
      default:
        return <Loader2 className={`${spinnerClass} animate-spin`} />;
    }
  };

  const content = (
    <div className={`flex flex-col items-center justify-center gap-3 ${className}`}>
      {renderSpinner()}
      {message && (
        <p className={`text-sm font-medium ${colorClasses[color]} animate-pulse`}>
          {message}
        </p>
      )}
    </div>
  );

  if (overlay) {
    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div className="bg-white rounded-lg p-8 shadow-2xl">
          {content}
        </div>
      </div>
    );
  }

  return content;
};

// مكون تحميل للجداول
export const TableLoadingSkeleton: React.FC<{
  rows?: number;
  columns?: number;
  className?: string;
}> = ({ rows = 5, columns = 4, className = '' }) => {
  return (
    <div className={`animate-pulse ${className}`}>
      {Array.from({ length: rows }).map((_, rowIndex) => (
        <div key={rowIndex} className="flex gap-4 py-4 border-b border-gray-200">
          {Array.from({ length: columns }).map((_, colIndex) => (
            <div
              key={colIndex}
              className="flex-1 h-4 bg-gray-200 rounded"
              style={{ width: Math.random() * 40 + 60 + '%' }}
            />
          ))}
        </div>
      ))}
    </div>
  );
};

// مكون تحميل للبطاقات
export const CardLoadingSkeleton: React.FC<{
  count?: number;
  className?: string;
}> = ({ count = 6, className = '' }) => {
  return (
    <div className={`grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 ${className}`}>
      {Array.from({ length: count }).map((_, index) => (
        <div key={index} className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 animate-pulse">
          <div className="flex items-center gap-3 mb-4">
            <div className="w-10 h-10 bg-gray-200 rounded-full" />
            <div className="flex-1">
              <div className="h-4 bg-gray-200 rounded mb-2" />
              <div className="h-3 bg-gray-200 rounded w-2/3" />
            </div>
          </div>
          <div className="space-y-3">
            <div className="h-3 bg-gray-200 rounded" />
            <div className="h-3 bg-gray-200 rounded w-5/6" />
            <div className="h-3 bg-gray-200 rounded w-4/6" />
          </div>
          <div className="flex justify-between items-center mt-4 pt-4 border-t border-gray-200">
            <div className="h-3 bg-gray-200 rounded w-1/4" />
            <div className="h-8 bg-gray-200 rounded w-20" />
          </div>
        </div>
      ))}
    </div>
  );
};

// مكون تحميل للإحصائيات
export const StatsLoadingSkeleton: React.FC<{
  count?: number;
  className?: string;
}> = ({ count = 4, className = '' }) => {
  return (
    <div className={`grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 ${className}`}>
      {Array.from({ length: count }).map((_, index) => (
        <div key={index} className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 animate-pulse">
          <div className="flex items-center justify-between mb-4">
            <div className="w-8 h-8 bg-gray-200 rounded" />
            <div className="w-6 h-6 bg-gray-200 rounded-full" />
          </div>
          <div className="space-y-2">
            <div className="h-8 bg-gray-200 rounded w-1/2" />
            <div className="h-4 bg-gray-200 rounded w-3/4" />
          </div>
        </div>
      ))}
    </div>
  );
};

// مكون تحميل مخصص للمزرعة
export const FarmLoadingAnimation: React.FC<{
  message?: string;
  className?: string;
}> = ({ message = 'جاري تحميل بيانات المزرعة...', className = '' }) => {
  return (
    <div className={`flex flex-col items-center justify-center py-12 ${className}`}>
      <div className="relative">
        {/* الخلفية المتحركة */}
        <div className="w-24 h-24 bg-gradient-to-br from-emerald-100 to-emerald-200 rounded-full animate-pulse" />
        
        {/* أيقونة الورقة المتحركة */}
        <div className="absolute inset-0 flex items-center justify-center">
          <Leaf className="w-12 h-12 text-emerald-600 animate-bounce" />
        </div>
        
        {/* دوائر متحركة */}
        <div className="absolute -inset-4">
          <div className="w-32 h-32 border-2 border-emerald-200 rounded-full animate-spin" style={{ animationDuration: '3s' }} />
        </div>
        <div className="absolute -inset-8">
          <div className="w-40 h-40 border border-emerald-100 rounded-full animate-spin" style={{ animationDuration: '4s', animationDirection: 'reverse' }} />
        </div>
      </div>
      
      <div className="mt-6 text-center">
        <p className="text-lg font-semibold text-emerald-700 mb-2">شركة الشفق للزراعة الحديثة</p>
        <p className="text-sm text-gray-600 animate-pulse">{message}</p>
      </div>
      
      {/* شريط التقدم المتحرك */}
      <div className="w-48 h-1 bg-emerald-100 rounded-full mt-4 overflow-hidden">
        <div className="h-full bg-emerald-500 rounded-full animate-pulse" style={{ 
          animation: 'progress 2s ease-in-out infinite',
          width: '30%'
        }} />
      </div>
      
      <style jsx>{`
        @keyframes progress {
          0% { transform: translateX(-100%); }
          50% { transform: translateX(300%); }
          100% { transform: translateX(-100%); }
        }
      `}</style>
    </div>
  );
};