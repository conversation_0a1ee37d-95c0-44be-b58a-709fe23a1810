import { StrictMode } from 'react';
import { createRoot } from 'react-dom/client';
import App from './App.tsx';
import './index.css';
import { enhancedDB } from './utils/enhancedDatabase';

// تهيئة قاعدة البيانات المحسنة
const initializeApp = async () => {
  try {
    console.log('🔄 جاري تهيئة قاعدة البيانات...');
    await enhancedDB.init();
    console.log('✅ تم تهيئة قاعدة البيانات بنجاح');
    
    // تشغيل التطبيق
    createRoot(document.getElementById('root')!).render(
      <StrictMode>
        <App />
      </StrictMode>
    );
  } catch (error) {
    console.error('❌ خطأ في تهيئة قاعدة البيانات:', error);
    
    // عرض رسالة خطأ للمستخدم
    document.getElementById('root')!.innerHTML = `
      <div style="
        display: flex; 
        align-items: center; 
        justify-content: center; 
        height: 100vh; 
        background: linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%);
        font-family: 'Cairo', sans-serif;
        direction: rtl;
      ">
        <div style="
          text-align: center; 
          padding: 2rem; 
          background: white; 
          border-radius: 1rem; 
          box-shadow: 0 10px 25px rgba(0,0,0,0.1);
          max-width: 400px;
        ">
          <div style="color: #dc2626; font-size: 3rem; margin-bottom: 1rem;">⚠️</div>
          <h2 style="color: #374151; margin-bottom: 1rem;">خطأ في تهيئة التطبيق</h2>
          <p style="color: #6b7280; margin-bottom: 1.5rem;">
            تعذر تهيئة قاعدة البيانات. يرجى إعادة تحميل الصفحة أو الاتصال بالدعم التقني.
          </p>
          <button 
            onclick="window.location.reload()" 
            style="
              background: #059669; 
              color: white; 
              border: none; 
              padding: 0.75rem 1.5rem; 
              border-radius: 0.5rem; 
              cursor: pointer; 
              font-weight: bold;
              font-size: 1rem;
            "
          >
            إعادة المحاولة
          </button>
        </div>
      </div>
    `;
  }
};

// بدء تهيئة التطبيق
initializeApp();
