@echo off
echo ========================================
echo    نظام إدارة مزرعة الخس
echo    تطبيق سطح المكتب
echo    شركة الشفق للزراعة الحديثة
echo ========================================
echo.

echo جاري تشغيل تطبيق سطح المكتب...
echo.

REM التحقق من وجود Node.js
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo خطأ: Node.js غير مثبت
    echo يرجى تثبيت Node.js من https://nodejs.org/
    pause
    exit /b 1
)

REM التحقق من وجود التبعيات
if not exist "node_modules" (
    echo تثبيت التبعيات...
    npm install
    if %errorlevel% neq 0 (
        echo خطأ في تثبيت التبعيات
        pause
        exit /b 1
    )
)

echo بناء المشروع...
npm run build
if %errorlevel% neq 0 (
    echo خطأ في بناء المشروع
    pause
    exit /b 1
)

echo تشغيل تطبيق سطح المكتب...
echo.

npm run electron

pause
