import React, { useState, useEffect } from 'react';
import { Zone, IrrigationValve } from '../types';
import { formatDate } from '../utils/database';

interface FarmMapViewProps {
  zones: Zone[];
  onZoneUpdate: (zoneId: string, updatedZone: Zone) => void;
}

const FarmMapView: React.FC<FarmMapViewProps> = ({ zones, onZoneUpdate }) => {
  const [selectedZone, setSelectedZone] = useState<Zone | null>(null);
  const [showDetails, setShowDetails] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const zonesPerPage = 12;

  const handleValveToggle = (zoneId: string | number, valveId: string | number) => {
    const zone = zones.find(z => z.id.toString() === zoneId.toString());
    if (!zone || !zone.valves) return;

    const updatedZone = {
      ...zone,
      valves: zone.valves.map(valve => 
        valve.id.toString() === valveId.toString()
          ? { ...valve, status: valve.status === 'active' ? 'inactive' as 'inactive' : 'active' as 'active' }
          : valve
      )
    };

    onZoneUpdate(zone.id.toString(), updatedZone);
  };

  const handleZoneStatusToggle = (zoneId: string | number) => {
    const zone = zones.find(z => z.id.toString() === zoneId.toString());
    if (!zone) return;

    const updatedZone = {
      ...zone,
      status: zone.status === 'active' ? 'inactive' as Zone['status'] : 'active' as Zone['status']
    };

    onZoneUpdate(zone.id.toString(), updatedZone);
  };

  const getValveColor = (valve: IrrigationValve) => {
    if (!valve.status || valve.status !== 'active') return 'bg-gray-300';
    return valve.lettuceType === 'iceberg' ? 'bg-blue-500' : 'bg-orange-500';
  };

  const getZoneStatusColor = (status: Zone['status']) => {
    return status === 'active' ? 'bg-green-500' : 'bg-red-500';
  };

  const getZoneStatusText = (status: Zone['status']) => {
    return status === 'active' ? 'نشط' : 'غير نشط';
  };

  const getZoneCardGradient = (status: Zone['status']) => {
    switch (status) {
      case 'active':
        return 'from-emerald-400 via-green-300 to-emerald-600';
      case 'harvesting':
        return 'from-yellow-200 via-orange-200 to-yellow-400';
      case 'preparing':
        return 'from-blue-200 via-blue-100 to-emerald-200';
      case 'inactive':
      default:
        return 'from-gray-100 via-blue-50 to-gray-300';
    }
  };

  // حساب الصفحات
  const totalPages = Math.ceil(zones.length / zonesPerPage);
  const startIndex = (currentPage - 1) * zonesPerPage;
  const endIndex = startIndex + zonesPerPage;
  const currentZones = zones.slice(startIndex, endIndex);

  // إحصائيات سريعة
  const totalZones = zones.length;
  const activeZones = zones.filter(z => z.status === 'active').length;
  const totalValves = zones.reduce((sum, zone) => sum + (zone.valves?.length || 0), 0);
  const activeValves = zones.reduce((sum, zone) => 
    sum + (zone.valves?.filter(v => v.status === 'active').length || 0), 0
  );

  return (
    <>
      {/* زخارف خلفية */}
      <div className="absolute top-0 left-0 w-full h-full pointer-events-none z-0">
        <div className="absolute -top-24 -left-24 w-96 h-96 bg-gradient-to-br from-green-300 via-blue-200 to-indigo-200 opacity-40 rounded-full blur-2xl animate-pulse"></div>
        <div className="absolute bottom-0 right-0 w-[32rem] h-[32rem] bg-gradient-to-tr from-indigo-200 via-blue-100 to-green-200 opacity-30 rounded-full blur-2xl animate-pulse-slow"></div>
      </div>
      <div className="relative z-10 p-6 min-h-screen bg-gradient-to-br from-green-100 via-blue-50 to-indigo-100">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-4xl font-extrabold bg-gradient-to-r from-emerald-600 via-blue-600 to-indigo-600 bg-clip-text text-transparent mb-2 text-center drop-shadow-lg tracking-tight">خريطة المزرعة</h1>
          <p className="text-gray-600 text-center">إدارة 24 مرحلة زراعية ومحابس الري</p>
        </div>

        {/* Statistics Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <div className="bg-gradient-to-br from-green-100 via-blue-100 to-indigo-100/80 bg-opacity-80 rounded-2xl shadow-2xl border-0 p-6 hover:shadow-emerald-200/60 transition-all duration-200">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-gray-600 text-sm font-medium mb-2">إجمالي المراحل</h3>
                <p className="text-3xl font-bold bg-gradient-to-r from-emerald-600 to-blue-600 bg-clip-text text-transparent drop-shadow">{totalZones}</p>
              </div>
              <div className="w-12 h-12 bg-gradient-to-br from-emerald-100 to-green-100 rounded-xl flex items-center justify-center">
                <span className="text-emerald-600 text-xl">🌱</span>
              </div>
            </div>
          </div>
          <div className="bg-gradient-to-br from-green-100 via-blue-100 to-indigo-100/80 bg-opacity-80 rounded-2xl shadow-2xl border-0 p-6 hover:shadow-green-200/60 transition-all duration-200">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-gray-600 text-sm font-medium mb-2">المراحل النشطة</h3>
                <p className="text-3xl font-bold bg-gradient-to-r from-green-600 to-emerald-600 bg-clip-text text-transparent drop-shadow">{activeZones}</p>
              </div>
              <div className="w-12 h-12 bg-gradient-to-br from-green-100 to-emerald-100 rounded-xl flex items-center justify-center">
                <span className="text-green-600 text-xl">✅</span>
              </div>
            </div>
          </div>
          <div className="bg-gradient-to-br from-green-100 via-blue-100 to-indigo-100/80 bg-opacity-80 rounded-2xl shadow-2xl border-0 p-6 hover:shadow-blue-200/60 transition-all duration-200">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-gray-600 text-sm font-medium mb-2">إجمالي المحابس</h3>
                <p className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-emerald-600 bg-clip-text text-transparent drop-shadow">{totalValves}</p>
              </div>
              <div className="w-12 h-12 bg-gradient-to-br from-blue-100 to-emerald-100 rounded-xl flex items-center justify-center">
                <span className="text-blue-600 text-xl">💧</span>
              </div>
            </div>
          </div>
          <div className="bg-gradient-to-br from-green-100 via-blue-100 to-indigo-100/80 bg-opacity-80 rounded-2xl shadow-2xl border-0 p-6 hover:shadow-orange-200/60 transition-all duration-200">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-gray-600 text-sm font-medium mb-2">المحابس النشطة</h3>
                <p className="text-3xl font-bold bg-gradient-to-r from-orange-500 to-yellow-500 bg-clip-text text-transparent drop-shadow">{activeValves}</p>
              </div>
              <div className="w-12 h-12 bg-gradient-to-br from-orange-100 to-yellow-100 rounded-xl flex items-center justify-center">
                <span className="text-orange-600 text-xl">🔧</span>
              </div>
            </div>
          </div>
        </div>

        {/* Farm Map Grid */}
        <div className="bg-gradient-to-br from-white/80 via-blue-50/80 to-green-50/80 rounded-2xl shadow-2xl p-6">
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-2xl font-extrabold bg-gradient-to-br from-emerald-600 to-blue-600 bg-clip-text text-transparent drop-shadow">خريطة المراحل الزراعية</h2>
            <div className="text-sm text-gray-600">الصفحة {currentPage} من {totalPages}</div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {currentZones.map((zone) => (
              <div
                key={zone.id}
                className={`relative bg-gradient-to-br ${getZoneCardGradient(zone.status)} rounded-2xl shadow-2xl border-2 border-emerald-200/70 p-4 transition-all duration-300 hover:scale-105 hover:shadow-emerald-400/40 overflow-hidden animate-fade-in group`}
                onClick={() => {
                  setSelectedZone(zone);
                  setShowDetails(true);
                }}
              >
                {/* زخرفة دائرية شفافة داخل البطاقة */}
                <div className="absolute -top-8 -left-8 w-24 h-24 bg-emerald-200 opacity-20 rounded-full"></div>
                {/* Zone Status Indicator */}
                <div className="absolute top-2 right-2">
                  <div className={`w-3 h-3 rounded-full border-2 border-white shadow-sm ${getZoneStatusColor(zone.status)}`}></div>
                </div>
                {/* Zone Header */}
                <div className="text-center mb-4">
                  <h3 className="text-lg font-bold bg-gradient-to-br from-emerald-600 to-blue-600 bg-clip-text text-transparent mb-1 drop-shadow">{zone.name}</h3>
                  <p className="text-sm text-gray-600">{zone.lettuceType === 'iceberg' ? 'خس أيسبيرغ' : 'خس رومين'}</p>
                  <button
                    onClick={() => handleZoneStatusToggle(zone.id)}
                    className={`inline-block px-2 py-1 rounded-full text-xs font-bold mt-1 shadow-sm transition-all duration-200 hover:scale-105 active:scale-95 hover:shadow-md ${zone.status === 'active' ? 'bg-gradient-to-r from-green-200 to-green-400 text-green-900 hover:from-green-300 hover:to-green-500' : 'bg-gradient-to-r from-red-200 to-red-400 text-red-900 hover:from-red-300 hover:to-red-500'}`}
                    title={`حالة المرحلة: ${getZoneStatusText(zone.status)} - اضغط للتبديل`}
                  >
                    {getZoneStatusText(zone.status)}
                  </button>
                </div>
                {/* Valves Display */}
                <div className="mb-4">
                  <div className="flex justify-center space-x-1 space-x-reverse">
                    {zone.valves?.map((valve) => (
                      <button
                        key={valve.id}
                        onClick={() => handleValveToggle(zone.id, valve.id)}
                        className={`w-8 h-8 rounded-full border-2 border-white shadow-md transition-all duration-200 hover:scale-125 bg-gradient-to-br from-white/60 to-blue-100/60 ${getValveColor(valve)} hover:shadow-lg active:scale-95`}
                        title={`محبس ${valve.valveNumber} - ${valve.lettuceType === 'iceberg' ? 'أيسبيرغ' : 'رومين'} - ${valve.status === 'active' ? 'نشط' : 'غير نشط'} - اضغط للتبديل`}
                      />
                    )) || []}
                  </div>
                  <div className="text-center mt-2">
                    <span className="text-xs text-gray-600">{zone.valves?.filter(v => v.status === 'active').length || 0}/{zone.valves?.length || 0} نشط</span>
                  </div>
                </div>
                {/* Zone Info */}
                <div className="text-xs text-gray-600 space-y-1">
                  <div className="flex justify-between">
                    <span>تاريخ الزراعة:</span>
                    <span className="font-medium">{zone.plantingDate ? formatDate(zone.plantingDate) : <span className="text-gray-400">غير محدد</span>}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>تاريخ الحصاد:</span>
                    <span className="font-medium">{zone.harvestDate ? formatDate(zone.harvestDate) : <span className="text-gray-400">غير محدد</span>}</span>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* Pagination */}
          {totalPages > 1 && (
            <div className="flex justify-center items-center space-x-2 space-x-reverse mt-8">
              <button
                onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                disabled={currentPage === 1}
                className="px-4 py-2 bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                السابق
              </button>
              {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
                <button
                  key={page}
                  onClick={() => setCurrentPage(page)}
                  className={`px-3 py-2 rounded-lg ${currentPage === page ? 'bg-emerald-600 text-white' : 'bg-gray-200 text-gray-700 hover:bg-gray-300'}`}
                >
                  {page}
                </button>
              ))}
              <button
                onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                disabled={currentPage === totalPages}
                className="px-4 py-2 bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                التالي
              </button>
            </div>
          )}
        </div>
      </div>

      {/* Zone Details Modal */}
      {showDetails && selectedZone && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-2xl shadow-2xl max-w-2xl w-full max-h-[90vh] overflow-y-auto border-t-8 border-emerald-400">
            <div className="bg-gradient-to-r from-emerald-600 to-blue-600 text-white p-6 rounded-t-2xl">
              <div className="flex justify-between items-center">
                <h2 className="text-xl font-bold drop-shadow">{selectedZone.name}</h2>
                <button
                  onClick={() => setShowDetails(false)}
                  className="text-white hover:text-gray-200 bg-emerald-700 hover:bg-emerald-800 rounded-full p-2 transition-colors"
                >
                  ✕
                </button>
              </div>
            </div>
            <div className="p-6 space-y-6">
              {/* Zone Information */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <h3 className="text-lg font-semibold bg-gradient-to-br from-emerald-600 to-blue-600 bg-clip-text text-transparent mb-3 drop-shadow">معلومات المرحلة</h3>
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span className="text-gray-600">نوع المحصول:</span>
                      <span className="font-medium">{selectedZone.lettuceType === 'iceberg' ? 'خس أيسبيرغ' : 'خس رومين'}</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-gray-600">الحالة:</span>
                      <div className="flex items-center gap-2">
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${selectedZone.status === 'active' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>{getZoneStatusText(selectedZone.status)}</span>
                        <button
                          onClick={() => handleZoneStatusToggle(selectedZone.id)}
                          className={`px-3 py-1 rounded-lg text-xs font-medium transition-colors shadow-sm ${selectedZone.status === 'active' ? 'bg-red-500 text-white hover:bg-red-600' : 'bg-green-500 text-white hover:bg-green-600'}`}
                          title={`${selectedZone.status === 'active' ? 'إيقاف' : 'تفعيل'} المرحلة`}
                        >
                          {selectedZone.status === 'active' ? 'إيقاف' : 'تفعيل'}
                        </button>
                      </div>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">تاريخ الزراعة:</span>
                      <span className="font-medium">{selectedZone.plantingDate ? formatDate(selectedZone.plantingDate) : <span className="text-gray-400">غير محدد</span>}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">تاريخ الحصاد:</span>
                      <span className="font-medium">{selectedZone.harvestDate ? formatDate(selectedZone.harvestDate) : <span className="text-gray-400">غير محدد</span>}</span>
                    </div>
                  </div>
                </div>
                <div>
                  <h3 className="text-lg font-semibold bg-gradient-to-br from-emerald-600 to-blue-600 bg-clip-text text-transparent mb-3 drop-shadow">الملاحظات</h3>
                  <p className="text-gray-600 text-sm">{selectedZone.notes}</p>
                </div>
              </div>

              {/* Valves Control */}
              <div>
                <h3 className="text-lg font-semibold bg-gradient-to-br from-emerald-600 to-blue-600 bg-clip-text text-transparent mb-3 drop-shadow">محابس الري</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {selectedZone.valves?.length ? selectedZone.valves.map((valve) => (
                    <div
                      key={valve.id}
                      className={`p-4 rounded-xl border-2 transition-all duration-200 shadow-md ${valve.status === 'active' ? 'border-green-200 bg-green-50' : 'border-gray-200 bg-gray-50'} group hover:scale-105`}
                    >
                      <div className="flex items-center justify-between mb-2">
                        <span className="font-medium">محبس {valve.valveNumber}</span>
                        <div className={`w-4 h-4 rounded-full border-2 border-white shadow-sm ${getValveColor(valve)}`}></div>
                      </div>
                      <div className="text-sm text-gray-600 mb-3">النوع: {valve.lettuceType === 'iceberg' ? 'أيسبيرغ' : 'رومين'}</div>
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          handleValveToggle(selectedZone.id, valve.id);
                        }}
                        className={`w-full py-2 px-3 rounded-lg text-sm font-medium transition-colors shadow-md ${valve.status === 'active' ? 'bg-red-500 text-white hover:bg-red-600' : 'bg-green-500 text-white hover:bg-green-600'}`}
                      >
                        {valve.status === 'active' ? 'إيقاف' : 'تشغيل'}
                      </button>
                    </div>
                  )) : (
                    <div className="col-span-full text-center py-8 text-gray-500">
                      <div className="text-4xl mb-2">🚰</div>
                      <p>لا توجد محابس مُعرفة لهذه المرحلة</p>
                    </div>
                  )}
                </div>
              </div>

              {/* Zone Status Control */}
              <div className="border-t pt-4">
                <h3 className="text-lg font-semibold bg-gradient-to-br from-emerald-600 to-blue-600 bg-clip-text text-transparent mb-3 drop-shadow">حالة المرحلة</h3>
                <button
                  onClick={() => {
                    handleZoneStatusToggle(selectedZone.id);
                    setShowDetails(false);
                  }}
                  className={`w-full py-3 px-4 rounded-lg font-medium transition-colors shadow-md ${selectedZone.status === 'active' ? 'bg-red-500 text-white hover:bg-red-600' : 'bg-green-500 text-white hover:bg-green-600'}`}
                >
                  {selectedZone.status === 'active' ? 'إيقاف المرحلة' : 'تفعيل المرحلة'}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default FarmMapView; 