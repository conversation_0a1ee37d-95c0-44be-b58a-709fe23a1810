# نظام إدارة مزرعة الخس - شركة الشفق للزراعة الحديثة

## 🚀 نظرة عامة

نظام متكامل لإدارة مزرعة الخس مع 24 مرحلة زراعية، مبني باستخدام أحدث التقنيات لضمان الأداء العالي والتجربة المثلى للمستخدم.

## ✨ المميزات الرئيسية

### 🌱 إدارة المراحل الزراعية
- 24 مرحلة زراعية متكاملة
- تتبع حالة كل مرحلة (نشطة، جاهزة للحصاد، معطلة)
- إدارة محابس الري لكل مرحلة
- جدولة عمليات الري والتسميد

### 💧 نظام الري الذكي
- إدارة محابس الري (120 محبس)
- جدولة عمليات الري
- تتبع عمليات الري المكتملة والمعلقة
- تقارير مفصلة عن استهلاك المياه

### 🧪 إدارة العمليات الزراعية
- تسجيل عمليات الرش والتسميد
- تتبع المواد المستخدمة
- جدولة العمليات المستقبلية
- تقارير الأداء

### 📦 إدارة المخزون
- تتبع المواد والمعدات
- تنبيهات المخزون المنخفض
- إدارة الموردين
- تقارير المخزون

### 👥 إدارة الموارد البشرية
- إدارة الموظفين
- نظام السلف والرواتب
- تتبع الإجازات والحضور
- تقارير الأداء

### 💰 إدارة المبيعات
- تسجيل المبيعات المحلية
- تتبع العملاء
- تقارير الإيرادات
- إدارة المدفوعات

### 📊 لوحة تحكم متقدمة
- إحصائيات فورية
- رسوم بيانية تفاعلية
- تنبيهات ذكية
- تقارير شاملة

## 🛠️ التقنيات المستخدمة

### Frontend
- **React 18** - مكتبة واجهة المستخدم
- **TypeScript** - لغة البرمجة الأساسية
- **Tailwind CSS** - إطار عمل التصميم
- **Lucide React** - مكتبة الأيقونات
- **Vite** - أداة البناء والتطوير

### Backend & Storage
- **IndexedDB** - قاعدة البيانات المحلية
- **LocalStorage** - التخزين المؤقت
- **File System API** - النسخ الاحتياطي

### Additional Libraries
- **Moment.js** - إدارة التواريخ
- **HTML2Canvas** - تصدير التقارير
- **jsPDF** - إنشاء ملفات PDF
- **Arabic Reshaper** - دعم النصوص العربية

## 🚀 التثبيت والتشغيل

### المتطلبات الأساسية
- Node.js (الإصدار 18 أو أحدث)
- npm أو yarn

### خطوات التثبيت

1. **استنساخ المشروع**
```bash
git clone [repository-url]
cd project-name
```

2. **تثبيت التبعيات**
```bash
npm install
```

3. **تشغيل المشروع في وضع التطوير**
```bash
npm run dev
```

4. **فتح المتصفح**
```
http://localhost:5173
```

### أوامر التشغيل المتاحة

```bash
# تشغيل في وضع التطوير
npm run dev

# بناء المشروع للإنتاج
npm run build

# معاينة البناء
npm run preview

# فحص الكود
npm run lint

# تشغيل كتطبيق سطح المكتب (Electron)
npm run electron-dev

# بناء تطبيق سطح المكتب
npm run dist
```

## 📁 هيكل المشروع

```
src/
├── components/          # مكونات React
│   ├── DashboardView.tsx
│   ├── ZonesView.tsx
│   ├── SprayingView.tsx
│   ├── FertilizationView.tsx
│   ├── InventoryView.tsx
│   ├── EmployeesView.tsx
│   ├── SuppliersView.tsx
│   ├── AdvancesView.tsx
│   ├── SalesView.tsx
│   ├── ReportsView.tsx
│   └── ...
├── types/              # تعريفات TypeScript
│   └── index.ts
├── utils/              # أدوات مساعدة
│   ├── database.ts
│   ├── storage.ts
│   └── ...
├── fonts/              # الخطوط العربية
├── App.tsx            # المكون الرئيسي
├── main.tsx           # نقطة البداية
└── index.css          # الأنماط العامة
```

## 🎨 التصميم والواجهة

### المميزات التصميمية
- **تصميم متجاوب** - يعمل على جميع الأجهزة
- **دعم كامل للغة العربية** - من اليمين لليسار
- **خطوط عربية عالية الجودة** - Cairo و Tajawal
- **ألوان متناسقة** - نظام ألوان أخضر يليق بالزراعة
- **أيقونات واضحة** - Lucide React
- **حركات سلسة** - انتقالات CSS متقدمة

### نظام الألوان
- **الأخضر الأساسي**: #059669 (الزراعة والنمو)
- **الأزرق الثانوي**: #1e40af (المياه والري)
- **الأصفر**: #f59e0b (التحذيرات)
- **الأحمر**: #ef4444 (الأخطاء)

## 📊 قاعدة البيانات

### هيكل البيانات
- **المراحل الزراعية**: 24 مرحلة مع محابس الري
- **عمليات الري**: جدولة وتتبع
- **الرش والتسميد**: تسجيل العمليات
- **المخزون**: المواد والمعدات
- **الموظفين**: البيانات الشخصية والمالية
- **الموردين**: معلومات الاتصال والمواد
- **المبيعات**: تسجيل المعاملات

### النسخ الاحتياطي
- **تصدير البيانات**: ملف JSON شامل
- **استيراد البيانات**: استعادة من النسخ الاحتياطي
- **تشفير البيانات**: حماية المعلومات الحساسة

## 🔧 التخصيص والتطوير

### إضافة ميزات جديدة
1. إنشاء مكون React جديد في `src/components/`
2. إضافة الأنواع المطلوبة في `src/types/index.ts`
3. إضافة وظائف قاعدة البيانات في `src/utils/database.ts`
4. تحديث `App.tsx` لإضافة المكون الجديد

### تخصيص التصميم
- تعديل الألوان في `src/index.css`
- إضافة أنماط جديدة في `tailwind.config.js`
- تخصيص الخطوط في `src/fonts/`

## 📱 تطبيق سطح المكتب

### Electron
- **تطبيق مستقل** - لا يحتاج متصفح
- **أداء عالي** - وصول مباشر للملفات
- **واجهة محلية** - تجربة مستخدم محسنة
- **تحديثات تلقائية** - إشعارات التحديث

### بناء التطبيق
```bash
# بناء لنظام Windows
npm run dist-win

# بناء لنظام macOS
npm run dist-mac

# بناء لنظام Linux
npm run dist-linux
```

## 🚀 النشر والإنتاج

### بناء للإنتاج
```bash
npm run build
```

### محتويات البناء
- ملفات HTML و CSS و JavaScript محسنة
- الصور والخطوط مضغوطة
- تحسين الأداء والسرعة

### النشر على الخادم
- رفع محتويات مجلد `dist/`
- تكوين الخادم لخدمة الملفات الثابتة
- إعداد HTTPS للأمان

## 🔒 الأمان والخصوصية

### حماية البيانات
- **تشفير البيانات الحساسة**
- **نسخ احتياطية مشفرة**
- **صلاحيات محدودة للوصول**
- **تسجيل العمليات**

### أفضل الممارسات
- **تحديث التبعيات** بانتظام
- **فحص الثغرات الأمنية**
- **نسخ احتياطية دورية**
- **مراقبة الأداء**

## 📞 الدعم والمساعدة

### المشاكل الشائعة
1. **مشاكل في التثبيت**: تأكد من إصدار Node.js
2. **مشاكل في الخطوط**: تحقق من تحميل الخطوط العربية
3. **مشاكل في قاعدة البيانات**: امسح ذاكرة التخزين المؤقت

### طلب المساعدة
- **إنشاء Issue** في GitHub
- **مراجعة التوثيق** المحدث
- **فحص سجل الأخطاء** في وحدة التحكم

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT. راجع ملف `LICENSE` للتفاصيل.

## 🤝 المساهمة

نرحب بالمساهمات! يرجى:
1. Fork المشروع
2. إنشاء فرع للميزة الجديدة
3. إجراء التغييرات
4. إنشاء Pull Request

---

**تم تطوير هذا النظام بواسطة فريق شركة الشفق للزراعة الحديثة** 🌱 