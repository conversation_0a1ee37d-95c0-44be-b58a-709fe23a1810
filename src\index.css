@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@200;300;400;500;600;700;800;900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Tajawal:wght@200;300;400;500;700;800;900&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --primary-color: #059669;
  --primary-dark: #047857;
  --primary-light: #10b981;
  --secondary-color: #1e40af;
  --accent-color: #f59e0b;
  --success-color: #10b981;
  --warning-color: #f59e0b;
  --error-color: #ef4444;
  --background-color: #f8fafc;
  --surface-color: #ffffff;
  --text-primary: #1e293b;
  --text-secondary: #64748b;
  --border-color: #e2e8f0;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Cairo', 'Tajawal', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  background-color: var(--background-color);
  color: var(--text-primary);
  direction: rtl;
  text-align: right;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideIn {
  from {
    transform: translateX(100%);
  }
  to {
    transform: translateX(0);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.fade-in {
  animation: fadeIn 0.3s ease-out;
}

.slide-in {
  animation: slideIn 0.3s ease-out;
}

.pulse {
  animation: pulse 2s infinite;
}

/* Custom Components */
.btn-primary {
  @apply bg-emerald-600 hover:bg-emerald-700 text-white font-medium py-2 px-4 rounded-lg transition-all duration-200 shadow-sm hover:shadow-md;
}

.btn-secondary {
  @apply bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg transition-all duration-200 shadow-sm hover:shadow-md;
}

.btn-success {
  @apply bg-green-600 hover:bg-green-700 text-white font-medium py-2 px-4 rounded-lg transition-all duration-200 shadow-sm hover:shadow-md;
}

.btn-warning {
  @apply bg-yellow-500 hover:bg-yellow-600 text-white font-medium py-2 px-4 rounded-lg transition-all duration-200 shadow-sm hover:shadow-md;
}

.btn-danger {
  @apply bg-red-600 hover:bg-red-700 text-white font-medium py-2 px-4 rounded-lg transition-all duration-200 shadow-sm hover:shadow-md;
}

.btn-outline {
  @apply border-2 border-gray-300 hover:border-gray-400 text-gray-700 hover:text-gray-900 font-medium py-2 px-4 rounded-lg transition-all duration-200;
}

.card {
  @apply bg-white rounded-xl shadow-sm border border-gray-200 p-6;
}

.card-hover {
  @apply hover:shadow-md transition-shadow duration-200;
}

.input-field {
  @apply w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 transition-all duration-200;
}

.modal-overlay {
  @apply fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4;
}

.modal-content {
  @apply bg-white rounded-2xl shadow-2xl p-6 w-full max-w-lg mx-4 max-h-[90vh] overflow-y-auto transform transition-all duration-300;
}

/* Status Colors */
.status-active {
  @apply bg-green-100 text-green-800 px-2 py-1 rounded-full text-sm font-medium;
}

.status-inactive {
  @apply bg-gray-100 text-gray-800 px-2 py-1 rounded-full text-sm font-medium;
}

.status-warning {
  @apply bg-yellow-100 text-yellow-800 px-2 py-1 rounded-full text-sm font-medium;
}

.status-error {
  @apply bg-red-100 text-red-800 px-2 py-1 rounded-full text-sm font-medium;
}

/* Table Styles */
.table-container {
  @apply overflow-x-auto shadow-sm rounded-lg border border-gray-200;
}

.table {
  @apply min-w-full divide-y divide-gray-200;
}

.table-header {
  @apply bg-gray-50;
}

.table-header-cell {
  @apply px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider;
}

.table-body {
  @apply bg-white divide-y divide-gray-200;
}

.table-row {
  @apply hover:bg-gray-50 transition-colors duration-150;
}

.table-cell {
  @apply px-6 py-4 whitespace-nowrap text-sm text-gray-900;
}

/* Navigation */
.nav-item {
  @apply flex items-center px-4 py-3 text-gray-700 hover:bg-emerald-50 hover:text-emerald-700 rounded-lg transition-all duration-200 cursor-pointer;
}

.nav-item.active {
  @apply bg-emerald-100 text-emerald-700 font-medium;
}

.nav-icon {
  @apply w-5 h-5 ml-3;
}

/* Dashboard Cards */
.dashboard-card {
  @apply bg-white rounded-xl shadow-sm border border-gray-200 p-6 hover:shadow-md transition-all duration-200;
}

.dashboard-stat {
  @apply text-3xl font-bold text-gray-900;
}

.dashboard-label {
  @apply text-sm font-medium text-gray-500 mt-1;
}

/* Zone Cards */
.zone-card {
  @apply bg-white rounded-xl shadow-sm border border-gray-200 p-4 hover:shadow-md transition-all duration-200 cursor-pointer;
}

.zone-header {
  @apply flex items-center justify-between mb-3;
}

.zone-title {
  @apply text-lg font-semibold text-gray-900;
}

.zone-status {
  @apply text-sm font-medium;
}

.zone-details {
  @apply space-y-2 text-sm text-gray-600;
}

/* Responsive Design */
@media (max-width: 768px) {
  .modal-content {
    @apply max-w-sm mx-2;
  }
  
  .card {
    @apply p-4;
  }
  
  .table-container {
    @apply text-xs;
  }
  
  .table-cell {
    @apply px-3 py-2;
  }
}
