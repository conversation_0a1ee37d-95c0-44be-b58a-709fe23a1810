# نظام التصميم - Lettuce Farm Management System

## نظرة عامة

هذا النظام يحتوي على جميع عناصر التصميم المستخدمة في مشروع نظام إدارة مزرعة الخس. تم استخراج جميع التنسيقات كما هي بالضبط دون أي تغيير أو تعديل، لضمان الهوية البصرية الموحدة في جميع المشاريع المستقبلية.

## الملفات المدرجة

### 1. `theme.css` - ملف CSS المركزي
يحتوي على جميع الأنماط CSS المستخدمة في المشروع:
- متغيرات CSS الأساسية
- أنماط الأزرار
- أنماط البطاقات
- أنماط حقول الإدخال
- أنماط النوافذ المنبثقة
- أنماط الجداول
- أنماط التنقل
- المؤثرات الحركية والانتقالات

### 2. `tailwind.config.js` - إعدادات Tailwind CSS
يحتوي على جميع التخصيصات المستخدمة في Tailwind:
- الخطوط العربية
- لوحة الألوان المخصصة
- الظلال المخصصة
- الحواف المخصصة
- المسافات المخصصة
- المؤثرات الحركية

### 3. `design-system.js` - نظام التصميم في JavaScript
يحتوي على جميع عناصر التصميم ككائنات JavaScript:
- لوحة الألوان
- الخطوط
- المسافات
- الظلال
- أنماط المكونات
- دوال مساعدة

## لوحة الألوان

### الألوان الأساسية
```css
--primary-color: #059669    /* الأخضر الأساسي */
--primary-dark: #047857     /* الأخضر الداكن */
--primary-light: #10b981    /* الأخضر الفاتح */
--secondary-color: #1e40af  /* الأزرق الثانوي */
--accent-color: #f59e0b     /* البرتقالي المميز */
```

### ألوان الحالة
```css
--success-color: #10b981    /* الأخضر للنجاح */
--warning-color: #f59e0b    /* البرتقالي للتحذير */
--error-color: #ef4444      /* الأحمر للخطأ */
```

### ألوان الخلفية والنصوص
```css
--background-color: #f8fafc /* خلفية الصفحة */
--surface-color: #ffffff    /* خلفية العناصر */
--text-primary: #1e293b     /* النص الأساسي */
--text-secondary: #64748b   /* النص الثانوي */
--border-color: #e2e8f0     /* لون الحدود */
```

## الخطوط

### الخطوط العربية المستخدمة
```css
font-family: 'Cairo', 'Tajawal', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
```

### أوزان الخطوط
- `200` - extralight
- `300` - light
- `400` - normal
- `500` - medium
- `600` - semibold
- `700` - bold
- `800` - extrabold
- `900` - black

## أنماط الأزرار

### الأزرار الأساسية
```css
.btn-primary {
  background-color: #059669;
  color: white;
  font-weight: 500;
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  transition: all 0.2s;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
}

.btn-secondary {
  background-color: #2563eb;
  /* نفس الخصائص مع اللون الأزرق */
}

.btn-success {
  background-color: #16a34a;
  /* نفس الخصائص مع اللون الأخضر */
}

.btn-warning {
  background-color: #eab308;
  /* نفس الخصائص مع اللون البرتقالي */
}

.btn-danger {
  background-color: #dc2626;
  /* نفس الخصائص مع اللون الأحمر */
}
```

### الأزرار المحددة
```css
.btn-outline {
  border: 2px solid #d1d5db;
  color: #374151;
  background-color: transparent;
}
```

## أنماط البطاقات

### البطاقات الأساسية
```css
.card {
  background-color: white;
  border-radius: 0.75rem;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  border: 1px solid #e5e7eb;
  padding: 1.5rem;
}

.card-hover {
  transition: box-shadow 0.2s;
}

.card-hover:hover {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}
```

### بطاقات لوحة التحكم
```css
.dashboard-card {
  background-color: white;
  border-radius: 0.75rem;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  border: 1px solid #e5e7eb;
  padding: 1.5rem;
  transition: all 0.2s;
}
```

## أنماط حقول الإدخال

```css
.input-field {
  width: 100%;
  padding: 0.5rem 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 0.5rem;
  transition: all 0.2s;
  font-size: 0.875rem;
  line-height: 1.25rem;
}

.input-field:focus {
  outline: none;
  border-color: #059669;
  box-shadow: 0 0 0 3px rgba(5, 150, 105, 0.1);
}
```

## أنماط النوافذ المنبثقة

```css
.modal-overlay {
  position: fixed;
  inset: 0;
  background-color: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(4px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 50;
  padding: 1rem;
}

.modal-content {
  background-color: white;
  border-radius: 1rem;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  padding: 1.5rem;
  width: 100%;
  max-width: 32rem;
  margin: 0 1rem;
  max-height: 90vh;
  overflow-y: auto;
  transform: translateZ(0);
  transition: all 0.3s;
}
```

## أنماط الجداول

```css
.table-container {
  overflow-x: auto;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  border-radius: 0.5rem;
  border: 1px solid #e5e7eb;
}

.table-header {
  background-color: #f9fafb;
}

.table-header-cell {
  padding: 0.75rem 1.5rem;
  text-align: right;
  font-size: 0.75rem;
  font-weight: 500;
  color: #6b7280;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.table-row {
  transition: background-color 0.15s;
}

.table-row:hover {
  background-color: #f9fafb;
}
```

## أنماط التنقل

```css
.nav-item {
  display: flex;
  align-items: center;
  padding: 0.75rem 1rem;
  color: #374151;
  border-radius: 0.5rem;
  transition: all 0.2s;
  cursor: pointer;
}

.nav-item:hover {
  background-color: #ecfdf5;
  color: #059669;
}

.nav-item.active {
  background-color: #d1fae5;
  color: #059669;
  font-weight: 500;
}
```

## أنماط حالات الحالة

```css
.status-active {
  background-color: #dcfce7;
  color: #166534;
  padding: 0.25rem 0.5rem;
  border-radius: 9999px;
  font-size: 0.875rem;
  font-weight: 500;
}

.status-inactive {
  background-color: #f1f5f9;
  color: #1e293b;
  padding: 0.25rem 0.5rem;
  border-radius: 9999px;
  font-size: 0.875rem;
  font-weight: 500;
}

.status-warning {
  background-color: #fef3c7;
  color: #92400e;
  padding: 0.25rem 0.5rem;
  border-radius: 9999px;
  font-size: 0.875rem;
  font-weight: 500;
}

.status-error {
  background-color: #fee2e2;
  color: #991b1b;
  padding: 0.25rem 0.5rem;
  border-radius: 9999px;
  font-size: 0.875rem;
  font-weight: 500;
}
```

## المؤثرات الحركية والانتقالات

### المؤثرات الحركية
```css
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideIn {
  from {
    transform: translateX(100%);
  }
  to {
    transform: translateX(0);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.fade-in {
  animation: fadeIn 0.3s ease-out;
}

.slide-in {
  animation: slideIn 0.3s ease-out;
}

.pulse {
  animation: pulse 2s infinite;
}
```

### الانتقالات
```css
transition: all 0.2s;
transition: box-shadow 0.2s;
transition: background-color 0.15s;
transition: all 0.3s;
```

## الأيقونات

### المصدر
- **المكتبة**: Lucide React
- **الرابط**: https://lucide.dev/

### الأيقونات المستخدمة
```javascript
[
  'BarChart3', 'Map', 'Leaf', 'Droplets', 'FlaskConical', 'Package', 
  'Users', 'Truck', 'DollarSign', 'ShoppingCart', 'ClipboardList', 
  'Settings', 'TrendingUp', 'Home', 'Calendar', 'FileText', 'Printer',
  'Download', 'Upload', 'Search', 'Filter', 'Eye', 'Edit', 'Plus',
  'Minus', 'Trash2', 'Save', 'X', 'CheckCircle', 'AlertTriangle',
  'Clock', 'Activity', 'Target', 'Zap', 'TreePine', 'Sprout'
]
```

### أحجام الأيقونات
```css
.icon-xs: 0.75rem
.icon-sm: 1rem
.icon-md: 1.25rem
.icon-lg: 1.5rem
.icon-xl: 2rem
.icon-2xl: 2.5rem
```

## الخلفيات المتدرجة

```css
.bg-gradient-primary {
  background: linear-gradient(135deg, #059669 0%, #10b981 100%);
}

.bg-gradient-secondary {
  background: linear-gradient(135deg, #2563eb 0%, #3b82f6 100%);
}

.bg-gradient-success {
  background: linear-gradient(135deg, #16a34a 0%, #22c55e 100%);
}

.bg-gradient-warning {
  background: linear-gradient(135deg, #eab308 0%, #f59e0b 100%);
}

.bg-gradient-danger {
  background: linear-gradient(135deg, #dc2626 0%, #ef4444 100%);
}
```

## الظلال

```css
.shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05)
.shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1)
.shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1)
.shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1)
.shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25)
```

## الحواف والزوايا

```css
.rounded-sm: 0.125rem
.rounded: 0.25rem
.rounded-md: 0.375rem
.rounded-lg: 0.5rem
.rounded-xl: 0.75rem
.rounded-2xl: 1rem
.rounded-3xl: 1.5rem
.rounded-full: 9999px
```

## التصميم المتجاوب

### نقاط التوقف
```css
xs: 475px
sm: 640px
md: 768px
lg: 1024px
xl: 1280px
2xl: 1536px
```

### التكيف للشاشات الصغيرة
```css
@media (max-width: 768px) {
  .modal-content {
    max-width: 24rem;
    margin: 0 0.5rem;
  }
  
  .card {
    padding: 1rem;
  }
  
  .table-container {
    font-size: 0.75rem;
  }
  
  .table-cell {
    padding: 0.75rem;
  }
}
```

## كيفية الاستخدام

### 1. استخدام ملف CSS
```html
<link rel="stylesheet" href="design-system/theme.css">
```

### 2. استخدام Tailwind CSS
```javascript
// استبدل ملف tailwind.config.js الحالي
import tailwindConfig from './design-system/tailwind.config.js';
```

### 3. استخدام JavaScript
```javascript
import { designSystem, colors, fonts } from './design-system/design-system.js';

// استخدام الألوان
const primaryColor = colors.primary[600];

// استخدام الخطوط
const fontFamily = fonts.family.arabic;

// استخدام نظام التصميم الكامل
const buttonStyle = designSystem.buttonStyles.primary;
```

### 4. استخدام في React
```jsx
import { buttonStyles, cardStyles } from './design-system/design-system.js';

function MyButton({ children, variant = 'primary' }) {
  const style = {
    ...buttonStyles.base,
    ...buttonStyles[variant]
  };
  
  return (
    <button style={style}>
      {children}
    </button>
  );
}

function MyCard({ children }) {
  const style = {
    ...cardStyles.base,
    ...cardStyles.hover
  };
  
  return (
    <div style={style}>
      {children}
    </div>
  );
}
```

## ملاحظات مهمة

1. **عدم التعديل**: جميع التنسيقات تم استخراجها كما هي بالضبط دون أي تغيير
2. **الهوية البصرية**: هذا النظام يضمن الهوية البصرية الموحدة في جميع المشاريع
3. **التوافق**: جميع التنسيقات متوافقة مع React و Tailwind CSS
4. **التخصيص**: يمكن إضافة تنسيقات جديدة دون تعديل التنسيقات الأساسية
5. **الأداء**: التنسيقات محسنة للأداء والسرعة

## الدعم

للاستفسارات أو المساعدة في استخدام نظام التصميم، يرجى الرجوع إلى:
- ملف `theme.css` للأنماط CSS
- ملف `tailwind.config.js` لإعدادات Tailwind
- ملف `design-system.js` للاستخدام في JavaScript/React 