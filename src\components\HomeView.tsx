import React from 'react';
import { 
  Home, 
  Snowflake, 
  Truck, 
  Warehouse, 
  DollarSign, 
  CreditCard, 
  Car, 
  Building2, 
  Leaf,
  BarChart3,
  Package,
  Users,
  Calendar,
  TrendingUp,
  Settings
} from 'lucide-react';

interface HomeViewProps {
  setCurrentView: (view: string) => void;
  navigationItems: any[];
  currentView: string;
  inventory: any[];
}

const HomeView: React.FC<HomeViewProps> = ({
  setCurrentView,
  navigationItems,
  currentView,
  inventory
}) => {
  // حساب إحصائيات المخزون
  const totalItems = inventory.length;
  const totalOrderBoxes = inventory.reduce((sum, item) => sum + item.totalBoxes, 0);
  const totalOrders = Math.floor(totalOrderBoxes / 70); // إجمالي الطبليات
  const totalBoxes = totalOrderBoxes % 70; // إجمالي الصناديق المتبقية

  // تاريخ اليوم بالميلادي
  const today = new Date();
  const weekdays = ['الأحد', 'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت'];
  const months = ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو', 'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'];
  const formattedDate = `${weekdays[today.getDay()]}، ${today.getDate()} ${months[today.getMonth()]} ${today.getFullYear()}`;

  return (
    <div className="min-h-screen w-full bg-gradient-to-br from-emerald-50 via-teal-50 to-blue-50 py-8 px-2 md:px-8">
      {/* Header */}
      <div className="bg-gradient-to-r from-blue-600 via-blue-500 to-indigo-500 text-white p-6 shadow-xl rounded-2xl mb-4">
        <div className="container mx-auto">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <div className="w-12 h-12 bg-white/20 rounded-2xl flex items-center justify-center backdrop-blur-sm">
                <Leaf size={24} className="text-white" />
              </div>
              <div>
                <h1 className="text-2xl font-bold font-arabic tracking-wide drop-shadow">شركة الشفق للزراعة الحديثة</h1>
                <p className="text-blue-100 text-sm font-medium tracking-wider">Al-Shafaq Modern Agriculture Company</p>
              </div>
            </div>
            <div className="text-sm bg-white/20 px-4 py-2 rounded-xl backdrop-blur-sm">
              {formattedDate}
            </div>
          </div>
        </div>
      </div>

      {/* Navigation */}
      <div className="bg-gradient-to-r from-blue-500 to-indigo-500 p-4 shadow-lg rounded-2xl mb-6">
        <div className="container mx-auto">
          <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 xl:grid-cols-9 gap-3">
            {navigationItems.map((item) => {
              const IconComponent = item.icon;
              return (
                <button
                  key={item.id}
                  onClick={() => setCurrentView(item.id)}
                  className={`p-4 rounded-2xl transition-all duration-300 transform hover:scale-105 hover:shadow-lg font-arabic font-bold border-2
                    ${currentView === item.id
                      ? 'bg-white text-blue-700 shadow-xl border-blue-500'
                      : 'bg-white text-blue-700 hover:bg-blue-50 shadow-md border-transparent'}
                  `}
                >
                  <IconComponent size={24} className="mx-auto mb-2" />
                  <span className="text-sm block">{item.name}</span>
                </button>
              );
            })}
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="container mx-auto p-0 md:p-6">
        {/* Welcome Section */}
        <div className="bg-white rounded-2xl shadow-xl border border-emerald-100 p-8 mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-3xl font-bold font-arabic text-emerald-700 mb-2 drop-shadow">مرحباً بك في نظام إدارة المخزون</h2>
              <p className="text-gray-600">يمكنك إدارة المخزون والمبيعات والسائقين والشحنات من خلال هذا النظام</p>
            </div>
            <div className="w-20 h-20 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-2xl flex items-center justify-center shadow-lg">
              <Home size={40} className="text-white" />
            </div>
          </div>
        </div>

        {/* Quick Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <div className="bg-white rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 border border-emerald-100">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-emerald-700 text-sm font-bold mb-2 font-arabic tracking-wide">إجمالي الأصناف</h3>
                <p className="text-3xl font-bold text-emerald-600 font-arabic">{totalItems}</p>
              </div>
              <div className="w-12 h-12 bg-gradient-to-br from-emerald-500 to-emerald-600 rounded-2xl flex items-center justify-center">
                <Package size={24} className="text-white" />
              </div>
            </div>
          </div>

          <div className="bg-white rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 border border-blue-100">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-blue-700 text-sm font-bold mb-2 font-arabic tracking-wide">إجمالي الصناديق</h3>
                <p className="text-3xl font-bold text-blue-600 font-arabic">{totalOrderBoxes}</p>
              </div>
              <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-2xl flex items-center justify-center">
                <Package size={24} className="text-white" />
              </div>
            </div>
          </div>

          <div className="bg-white rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 border border-purple-100">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-purple-700 text-sm font-bold mb-2 font-arabic tracking-wide">إجمالي الطبليات</h3>
                <p className="text-3xl font-bold text-purple-600 font-arabic">{totalOrders} <span className="text-sm font-normal text-gray-500">({totalBoxes} صندوق)</span></p>
              </div>
              <div className="w-12 h-12 bg-gradient-to-br from-purple-500 to-purple-600 rounded-2xl flex items-center justify-center">
                <Truck size={24} className="text-white" />
              </div>
            </div>
          </div>

          <div className="bg-white rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 border border-green-100">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-green-700 text-sm font-bold mb-2 font-arabic tracking-wide">الإحصائيات</h3>
                <p className="text-3xl font-bold text-green-600 font-arabic">
                  <BarChart3 size={28} className="inline-block mr-1" />
                </p>
              </div>
              <div className="w-12 h-12 bg-gradient-to-br from-green-500 to-green-600 rounded-2xl flex items-center justify-center">
                <TrendingUp size={24} className="text-white" />
              </div>
            </div>
          </div>
        </div>

        {/* Quick Access Sections */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
          {/* Farm Management */}
          <div className="bg-white rounded-2xl shadow-lg border border-green-100 overflow-hidden transition-transform duration-300 hover:scale-[1.02] hover:shadow-green-200/60">
            <div className="p-6 bg-gradient-to-r from-green-500 to-emerald-500 text-white">
              <div className="flex items-center gap-3">
                <Leaf size={24} />
                <h3 className="text-xl font-bold font-arabic drop-shadow">إدارة المزرعة</h3>
              </div>
            </div>
            <div className="p-6">
              <div className="grid grid-cols-1 gap-4">
                <button 
                  onClick={() => setCurrentView('farm')}
                  className="bg-green-50 hover:bg-green-100 p-4 rounded-xl transition-all duration-300 flex flex-col items-center gap-2 font-arabic font-bold text-green-700"
                >
                  <div className="w-12 h-12 bg-gradient-to-br from-green-500 to-emerald-600 rounded-xl flex items-center justify-center">
                    <Leaf size={24} className="text-white" />
                  </div>
                  <span className="font-medium text-gray-700">المزرعة</span>
                  <span className="text-xs text-gray-500">24 مرحلة × 5 محابس</span>
                </button>
                <div className="grid grid-cols-2 gap-2 text-center">
                  <div className="bg-gray-50 p-3 rounded-lg">
                    <p className="text-sm text-gray-600">أيسبيرغ</p>
                    <p className="text-lg font-bold text-blue-600">96</p>
                  </div>
                  <div className="bg-gray-50 p-3 rounded-lg">
                    <p className="text-sm text-gray-600">رومين</p>
                    <p className="text-lg font-bold text-orange-600">24</p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Inventory Management */}
          <div className="bg-white rounded-2xl shadow-lg border border-purple-100 overflow-hidden transition-transform duration-300 hover:scale-[1.02] hover:shadow-purple-200/60">
            <div className="p-6 bg-gradient-to-r from-emerald-500 to-teal-500 text-white">
              <div className="flex items-center gap-3">
                <Snowflake size={24} />
                <h3 className="text-xl font-bold font-arabic drop-shadow">إدارة المخزون</h3>
              </div>
            </div>
            <div className="p-6">
              <div className="grid grid-cols-2 gap-4">
                <button 
                  onClick={() => setCurrentView('warehouse')}
                  className="bg-purple-50 hover:bg-purple-100 p-4 rounded-xl transition-all duration-300 flex flex-col items-center gap-2 font-arabic font-bold text-purple-700"
                >
                  <div className="w-12 h-12 bg-gradient-to-br from-purple-500 to-indigo-600 rounded-xl flex items-center justify-center">
                    <Warehouse size={24} className="text-white" />
                  </div>
                  <span className="font-medium text-gray-700">المستودع</span>
                </button>
                <button 
                  onClick={() => setCurrentView('shipping')}
                  className="bg-indigo-50 hover:bg-indigo-100 p-4 rounded-xl transition-all duration-300 flex flex-col items-center gap-2 font-arabic font-bold text-indigo-700"
                >
                  <div className="w-12 h-12 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-xl flex items-center justify-center">
                    <Building2 size={24} className="text-white" />
                  </div>
                  <span className="font-medium text-gray-700">مكتب الشحن</span>
                </button>
              </div>
            </div>
          </div>

          {/* Financial Management */}
          <div className="bg-white rounded-2xl shadow-lg border border-orange-100 overflow-hidden transition-transform duration-300 hover:scale-[1.02] hover:shadow-orange-200/60">
            <div className="p-6 bg-gradient-to-r from-green-500 to-emerald-500 text-white">
              <div className="flex items-center gap-3">
                <DollarSign size={24} />
                <h3 className="text-xl font-bold font-arabic drop-shadow">الإدارة المالية والموظفين</h3>
              </div>
            </div>
            <div className="p-6">
              <div className="grid grid-cols-2 gap-4">
                <button 
                  onClick={() => setCurrentView('cash')}
                  className="bg-green-50 hover:bg-green-100 p-4 rounded-xl transition-all duration-300 flex flex-col items-center gap-2 font-arabic font-bold text-green-700"
                >
                  <div className="w-12 h-12 bg-gradient-to-br from-green-500 to-emerald-600 rounded-xl flex items-center justify-center">
                    <DollarSign size={24} className="text-white" />
                  </div>
                  <span className="font-medium text-gray-700">الكاش</span>
                </button>
                <button 
                  onClick={() => setCurrentView('advances')}
                  className="bg-orange-50 hover:bg-orange-100 p-4 rounded-xl transition-all duration-300 flex flex-col items-center gap-2 font-arabic font-bold text-orange-700"
                >
                  <div className="w-12 h-12 bg-gradient-to-br from-orange-500 to-amber-600 rounded-xl flex items-center justify-center">
                    <CreditCard size={24} className="text-white" />
                  </div>
                  <span className="font-medium text-gray-700">السلف</span>
                </button>
                <button 
                  onClick={() => setCurrentView('drivers')}
                  className="bg-red-50 hover:bg-red-100 p-4 rounded-xl transition-all duration-300 flex flex-col items-center gap-2 font-arabic font-bold text-red-700"
                >
                  <div className="w-12 h-12 bg-gradient-to-br from-red-500 to-rose-600 rounded-xl flex items-center justify-center">
                    <Car size={24} className="text-white" />
                  </div>
                  <span className="font-medium text-gray-700">السائقين</span>
                </button>
                <button 
                  className="bg-gray-50 hover:bg-gray-100 p-4 rounded-xl transition-all duration-300 flex flex-col items-center gap-2 font-arabic font-bold text-gray-700"
                >
                  <div className="w-12 h-12 bg-gradient-to-br from-gray-500 to-gray-600 rounded-xl flex items-center justify-center">
                    <Settings size={24} className="text-white" />
                  </div>
                  <span className="font-medium text-gray-700">الإعدادات</span>
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Recent Activity */}
        <div className="bg-white rounded-2xl shadow-xl border border-blue-100 overflow-hidden">
          <div className="p-6 bg-gradient-to-r from-blue-500 to-indigo-500 text-white">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <Calendar size={24} />
                <h3 className="text-xl font-bold font-arabic drop-shadow">النشاط الأخير</h3>
              </div>
            </div>
          </div>
          <div className="p-6">
            <div className="space-y-4">
              <div className="flex items-center p-4 bg-blue-50 rounded-xl">
                <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center mr-4">
                  <Package size={20} className="text-blue-600" />
                </div>
                <div>
                  <p className="text-gray-700">تم إضافة <span className="font-medium">50 صندوق</span> من صنف <span className="font-medium">طماطم</span> إلى الثلاجة</p>
                  <p className="text-sm text-gray-500">اليوم، 10:30 صباحاً</p>
                </div>
              </div>
              <div className="flex items-center p-4 bg-red-50 rounded-xl">
                <div className="w-10 h-10 bg-red-100 rounded-full flex items-center justify-center mr-4">
                  <Truck size={20} className="text-red-600" />
                </div>
                <div>
                  <p className="text-gray-700">تم إخراج <span className="font-medium">30 صندوق</span> من صنف <span className="font-medium">خيار</span> من البراد</p>
                  <p className="text-sm text-gray-500">اليوم، 09:15 صباحاً</p>
                </div>
              </div>
              <div className="flex items-center p-4 bg-green-50 rounded-xl">
                <div className="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center mr-4">
                  <DollarSign size={20} className="text-green-600" />
                </div>
                <div>
                  <p className="text-gray-700">تم تسجيل دخل بقيمة <span className="font-medium">5000 دينار أردني</span> من مبيعات الخضروات</p>
                  <p className="text-sm text-gray-500">أمس، 03:45 مساءً</p>
                </div>
              </div>
              <div className="flex items-center p-4 bg-purple-50 rounded-xl">
                <div className="w-10 h-10 bg-purple-100 rounded-full flex items-center justify-center mr-4">
                  <Building2 size={20} className="text-purple-600" />
                </div>
                <div>
                  <p className="text-gray-700">تم إضافة شحنة جديدة برقم <span className="font-medium">SHP-005</span> إلى الرياض</p>
                  <p className="text-sm text-gray-500">أمس، 02:20 مساءً</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default HomeView;