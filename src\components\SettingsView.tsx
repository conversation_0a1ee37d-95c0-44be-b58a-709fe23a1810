import React, { useState } from 'react';
import {
  Settings,
  Save,
  Leaf,
  User,
  Building,
  Phone,
  Mail,
  Globe,
  Database,
  Shield,
  Bell,
  Printer,
  Monitor,
  HardDrive,
  RefreshCw,
  Download,
  Upload,
  Info,
  FileDown,
  FileUp,
  Copy,
  AlertTriangle
} from 'lucide-react';

interface SettingsViewProps {
  setCurrentView: (view: string) => void;
  navigationItems: any[];
  currentView: string;
}

const SettingsView: React.FC<SettingsViewProps> = ({
  setCurrentView,
  navigationItems,
  currentView
}) => {
  const [activeTab, setActiveTab] = useState<string>('general');
  
  // بيانات تجريبية للإعدادات
  const [companyName, setCompanyName] = useState<string>('شركة الشفق للزراعة الحديثة');
  const [companyNameEn, setCompanyNameEn] = useState<string>('Al-Shafaq Modern Agriculture Company');
  const [managerName, setManagerName] = useState<string>('شادي أبوجيش');
  const [phone, setPhone] = useState<string>('00962793606169');
  const [email, setEmail] = useState<string>('<EMAIL>');
  const [address, setAddress] = useState<string>('الرياض - طريق الملك عبدالله');
  const [website, setWebsite] = useState<string>('www.alshafaq.com');
  const [taxNumber, setTaxNumber] = useState<string>('*********');
  const [commercialRegister, setCommercialRegister] = useState<string>('*********');
  
  // إعدادات النظام
  const [autoBackup, setAutoBackup] = useState<boolean>(true);
  const [backupFrequency, setBackupFrequency] = useState<string>('daily');
  const [notificationsEnabled, setNotificationsEnabled] = useState<boolean>(true);
  const [darkMode, setDarkMode] = useState<boolean>(false);
  const [language, setLanguage] = useState<string>('ar');
  const [printerName, setPrinterName] = useState<string>('HP LaserJet Pro');
  const [paperSize, setPaperSize] = useState<string>('A4');

  // دوال النسخ الاحتياطي الشامل
  const getAllSystemData = () => {
    try {
      const systemData = {
        exportInfo: {
          date: new Date().toISOString(),
          time: new Date().toLocaleString('ar-SA'),
          version: '1.0.0',
          type: 'complete_system_backup'
        },
        inventory: {
          warehouse: {
            items: JSON.parse(localStorage.getItem('warehouseInventory') || '[]'),
            movements: JSON.parse(localStorage.getItem('warehouseMovements') || '[]'),
            availableItems: JSON.parse(localStorage.getItem('warehouseAvailableItems') || '[]')
          }
        },
        financial: {
          cashTransactions: JSON.parse(localStorage.getItem('cashTransactions') || '[]'),
          advances: JSON.parse(localStorage.getItem('advances') || '[]')
        },
        personnel: {
          drivers: JSON.parse(localStorage.getItem('drivers') || '[]'),
          employees: JSON.parse(localStorage.getItem('employees') || '[]')
        },
        suppliers: JSON.parse(localStorage.getItem('suppliers') || '[]'),
        settings: {
          companyInfo: {
            companyName,
            companyNameEn,
            managerName,
            phone,
            email,
            address,
            website,
            taxNumber,
            commercialRegister
          },
          systemSettings: {
            autoBackup,
            backupFrequency,
            notificationsEnabled,
            darkMode,
            language,
            printerName,
            paperSize
          }
        }
      };

      return systemData;
    } catch (error) {
      console.error('خطأ في جمع بيانات النظام:', error);
      throw error;
    }
  };

  const exportCompleteBackup = async () => {
    // إظهار مؤشر التحميل
    const originalButtonText = 'إنشاء نسخة احتياطية شاملة';
    const button = document.querySelector('[data-backup-export]') as HTMLButtonElement;
    if (button) {
      button.disabled = true;
      button.innerHTML = '<div class="flex items-center gap-2"><div class="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>جاري التصدير...</div>';
    }

    try {
      console.log('🔄 بدء تصدير النسخة الاحتياطية الشاملة...');

      // التحقق من وجود بيانات
      const systemData = getAllSystemData();

      // حساب الإحصائيات
      const stats = {
        totalWarehouseItems: systemData.inventory.warehouse.items.length,
        totalWarehouseMovements: systemData.inventory.warehouse.movements.length,
        totalWarehouseAvailableItems: systemData.inventory.warehouse.availableItems.length,
        totalCashTransactions: systemData.financial.cashTransactions.length,
        totalAdvances: systemData.financial.advances.length,
        totalDrivers: systemData.personnel.drivers.length,
        totalEmployees: systemData.personnel.employees.length,
        totalSuppliers: systemData.suppliers.length
      };

      // التحقق من وجود بيانات للتصدير
      const totalRecords = Object.values(stats).reduce((sum, count) => sum + count, 0);
      if (totalRecords === 0) {
        alert('⚠️ لا توجد بيانات للتصدير. يرجى إضافة بعض البيانات أولاً.');
        return;
      }

      const backupData = {
        ...systemData,
        statistics: stats
      };

      console.log('📊 إحصائيات النسخة الاحتياطية:', stats);

      // إنشاء الملف
      const jsonString = JSON.stringify(backupData, null, 2);
      const blob = new Blob([jsonString], { type: 'application/json;charset=utf-8' });
      const url = URL.createObjectURL(blob);

      // إنشاء اسم الملف مع الوقت
      const now = new Date();
      const dateStr = now.toLocaleDateString('en-GB').split('/').reverse().join('-');
      const timeStr = now.toLocaleTimeString('en-GB', { hour: '2-digit', minute: '2-digit' }).replace(/:/g, '-');
      const fileName = `complete-system-backup-${dateStr}-${timeStr}.json`;

      const link = document.createElement('a');
      link.href = url;
      link.download = fileName;
      link.style.display = 'none';
      document.body.appendChild(link);

      console.log('🔗 رابط التحميل:', url);
      link.click();

      // تنظيف
      setTimeout(() => {
        if (document.body.contains(link)) {
          document.body.removeChild(link);
        }
        URL.revokeObjectURL(url);
      }, 1000);

      console.log('✅ تم تصدير النسخة الاحتياطية الشاملة بنجاح');

      // رسالة نجاح مفصلة
      const successMessage = `✅ تم تصدير النسخة الاحتياطية الشاملة بنجاح!\n\n` +
        `📁 اسم الملف: ${fileName}\n` +
        `📊 الإحصائيات:\n` +
        `• الأصناف: ${stats.totalWarehouseItems}\n` +
        `• الحركات: ${stats.totalWarehouseMovements}\n` +
        `• المعاملات المالية: ${stats.totalCashTransactions}\n` +
        `• السلف: ${stats.totalAdvances}\n` +
        `• السائقين: ${stats.totalDrivers}\n` +
        `• الموظفين: ${stats.totalEmployees}\n` +
        `• الموردين: ${stats.totalSuppliers}\n\n` +
        `📦 إجمالي السجلات: ${totalRecords}`;

      alert(successMessage);

    } catch (error) {
      console.error('❌ خطأ في تصدير النسخة الاحتياطية:', error);
      let msg = '❌ حدث خطأ أثناء تصدير النسخة الاحتياطية.';
      if (error instanceof Error) {
        msg += `\n${error.message}`;
      }
      alert(`${msg}\n\nيرجى المحاولة مرة أخرى أو الاتصال بالدعم الفني.`);
    } finally {
      // إعادة تعيين الزر
      if (button) {
        button.disabled = false;
        button.innerHTML = `<svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path></svg>إنشاء نسخة احتياطية شاملة`;
      }
    }
  };

  const importCompleteBackup = async () => {
    // إظهار مؤشر التحميل
    const button = document.querySelector('[data-backup-import]') as HTMLButtonElement;
    if (button) {
      button.disabled = true;
      button.innerHTML = '<div class="flex items-center gap-2"><div class="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>جاري الاستيراد...</div>';
    }

    try {
      console.log('🔄 بدء استيراد النسخة الاحتياطية...');

      const input = document.createElement('input');
      input.type = 'file';
      input.accept = '.json';
      input.style.display = 'none';

      const filePromise = new Promise<File | null>((resolve) => {
        input.onchange = (event) => {
          const file = (event.target as HTMLInputElement).files?.[0];
          resolve(file || null);
        };

        input.oncancel = () => {
          resolve(null);
        };
      });

      document.body.appendChild(input);
      input.click();

      const file = await filePromise;
      document.body.removeChild(input);

      if (!file) {
        console.log('❌ لم يتم اختيار ملف');
        return;
      }

      console.log('📁 تم اختيار الملف:', file.name, 'الحجم:', Math.round(file.size / 1024), 'KB');

      // التحقق من حجم الملف
      if (file.size > 10 * 1024 * 1024) { // 10MB
        alert('❌ حجم الملف كبير جداً. يجب أن يكون أقل من 10 ميجابايت.');
        return;
      }

      const reader = new FileReader();

      const readPromise = new Promise<string>((resolve, reject) => {
        reader.onload = (e) => {
          const content = e.target?.result as string;
          resolve(content);
        };

        reader.onerror = () => {
          reject(new Error('فشل في قراءة الملف'));
        };
      });

      reader.readAsText(file, 'utf-8');
      const content = await readPromise;

      let backupData;
      try {
        backupData = JSON.parse(content);
      } catch (parseError) {
        alert('❌ الملف تالف أو ليس بتنسيق JSON صحيح.');
        return;
      }

      console.log('📊 البيانات المستوردة:', backupData);

      // التحقق من صحة البيانات
      if (!backupData.exportInfo || backupData.exportInfo.type !== 'complete_system_backup') {
        alert('❌ تنسيق الملف غير صحيح. يجب أن يكون ملف نسخة احتياطية شاملة من هذا النظام.');
        return;
      }

      const stats = backupData.statistics || {};
      const totalRecords = Object.values(stats).reduce((sum: number, count: any) => sum + (count || 0), 0);

      const confirmMessage = `هل تريد استيراد النسخة الاحتياطية؟\n\n` +
        `📁 اسم الملف: ${file.name}\n` +
        `📅 تاريخ النسخة: ${new Date(backupData.exportInfo.date).toLocaleDateString('en-GB')}
` +
        `⏰ وقت النسخة: ${new Date(backupData.exportInfo.date).toLocaleTimeString('en-GB', { hour: '2-digit', minute: '2-digit' })}

` +
        `📊 الإحصائيات:\n` +
        `• أصناف المستودع: ${stats.totalWarehouseItems || 0}\n` +
        `• حركات المستودع: ${stats.totalWarehouseMovements || 0}\n` +
        `• أصناف المستودع المتاحة: ${stats.totalWarehouseAvailableItems || 0}\n` +
        `• المعاملات المالية: ${stats.totalCashTransactions || 0}\n` +
        `• السلف: ${stats.totalAdvances || 0}\n` +
        `• السائقين: ${stats.totalDrivers || 0}\n` +
        `• الموظفين: ${stats.totalEmployees || 0}\n` +
        `• الموردين: ${stats.totalSuppliers || 0}\n\n` +
        `📦 إجمالي السجلات: ${totalRecords}\n\n` +
        `⚠️ تحذير: سيتم استبدال جميع البيانات الحالية!`;

      const confirmImport = confirm(confirmMessage);

      if (confirmImport) {
        // استيراد البيانات مع معالجة الأخطاء
        try {
          if (backupData.inventory) {
            // استيراد بيانات المستودع
            if (backupData.inventory.warehouse) {
              localStorage.setItem('warehouseInventory', JSON.stringify(backupData.inventory.warehouse.items || []));
              localStorage.setItem('warehouseMovements', JSON.stringify(backupData.inventory.warehouse.movements || []));
              localStorage.setItem('warehouseAvailableItems', JSON.stringify(backupData.inventory.warehouse.availableItems || []));
            }
          }

          if (backupData.financial) {
            localStorage.setItem('cashTransactions', JSON.stringify(backupData.financial.cashTransactions || []));
            localStorage.setItem('advances', JSON.stringify(backupData.financial.advances || []));
          }

          if (backupData.personnel) {
            localStorage.setItem('drivers', JSON.stringify(backupData.personnel.drivers || []));
            localStorage.setItem('employees', JSON.stringify(backupData.personnel.employees || []));
          }

          if (backupData.suppliers) {
            localStorage.setItem('suppliers', JSON.stringify(backupData.suppliers || []));
          }

          console.log('✅ تم استيراد النسخة الاحتياطية بنجاح');

          const successMessage = `✅ تم استيراد النسخة الاحتياطية بنجاح!\n\n` +
            `📦 تم استيراد ${totalRecords} سجل\n\n` +
            `🔄 يرجى إعادة تشغيل التطبيق لرؤية التغييرات.\n\n` +
            `💡 نصيحة: يمكنك إعادة تشغيل التطبيق بالضغط على F5 أو إغلاق النافذة وفتحها مرة أخرى.`;

          alert(successMessage);

        } catch (storageError) {
          console.error('❌ خطأ في حفظ البيانات:', storageError);
          alert('❌ حدث خطأ أثناء حفظ البيانات. قد تكون مساحة التخزين ممتلئة.');
        }
      }

    } catch (error) {
      console.error('❌ خطأ في استيراد النسخة الاحتياطية:', error);
      let msg = '❌ حدث خطأ أثناء استيراد النسخة الاحتياطية.';
      if (error instanceof Error) {
        msg += `\n${error.message}`;
      }
      alert(`${msg}\n\nيرجى التأكد من صحة الملف والمحاولة مرة أخرى.`);
    } finally {
      // إعادة تعيين الزر
      if (button) {
        button.disabled = false;
        button.innerHTML = `<svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10"></path></svg>استعادة من نسخة احتياطية`;
      }
    }
  };

  // دوال حفظ واسترجاع الإعدادات
  const SETTINGS_KEY = 'systemSettings';

  const saveSettings = () => {
    try {
      const settings = {
        companyName,
        companyNameEn,
        managerName,
        phone,
        email,
        address,
        website,
        taxNumber,
        commercialRegister,
        autoBackup,
        backupFrequency,
        notificationsEnabled,
        darkMode,
        language,
        printerName,
        paperSize
      };
      localStorage.setItem(SETTINGS_KEY, JSON.stringify(settings));
      alert('✅ تم حفظ الإعدادات بنجاح!');
    } catch (error) {
      alert('❌ حدث خطأ أثناء حفظ الإعدادات!');
    }
  };

  const loadSettings = () => {
    try {
      const settingsStr = localStorage.getItem(SETTINGS_KEY);
      if (settingsStr) {
        const settings = JSON.parse(settingsStr);
        setCompanyName(settings.companyName || '');
        setCompanyNameEn(settings.companyNameEn || '');
        setManagerName(settings.managerName || '');
        setPhone(settings.phone || '');
        setEmail(settings.email || '');
        setAddress(settings.address || '');
        setWebsite(settings.website || '');
        setTaxNumber(settings.taxNumber || '');
        setCommercialRegister(settings.commercialRegister || '');
        setAutoBackup(settings.autoBackup ?? true);
        setBackupFrequency(settings.backupFrequency || 'daily');
        setNotificationsEnabled(settings.notificationsEnabled ?? true);
        setDarkMode(settings.darkMode ?? false);
        setLanguage(settings.language || 'ar');
        setPrinterName(settings.printerName || 'HP LaserJet Pro');
        setPaperSize(settings.paperSize || 'A4');
      }
    } catch (error) {
      // تجاهل الخطأ
    }
  };

  React.useEffect(() => {
    loadSettings();
    // eslint-disable-next-line
  }, []);

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-100 via-indigo-100 to-green-100 bg-fixed" dir="rtl">
      {/* Header */}
      <div className="bg-gradient-to-r from-indigo-700 via-blue-600 to-green-600 text-white p-6 shadow-2xl rounded-b-3xl">
        <div className="container mx-auto">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <div className="w-12 h-12 bg-white/30 rounded-2xl flex items-center justify-center backdrop-blur-md shadow-lg border-2 border-indigo-300">
                <Leaf size={24} className="text-white" />
              </div>
              <div>
                <h1 className="text-2xl font-bold font-arabic tracking-wide text-white drop-shadow-lg">شركة الشفق للزراعة الحديثة</h1>
                <p className="text-indigo-100 text-sm font-medium tracking-wider">Al-Shafaq Modern Agriculture Company</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="container mx-auto py-8">
        {/* Page Header */}
        <div className="flex items-center justify-between mb-8">
          <div className="flex items-center gap-4">
            <div className="w-12 h-12 bg-gradient-to-br from-indigo-500 to-blue-600 rounded-2xl flex items-center justify-center shadow-xl border-2 border-indigo-300">
              <Settings size={24} className="text-white" />
            </div>
            <h2 className="text-3xl font-bold font-arabic text-indigo-800 tracking-wide drop-shadow">إعدادات النظام</h2>
          </div>
          {/* Save Button */}
          <div className="flex gap-3">
            <button
              className="bg-gradient-to-r from-blue-500 to-indigo-600 text-white px-6 py-3 rounded-xl hover:from-indigo-600 hover:to-blue-700 transition-all duration-300 flex items-center gap-2 shadow-xl hover:shadow-2xl transform hover:scale-105 font-arabic font-medium tracking-wide"
              onClick={saveSettings}
            >
              <Save size={20} />
              حفظ الإعدادات
            </button>
          </div>
        </div>

        {/* Settings Tabs */}
        <div className="flex border-b bg-gradient-to-r from-indigo-50 to-blue-50">
          <button
            onClick={() => setActiveTab('general')}
            className={`px-6 py-4 font-medium text-sm flex items-center gap-2 ${activeTab === 'general' ? 'border-b-2 border-blue-500 text-blue-600' : 'text-gray-500 hover:text-gray-700'}`}
          >
            <Building size={18} />
            معلومات الشركة
          </button>
          <button
            onClick={() => setActiveTab('system')}
            className={`px-6 py-4 font-medium text-sm flex items-center gap-2 ${activeTab === 'system' ? 'border-b-2 border-blue-500 text-blue-600' : 'text-gray-500 hover:text-gray-700'}`}
          >
            <Monitor size={18} />
            إعدادات النظام
          </button>
          <button
            onClick={() => setActiveTab('backup')}
            className={`px-6 py-4 font-medium text-sm flex items-center gap-2 ${activeTab === 'backup' ? 'border-b-2 border-blue-500 text-blue-600' : 'text-gray-500 hover:text-gray-700'}`}
          >
            <Database size={18} />
            النسخ الاحتياطي
          </button>
          <button
            onClick={() => setActiveTab('security')}
            className={`px-6 py-4 font-medium text-sm flex items-center gap-2 ${activeTab === 'security' ? 'border-b-2 border-blue-500 text-blue-600' : 'text-gray-500 hover:text-gray-700'}`}
          >
            <Shield size={18} />
            الأمان
          </button>
          <button
            onClick={() => setActiveTab('about')}
            className={`px-6 py-4 font-medium text-sm flex items-center gap-2 ${activeTab === 'about' ? 'border-b-2 border-blue-500 text-blue-600' : 'text-gray-500 hover:text-gray-700'}`}
          >
            <Info size={18} />
            حول النظام
          </button>
        </div>

        {/* تبويبات الإعدادات */}
        <div className="py-8">
          {/* General Settings */}
          {activeTab === 'general' && (
            <div className="space-y-6">
              <h3 className="text-lg font-bold text-gray-800 mb-4">معلومات الشركة الأساسية</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">اسم الشركة (عربي)</label>
                  <input
                    type="text"
                    value={companyName}
                    onChange={(e) => setCompanyName(e.target.value)}
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">اسم الشركة (إنجليزي)</label>
                  <input
                    type="text"
                    value={companyNameEn}
                    onChange={(e) => setCompanyNameEn(e.target.value)}
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">اسم المدير</label>
                  <input
                    type="text"
                    value={managerName}
                    onChange={(e) => setManagerName(e.target.value)}
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">رقم الهاتف</label>
                  <input
                    type="text"
                    value={phone}
                    onChange={(e) => setPhone(e.target.value)}
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">البريد الإلكتروني</label>
                  <input
                    type="email"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">العنوان</label>
                  <input
                    type="text"
                    value={address}
                    onChange={(e) => setAddress(e.target.value)}
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">الموقع الإلكتروني</label>
                  <input
                    type="url"
                    value={website}
                    onChange={(e) => setWebsite(e.target.value)}
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">رقم الضريبة</label>
                  <input
                    type="text"
                    value={taxNumber}
                    onChange={(e) => setTaxNumber(e.target.value)}
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">سجل تجاري</label>
                  <input
                    type="text"
                    value={commercialRegister}
                    onChange={(e) => setCommercialRegister(e.target.value)}
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                  />
                </div>
              </div>
            </div>
          )}
          {/* System Settings */}
          {activeTab === 'system' && (
            <div className="space-y-6">
              <h3 className="text-lg font-bold text-gray-800 mb-4">إعدادات النظام</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">تفعيل النسخ الاحتياطي التلقائي</label>
                  <input type="checkbox" checked={autoBackup} onChange={e => setAutoBackup(e.target.checked)} className="form-checkbox h-5 w-5 text-emerald-600" />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">تفعيل الإشعارات</label>
                  <input type="checkbox" checked={notificationsEnabled} onChange={e => setNotificationsEnabled(e.target.checked)} className="form-checkbox h-5 w-5 text-emerald-600" />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">اللغة</label>
                  <select value={language} onChange={e => setLanguage(e.target.value)} className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors">
                    <option value="ar">العربية</option>
                    <option value="en">English</option>
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">الوضع الليلي</label>
                  <input type="checkbox" checked={darkMode} onChange={e => setDarkMode(e.target.checked)} className="form-checkbox h-5 w-5 text-emerald-600" />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">اسم الطابعة</label>
                  <input
                    type="text"
                    value={printerName}
                    onChange={(e) => setPrinterName(e.target.value)}
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">حجم الورق</label>
                  <select value={paperSize} onChange={e => setPaperSize(e.target.value)} className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors">
                    <option value="A4">A4</option>
                    <option value="Letter">Letter</option>
                    <option value="Legal">Legal</option>
                  </select>
                </div>
              </div>
            </div>
          )}
          {/* Backup */}
          {activeTab === 'backup' && (
            <div className="flex flex-col items-center justify-center gap-8 py-12">
              <div className="text-center">
                <h2 className="text-2xl font-bold text-emerald-700 mb-4 font-arabic">النسخ الاحتياطي الشامل</h2>
                <p className="text-gray-600 mb-6">يمكنك تصدير نسخة احتياطية كاملة من جميع بيانات النظام.</p>
              </div>
              <button onClick={exportCompleteBackup} className="bg-gradient-to-r from-emerald-500 to-green-600 text-white py-4 px-10 rounded-2xl font-bold text-xl flex items-center gap-3 shadow-lg hover:from-emerald-600 hover:to-green-700 transition-all duration-300 transform hover:scale-105 font-arabic">
                <Download size={28} />
                إنشاء نسخة احتياطية شاملة
              </button>
            </div>
          )}
          {/* Security */}
          {activeTab === 'security' && (
            <div className="space-y-6">
              <h3 className="text-lg font-bold text-gray-800 mb-4">إعدادات الأمان</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">كلمة المرور الرئيسية</label>
                  <input type="password" className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors" />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">تفعيل التحقق من الهوية</label>
                  <input type="checkbox" className="form-checkbox h-5 w-5 text-emerald-600" />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">تفعيل التحقق من التطبيق</label>
                  <input type="checkbox" className="form-checkbox h-5 w-5 text-emerald-600" />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">تفعيل التحقق من المستخدم</label>
                  <input type="checkbox" className="form-checkbox h-5 w-5 text-emerald-600" />
                </div>
              </div>
            </div>
          )}
          {/* About */}
          {activeTab === 'about' && (
            <div className="space-y-6">
              <h3 className="text-lg font-bold text-gray-800 mb-4">حول النظام</h3>
              <p className="text-gray-700">
                هذا النظام هو أحدث تطبيق للزراعة الحديثة والتسويق.
                يتم تطويره باستمرار لتحسين الوظائف والأداء.
              </p>
              <p className="text-gray-700">
                الإصدار: 1.0.0
              </p>
              <p className="text-gray-700">
                تاريخ الإصدار: 2023-10-27
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default SettingsView;