import React, { useState, useEffect } from 'react';
import {
  Plus,
  FileText,
  Car,
  User,
  MapPin,
  Phone,
  Edit,
  Leaf,
  Trash2,
  Calendar,
  Save,
  X,
  Printer,
  Download
} from 'lucide-react';
import Modal from './Modal';

interface Driver {
  id: number;
  name: string;
  phones: string[];
  joinDate: string;
  photo?: string;
  shippingOffice?: string;
  notes?: string;
}

interface DriversViewProps {
  setCurrentView: (view: string) => void;
  navigationItems: any[];
  currentView: string;
}

const DriversView: React.FC<DriversViewProps> = ({
  setCurrentView,
  navigationItems,
  currentView
}) => {
  // حالة السائقين المحفوظة محلياً
  const [drivers, setDrivers] = useState<Driver[]>([]);

  // حالات النوافذ المنبثقة
  const [isAddModalOpen, setIsAddModalOpen] = useState<boolean>(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState<boolean>(false);
  const [isReportModalOpen, setIsReportModalOpen] = useState<boolean>(false);

  // حالات البيانات الجديدة
  const [newDriver, setNewDriver] = useState<{
    name: string;
    phones: string[];
    photo: string;
    shippingOffice: string;
    notes: string;
  }>({
    name: '',
    phones: [''],
    photo: '',
    shippingOffice: '',
    notes: ''
  });

  const [editingDriver, setEditingDriver] = useState<Driver | null>(null);

  // تحميل البيانات من localStorage
  useEffect(() => {
    const loadDrivers = () => {
      try {
        const savedDrivers = localStorage.getItem('drivers');
        if (savedDrivers) {
          setDrivers(JSON.parse(savedDrivers));
        }
      } catch (error) {
        console.error('خطأ في تحميل بيانات السائقين:', error);
      }
    };

    loadDrivers();
  }, []);

  // حفظ البيانات في localStorage
  useEffect(() => {
    localStorage.setItem('drivers', JSON.stringify(drivers));
  }, [drivers]);

  // دوال التنسيق
  const formatDate = (date: Date) => {
    const day = date.getDate().toString().padStart(2, '0');
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const year = date.getFullYear();
    return `${day}/${month}/${year}`;
  };

  // دوال إدارة أرقام الهواتف
  const addPhoneField = () => {
    setNewDriver({...newDriver, phones: [...newDriver.phones, '']});
  };

  const removePhoneField = (index: number) => {
    if (newDriver.phones.length > 1) {
      const updatedPhones = newDriver.phones.filter((_, i) => i !== index);
      setNewDriver({...newDriver, phones: updatedPhones});
    }
  };

  const updatePhoneField = (index: number, value: string) => {
    const updatedPhones = [...newDriver.phones];
    updatedPhones[index] = value;
    setNewDriver({...newDriver, phones: updatedPhones});
  };

  // دالة رفع الصورة
  const handlePhotoUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        const result = e.target?.result as string;
        setNewDriver({...newDriver, photo: result});
      };
      reader.readAsDataURL(file);
    }
  };

  // دوال المعالجة
  const handleAddDriver = () => {
    try {
      if (!newDriver.name.trim()) {
        alert('يرجى إدخال اسم السائق');
        return;
      }

      // التحقق من وجود رقم هاتف واحد على الأقل
      const validPhones = newDriver.phones.filter(phone => phone.trim() !== '');
      if (validPhones.length === 0) {
        alert('يرجى إدخال رقم هاتف واحد على الأقل');
        return;
      }

      const newDriverItem: Driver = {
        id: Date.now(),
        name: newDriver.name.trim(),
        phones: validPhones,
        joinDate: formatDate(new Date()),
        photo: newDriver.photo,
        shippingOffice: newDriver.shippingOffice.trim(),
        notes: newDriver.notes
      };

      setDrivers([newDriverItem, ...drivers]);
      setNewDriver({
        name: '',
        phones: [''],
        photo: '',
        shippingOffice: '',
        notes: ''
      });
      setIsAddModalOpen(false);
      alert('تم إضافة السائق بنجاح!');
    } catch (error) {
      console.error('خطأ في إضافة السائق:', error);
      alert('حدث خطأ أثناء إضافة السائق');
    }
  };

  const handleEditDriver = (driver: Driver) => {
    setEditingDriver(driver);
    setIsEditModalOpen(true);
  };

  // دوال إدارة أرقام الهواتف للتعديل
  const addEditPhoneField = () => {
    if (editingDriver) {
      setEditingDriver({...editingDriver, phones: [...editingDriver.phones, '']});
    }
  };

  const removeEditPhoneField = (index: number) => {
    if (editingDriver && editingDriver.phones.length > 1) {
      const updatedPhones = editingDriver.phones.filter((_, i) => i !== index);
      setEditingDriver({...editingDriver, phones: updatedPhones});
    }
  };

  const updateEditPhoneField = (index: number, value: string) => {
    if (editingDriver) {
      const updatedPhones = [...editingDriver.phones];
      updatedPhones[index] = value;
      setEditingDriver({...editingDriver, phones: updatedPhones});
    }
  };

  // دالة رفع الصورة للتعديل
  const handleEditPhotoUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file && editingDriver) {
      const reader = new FileReader();
      reader.onload = (e) => {
        const result = e.target?.result as string;
        setEditingDriver({...editingDriver, photo: result});
      };
      reader.readAsDataURL(file);
    }
  };

  const handleSaveEdit = () => {
    if (!editingDriver) return;

    try {
      const updatedDrivers = drivers.map(driver =>
        driver.id === editingDriver.id ? editingDriver : driver
      );
      setDrivers(updatedDrivers);
      setIsEditModalOpen(false);
      setEditingDriver(null);
      alert('تم تحديث البيانات بنجاح!');
    } catch (error) {
      console.error('خطأ في تحديث البيانات:', error);
      alert('حدث خطأ أثناء تحديث البيانات');
    }
  };

  const handleDeleteDriver = (id: number) => {
    if (window.confirm('هل أنت متأكد من حذف هذا السائق؟')) {
      setDrivers(drivers.filter(driver => driver.id !== id));
      alert('تم حذف السائق بنجاح!');
    }
  };

  const handlePrint = () => {
    window.print();
  };

  const handleExportReport = () => {
    try {
      const today = new Date();
      const formattedDate = today.toLocaleDateString('ar-SA', {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      });

      const htmlContent = `
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تقرير السائقين - شركة الشفق للزراعة الحديثة</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background: white;
            direction: rtl;
            text-align: right;
        }

        .container {
            max-width: 210mm;
            margin: 0 auto;
            padding: 20mm;
            background: white;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 3px solid #dc2626;
            padding-bottom: 20px;
        }

        .company-name {
            font-size: 28px;
            font-weight: bold;
            color: #dc2626;
            margin-bottom: 10px;
        }

        .company-name-en {
            font-size: 16px;
            color: #6b7280;
            margin-bottom: 20px;
        }

        .report-title {
            font-size: 24px;
            font-weight: bold;
            color: #1f2937;
            margin-bottom: 10px;
        }

        .report-date {
            font-size: 14px;
            color: #6b7280;
        }

        .summary-box {
            background: #fef2f2;
            border: 1px solid #dc2626;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }

        .summary-title {
            font-size: 18px;
            font-weight: bold;
            color: #991b1b;
            margin-bottom: 15px;
        }

        .summary-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }

        .summary-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px;
            background: white;
            border-radius: 6px;
            border: 1px solid #fecaca;
        }

        .summary-label {
            font-weight: 500;
            color: #374151;
        }

        .summary-value {
            font-weight: bold;
            color: #dc2626;
        }

        .drivers-list {
            margin-top: 30px;
        }

        .driver-card {
            background: #f9fafb;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
            border-right: 4px solid #dc2626;
        }

        .driver-name {
            font-size: 16px;
            font-weight: bold;
            color: #1f2937;
            margin-bottom: 8px;
        }

        .driver-info {
            font-size: 14px;
            color: #6b7280;
            margin-bottom: 5px;
        }

        .phones-list {
            margin: 5px 0;
        }

        .phone-item {
            display: inline-block;
            background: #dbeafe;
            color: #1e40af;
            padding: 2px 8px;
            border-radius: 4px;
            margin: 2px;
            font-size: 12px;
        }

        .footer {
            margin-top: 50px;
            text-align: center;
            font-size: 12px;
            color: #6b7280;
            border-top: 1px solid #e5e7eb;
            padding-top: 20px;
        }

        @media print {
            body {
                margin: 0;
                padding: 0;
            }

            .container {
                max-width: none;
                margin: 0;
                padding: 15mm;
            }
        }

        @page {
            size: A4;
            margin: 15mm;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="company-name">شركة الشفق للزراعة الحديثة</div>
            <div class="company-name-en">Al-Shafaq Modern Agriculture Company</div>
            <div class="report-title">تقرير السائقين</div>
            <div class="report-date">${formattedDate}</div>
        </div>

        <div class="summary-box">
            <div class="summary-title">ملخص السائقين</div>
            <div class="summary-grid">
                <div class="summary-item">
                    <span class="summary-label">إجمالي السائقين:</span>
                    <span class="summary-value">${totalDrivers}</span>
                </div>
                <div class="summary-item">
                    <span class="summary-label">إجمالي أرقام الهواتف:</span>
                    <span class="summary-value">${totalPhones}</span>
                </div>
                <div class="summary-item">
                    <span class="summary-label">لديهم صور:</span>
                    <span class="summary-value">${driversWithPhotos}</span>
                </div>
                <div class="summary-item">
                    <span class="summary-label">بدون صور:</span>
                    <span class="summary-value">${driversWithoutPhotos}</span>
                </div>
            </div>
        </div>

        <div class="drivers-list">
            <h3 style="color: #dc2626; margin-bottom: 20px;">قائمة السائقين المفصلة</h3>
            ${drivers.length === 0 ?
              '<p style="text-align: center; color: #6b7280; padding: 40px;">لا يوجد سائقين مسجلين</p>' :
              drivers.map((driver, index) => `
                <div class="driver-card">
                    <div class="driver-name">${index + 1}. ${driver.name}</div>
                    <div class="driver-info">تاريخ الانضمام: ${driver.joinDate}</div>
                    <div class="phones-list">
                        <strong>أرقام الهواتف:</strong>
                        ${driver.phones.map(phone => `<span class="phone-item">${phone}</span>`).join('')}
                    </div>
                    ${driver.shippingOffice ? `<div class="driver-info"><strong>مكتب الشحن:</strong> ${driver.shippingOffice}</div>` : ''}
                    ${driver.notes ? `<div class="driver-info"><strong>ملاحظات:</strong> ${driver.notes}</div>` : ''}
                </div>
              `).join('')
            }
        </div>

        <div class="footer">
            <p>تم إنشاء هذا التقرير بواسطة نظام إدارة المخزون - شركة الشفق للزراعة الحديثة</p>
            <p>تاريخ الطباعة: ${formattedDate}</p>
        </div>
    </div>
</body>
</html>`;

      const blob = new Blob([htmlContent], { type: 'text/html;charset=utf-8' });
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `تقرير-السائقين-${formatDate(new Date()).replace(/\//g, '-')}.html`;
      link.click();
      URL.revokeObjectURL(url);
      alert('تم تصدير التقرير بنجاح!');
    } catch (error) {
      console.error('خطأ في تصدير التقرير:', error);
      alert('حدث خطأ أثناء تصدير التقرير');
    }
  };

  // حساب الإحصائيات
  const totalDrivers = drivers.length;
  const totalPhones = drivers.reduce((sum, driver) => sum + driver.phones.length, 0);
  const driversWithPhotos = drivers.filter(driver => driver.photo).length;
  const driversWithoutPhotos = drivers.filter(driver => !driver.photo).length;

  // تاريخ اليوم بالميلادي
  const today = new Date();
  const weekdays = ['الأحد', 'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت'];
  const months = ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو', 'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'];
  const formattedDate = `${weekdays[today.getDay()]}، ${today.getDate()} ${months[today.getMonth()]} ${today.getFullYear()}`;

  return (
    <div className="min-h-screen bg-gradient-to-br from-red-50 via-blue-50 to-green-100 p-6 space-y-6">
      {/* Header */}
      <div className="bg-gradient-to-r from-indigo-700 via-red-600 to-green-600 text-white p-6 shadow-2xl rounded-b-3xl">
        <div className="container mx-auto">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <div className="w-12 h-12 bg-white/30 rounded-2xl flex items-center justify-center backdrop-blur-md shadow-lg border-2 border-indigo-300">
                <Car size={24} className="text-white" />
              </div>
              <div>
                <h1 className="text-2xl font-bold font-arabic tracking-wide text-white drop-shadow-lg">إدارة السائقين</h1>
                <p className="text-indigo-100 text-sm font-medium tracking-wider">Drivers Management</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Navigation */}
      <div className="bg-gradient-to-r from-red-500 to-rose-500 p-4 shadow-lg">
        <div className="container mx-auto">
          <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 xl:grid-cols-9 gap-3">
            {navigationItems.map((item) => {
              const IconComponent = item.icon;
              return (
                <button
                  key={item.id}
                  onClick={() => setCurrentView(item.id)}
                  className={`p-4 rounded-2xl transition-all duration-300 transform hover:scale-105 hover:shadow-lg ${
                    currentView === item.id
                      ? 'bg-white text-red-600 shadow-xl border-2 border-red-500'
                      : 'bg-white text-red-600 hover:bg-gray-50 shadow-md'
                  }`}
                >
                  <IconComponent size={24} className="mx-auto mb-2 text-red-600" />
                  <span className="text-sm font-bold block font-arabic text-red-600">{item.name}</span>
                </button>
              );
            })}
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="container mx-auto p-6">
        {/* Page Header */}
        <div className="flex items-center justify-between mb-8">
          <div className="flex items-center gap-4">
            <div className="w-12 h-12 bg-gradient-to-br from-red-500 to-rose-600 rounded-2xl flex items-center justify-center shadow-lg">
              <Car size={24} className="text-white" />
            </div>
            <div>
              <h2 className="text-3xl font-bold font-arabic text-gray-800 tracking-wide">إدارة السائقين</h2>
              <p className="text-gray-600 text-sm mt-1">{formattedDate}</p>
            </div>
          </div>
          
          {/* Action Buttons */}
          <div className="flex gap-3">
            <button
              onClick={() => setIsAddModalOpen(true)}
              className="bg-gradient-to-r from-red-500 to-red-600 text-white px-6 py-3 rounded-xl hover:from-red-600 hover:to-red-700 transition-all duration-300 flex items-center gap-2 shadow-lg hover:shadow-xl transform hover:scale-105 font-arabic font-medium tracking-wide"
            >
              <Plus size={20} />
              إضافة سائق
            </button>
            <button
              onClick={() => setIsReportModalOpen(true)}
              className="bg-gradient-to-r from-blue-500 to-blue-600 text-white px-6 py-3 rounded-xl hover:from-blue-600 hover:to-blue-700 transition-all duration-300 flex items-center gap-2 shadow-lg hover:shadow-xl transform hover:scale-105 font-arabic font-medium tracking-wide"
            >
              <FileText size={20} />
              تقرير السائقين
            </button>
          </div>
        </div>

        {/* Statistics Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <div className="bg-white rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 border border-red-100">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-gray-600 text-sm font-medium mb-2 font-arabic tracking-wide">إجمالي السائقين</h3>
                <p className="text-3xl font-bold text-red-600 font-arabic">{totalDrivers}</p>
              </div>
              <div className="w-12 h-12 bg-gradient-to-br from-red-500 to-red-600 rounded-2xl flex items-center justify-center">
                <User size={24} className="text-white" />
              </div>
            </div>
          </div>

          <div className="bg-white rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 border border-green-100">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-gray-600 text-sm font-medium mb-2 font-arabic tracking-wide">إجمالي أرقام الهواتف</h3>
                <p className="text-3xl font-bold text-green-600 font-arabic">{totalPhones}</p>
              </div>
              <div className="w-12 h-12 bg-gradient-to-br from-green-500 to-green-600 rounded-2xl flex items-center justify-center">
                <Phone size={24} className="text-white" />
              </div>
            </div>
          </div>

          <div className="bg-white rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 border border-blue-100">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-gray-600 text-sm font-medium mb-2 font-arabic tracking-wide">لديهم صور</h3>
                <p className="text-3xl font-bold text-blue-600 font-arabic">{driversWithPhotos}</p>
              </div>
              <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-2xl flex items-center justify-center">
                <User size={24} className="text-white" />
              </div>
            </div>
          </div>

          <div className="bg-white rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-100">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-gray-600 text-sm font-medium mb-2 font-arabic tracking-wide">بدون صور</h3>
                <p className="text-3xl font-bold text-gray-600 font-arabic">{driversWithoutPhotos}</p>
              </div>
              <div className="w-12 h-12 bg-gradient-to-br from-gray-500 to-gray-600 rounded-2xl flex items-center justify-center">
                <User size={24} className="text-white" />
              </div>
            </div>
          </div>
        </div>

        {/* Drivers Table */}
        <div className="bg-white rounded-2xl shadow-xl overflow-hidden">
          <div className="p-6 bg-gradient-to-r from-red-500 to-rose-500 text-white">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <Car size={24} />
                <h3 className="text-xl font-bold font-arabic">قائمة السائقين</h3>
              </div>
              <div className="text-sm bg-white/20 px-3 py-1 rounded-lg backdrop-blur-sm">
                {new Date().toLocaleDateString('ar-SA', { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' })}
              </div>
            </div>
          </div>
          
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-4 text-right text-sm font-medium text-gray-500 tracking-wider">#</th>
                  <th className="px-6 py-4 text-right text-sm font-medium text-gray-500 tracking-wider">الصورة</th>
                  <th className="px-6 py-4 text-right text-sm font-medium text-gray-500 tracking-wider">الاسم</th>
                  <th className="px-6 py-4 text-right text-sm font-medium text-gray-500 tracking-wider">أرقام الهواتف</th>
                  <th className="px-6 py-4 text-right text-sm font-medium text-gray-500 tracking-wider">مكتب الشحن</th>
                  <th className="px-6 py-4 text-right text-sm font-medium text-gray-500 tracking-wider">تاريخ الانضمام</th>
                  <th className="px-6 py-4 text-right text-sm font-medium text-gray-500 tracking-wider">ملاحظات</th>
                  <th className="px-6 py-4 text-right text-sm font-medium text-gray-500 tracking-wider">إجراءات</th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-200">
                {drivers.map((driver, index) => (
                  <tr key={driver.id} className={index % 2 === 0 ? 'bg-white' : 'bg-gray-50'}>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{index + 1}</td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="w-12 h-12 rounded-full overflow-hidden bg-gray-200 flex items-center justify-center">
                        {driver.photo ? (
                          <img
                            src={driver.photo}
                            alt={driver.name}
                            className="w-full h-full object-cover"
                          />
                        ) : (
                          <User size={20} className="text-gray-400" />
                        )}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="font-medium text-gray-900">{driver.name}</div>
                    </td>
                    <td className="px-6 py-4">
                      <div className="space-y-1">
                        {driver.phones.map((phone, phoneIndex) => (
                          <div key={phoneIndex} className="flex items-center text-gray-500">
                            <Phone size={14} className="mr-1" />
                            <span className="text-sm">{phone}</span>
                          </div>
                        ))}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-500">
                        {driver.shippingOffice || '-'}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center text-gray-500">
                        <Calendar size={14} className="mr-1" />
                        {driver.joinDate}
                      </div>
                    </td>
                    <td className="px-6 py-4">
                      <div className="text-sm text-gray-500 max-w-xs truncate">
                        {driver.notes || '-'}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <div className="flex gap-2">
                        <button
                          onClick={() => handleEditDriver(driver)}
                          className="text-blue-600 hover:text-blue-900 bg-blue-100 hover:bg-blue-200 p-2 rounded-lg transition-colors"
                          title="تعديل"
                        >
                          <Edit size={16} />
                        </button>
                        <button
                          onClick={() => handleDeleteDriver(driver.id)}
                          className="text-red-600 hover:text-red-900 bg-red-100 hover:bg-red-200 p-2 rounded-lg transition-colors"
                          title="حذف"
                        >
                          <Trash2 size={16} />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </div>

      {/* Add Driver Modal */}
      <Modal isOpen={isAddModalOpen} onClose={() => {
        setIsAddModalOpen(false);
        setNewDriver({
          name: '',
          phones: [''],
          photo: '',
          shippingOffice: '',
          notes: ''
        });
      }} title="إضافة سائق جديد">
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">اسم السائق</label>
            <input
              type="text"
              value={newDriver.name}
              onChange={(e) => setNewDriver({...newDriver, name: e.target.value})}
              className="w-full p-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-red-500 focus:border-red-500 transition-all duration-200"
              placeholder="أدخل اسم السائق"
              dir="rtl"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">أرقام الهواتف</label>
            <div className="space-y-2">
              {newDriver.phones.map((phone, index) => (
                <div key={index} className="flex gap-2">
                  <input
                    type="tel"
                    value={phone}
                    onChange={(e) => updatePhoneField(index, e.target.value)}
                    className="flex-1 p-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-red-500 focus:border-red-500 transition-all duration-200"
                    placeholder={`رقم الهاتف ${index + 1}`}
                    dir="ltr"
                  />
                  {newDriver.phones.length > 1 && (
                    <button
                      type="button"
                      onClick={() => removePhoneField(index)}
                      className="px-3 py-2 bg-red-100 text-red-600 rounded-xl hover:bg-red-200 transition-colors"
                      title="حذف رقم الهاتف"
                    >
                      <X size={16} />
                    </button>
                  )}
                </div>
              ))}
              <button
                type="button"
                onClick={addPhoneField}
                className="w-full p-2 border-2 border-dashed border-gray-300 rounded-xl text-gray-500 hover:border-red-300 hover:text-red-500 transition-colors"
              >
                + إضافة رقم هاتف آخر
              </button>
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">الصورة الشخصية</label>
            <div className="flex items-center gap-4">
              <div className="w-20 h-20 rounded-full overflow-hidden bg-gray-200 flex items-center justify-center">
                {newDriver.photo ? (
                  <img
                    src={newDriver.photo}
                    alt="معاينة الصورة"
                    className="w-full h-full object-cover"
                  />
                ) : (
                  <User size={24} className="text-gray-400" />
                )}
              </div>
              <div className="flex-1">
                <input
                  type="file"
                  accept="image/*"
                  onChange={handlePhotoUpload}
                  className="w-full p-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-red-500 focus:border-red-500 transition-all duration-200"
                />
                <p className="text-xs text-gray-500 mt-1">اختر صورة (JPG, PNG, GIF)</p>
              </div>
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">مكتب الشحن (اختياري)</label>
            <input
              type="text"
              value={newDriver.shippingOffice}
              onChange={(e) => setNewDriver({...newDriver, shippingOffice: e.target.value})}
              className="w-full p-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-red-500 focus:border-red-500 transition-all duration-200"
              placeholder="أدخل اسم مكتب الشحن (اختياري)"
              dir="rtl"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">الملاحظات</label>
            <textarea
              value={newDriver.notes}
              onChange={(e) => setNewDriver({...newDriver, notes: e.target.value})}
              className="w-full p-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-red-500 focus:border-red-500 transition-all duration-200 resize-none"
              rows={3}
              placeholder="أدخل أي ملاحظات إضافية (اختياري)"
              dir="rtl"
            />
          </div>

          <div className="flex gap-3 pt-4">
            <button
              onClick={handleAddDriver}
              className="flex-1 bg-gradient-to-r from-red-500 to-red-600 text-white py-3 rounded-xl hover:from-red-600 hover:to-red-700 transition-all duration-300 flex items-center justify-center gap-2 shadow-lg"
            >
              <Save size={20} />
              حفظ
            </button>
            <button
              onClick={() => {
                setIsAddModalOpen(false);
                setNewDriver({
                  name: '',
                  phones: [''],
                  photo: '',
                  shippingOffice: '',
                  notes: ''
                });
              }}
              className="flex-1 bg-gray-500 text-white py-3 rounded-xl hover:bg-gray-600 transition-all duration-300"
            >
              إلغاء
            </button>
          </div>
        </div>
      </Modal>

      {/* Edit Driver Modal */}
      <Modal isOpen={isEditModalOpen} onClose={() => setIsEditModalOpen(false)} title="تعديل بيانات السائق">
        {editingDriver && (
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">اسم السائق</label>
              <input
                type="text"
                value={editingDriver.name}
                onChange={(e) => setEditingDriver({...editingDriver, name: e.target.value})}
                className="w-full p-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-red-500 focus:border-red-500 transition-all duration-200"
                dir="rtl"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">أرقام الهواتف</label>
              <div className="space-y-2">
                {editingDriver.phones.map((phone, index) => (
                  <div key={index} className="flex gap-2">
                    <input
                      type="tel"
                      value={phone}
                      onChange={(e) => updateEditPhoneField(index, e.target.value)}
                      className="flex-1 p-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-red-500 focus:border-red-500 transition-all duration-200"
                      placeholder={`رقم الهاتف ${index + 1}`}
                      dir="ltr"
                    />
                    {editingDriver.phones.length > 1 && (
                      <button
                        type="button"
                        onClick={() => removeEditPhoneField(index)}
                        className="px-3 py-2 bg-red-100 text-red-600 rounded-xl hover:bg-red-200 transition-colors"
                        title="حذف رقم الهاتف"
                      >
                        <X size={16} />
                      </button>
                    )}
                  </div>
                ))}
                <button
                  type="button"
                  onClick={addEditPhoneField}
                  className="w-full p-2 border-2 border-dashed border-gray-300 rounded-xl text-gray-500 hover:border-red-300 hover:text-red-500 transition-colors"
                >
                  + إضافة رقم هاتف آخر
                </button>
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">الصورة الشخصية</label>
              <div className="flex items-center gap-4">
                <div className="w-20 h-20 rounded-full overflow-hidden bg-gray-200 flex items-center justify-center">
                  {editingDriver.photo ? (
                    <img
                      src={editingDriver.photo}
                      alt="معاينة الصورة"
                      className="w-full h-full object-cover"
                    />
                  ) : (
                    <User size={24} className="text-gray-400" />
                  )}
                </div>
                <div className="flex-1">
                  <input
                    type="file"
                    accept="image/*"
                    onChange={handleEditPhotoUpload}
                    className="w-full p-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-red-500 focus:border-red-500 transition-all duration-200"
                  />
                  <p className="text-xs text-gray-500 mt-1">اختر صورة جديدة (JPG, PNG, GIF)</p>
                </div>
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">مكتب الشحن (اختياري)</label>
              <input
                type="text"
                value={editingDriver.shippingOffice || ''}
                onChange={(e) => setEditingDriver({...editingDriver, shippingOffice: e.target.value})}
                className="w-full p-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-red-500 focus:border-red-500 transition-all duration-200"
                placeholder="أدخل اسم مكتب الشحن (اختياري)"
                dir="rtl"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">الملاحظات</label>
              <textarea
                value={editingDriver.notes || ''}
                onChange={(e) => setEditingDriver({...editingDriver, notes: e.target.value})}
                className="w-full p-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-red-500 focus:border-red-500 transition-all duration-200"
                rows={3}
                dir="rtl"
              />
            </div>

            <div className="flex gap-3 pt-4">
              <button
                onClick={handleSaveEdit}
                className="flex-1 bg-gradient-to-r from-red-500 to-red-600 text-white py-3 rounded-xl hover:from-red-600 hover:to-red-700 transition-all duration-300 flex items-center justify-center gap-2 shadow-lg"
              >
                <Save size={20} />
                حفظ التعديل
              </button>
              <button
                onClick={() => setIsEditModalOpen(false)}
                className="flex-1 bg-gray-500 text-white py-3 rounded-xl hover:bg-gray-600 transition-all duration-300"
              >
                إلغاء
              </button>
            </div>
          </div>
        )}
      </Modal>

      {/* Report Modal */}
      <Modal isOpen={isReportModalOpen} onClose={() => setIsReportModalOpen(false)} title="تقرير السائقين">
        <div className="mb-4 flex gap-3">
          <button
            onClick={handlePrint}
            className="flex-1 bg-gradient-to-r from-red-500 to-red-600 text-white py-2 px-4 rounded-xl hover:from-red-600 hover:to-red-700 transition-all duration-300 flex items-center justify-center gap-2 shadow-lg"
          >
            <Printer size={18} />
            طباعة
          </button>
          <button
            onClick={handleExportReport}
            className="flex-1 bg-gradient-to-r from-blue-500 to-blue-600 text-white py-2 px-4 rounded-xl hover:from-blue-600 hover:to-blue-700 transition-all duration-300 flex items-center justify-center gap-2 shadow-lg"
          >
            <Download size={18} />
            تصدير التقرير
          </button>
        </div>

        {/* Report Summary */}
        <div className="bg-gray-50 p-4 rounded-xl mb-4">
          <h3 className="text-lg font-bold text-gray-800 mb-3 font-arabic">ملخص السائقين</h3>
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div className="flex justify-between">
              <span className="text-gray-600">إجمالي السائقين:</span>
              <span className="font-bold text-red-600">{totalDrivers}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">إجمالي أرقام الهواتف:</span>
              <span className="font-bold text-green-600">{totalPhones}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">لديهم صور:</span>
              <span className="font-bold text-blue-600">{driversWithPhotos}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">بدون صور:</span>
              <span className="font-bold text-gray-600">{driversWithoutPhotos}</span>
            </div>
          </div>
        </div>

        {/* Detailed List */}
        <div className="max-h-96 overflow-y-auto">
          <div className="space-y-3">
            {drivers.length === 0 ? (
              <div className="text-center py-8 text-gray-500">
                <Car size={48} className="mx-auto mb-4 text-gray-300" />
                <p>لا يوجد سائقين مسجلين</p>
              </div>
            ) : (
              drivers.map((driver) => (
                <div key={driver.id} className="p-4 rounded-xl border-r-4 bg-gray-50 border-red-500">
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center gap-3">
                      <div className="w-10 h-10 rounded-full overflow-hidden bg-gray-200 flex items-center justify-center">
                        {driver.photo ? (
                          <img
                            src={driver.photo}
                            alt={driver.name}
                            className="w-full h-full object-cover"
                          />
                        ) : (
                          <User size={16} className="text-gray-400" />
                        )}
                      </div>
                      <span className="font-medium text-gray-900">{driver.name}</span>
                    </div>
                    <span className="text-sm text-gray-500">{driver.joinDate}</span>
                  </div>
                  <div className="text-sm space-y-1">
                    <div>
                      <span className="font-medium text-gray-700">أرقام الهواتف:</span>
                      <div className="mt-1 space-y-1">
                        {driver.phones.map((phone, index) => (
                          <div key={index} className="text-gray-600 flex items-center gap-1">
                            <Phone size={12} />
                            {phone}
                          </div>
                        ))}
                      </div>
                    </div>
                    {driver.notes && (
                      <p className="text-gray-600">
                        <span className="font-medium">ملاحظات:</span> {driver.notes}
                      </p>
                    )}
                  </div>
                </div>
              ))
            )}
          </div>
        </div>
      </Modal>
    </div>
  );
};

export default DriversView;