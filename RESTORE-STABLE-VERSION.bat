@echo off
chcp 65001 >nul
title استعادة النسخة المستقرة - نظام إدارة مزرعة الخس

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    🔄 استعادة النسخة المستقرة                    ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo 📋 جاري استعادة النسخة المستقرة...
echo.

REM نسخ النسخة الاحتياطية
echo 📁 نسخ الملفات...
xcopy "%~dp0*" "e:\نسخة نهائية من البرنامج\المزرعة\" /E /I /H /Y /Q

if %errorlevel% neq 0 (
    echo ❌ فشل في نسخ الملفات!
    pause
    exit /b 1
)

echo ✅ تم نسخ الملفات بنجاح!
echo.

REM الانتقال للمجلد الرئيسي
cd /d "e:\نسخة نهائية من البرنامج\المزرعة"

echo 📦 تثبيت التبعيات...
call npm install

if %errorlevel% neq 0 (
    echo ❌ فشل في تثبيت التبعيات!
    echo 🔧 جرب حذف node_modules وإعادة التثبيت
    pause
    exit /b 1
)

echo ✅ تم تثبيت التبعيات بنجاح!
echo.

echo 🏗️ بناء المشروع...
call npm run build

if %errorlevel% neq 0 (
    echo ⚠️ تحذير: فشل البناء، لكن يمكن تشغيل وضع التطوير
)

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    ✅ تمت الاستعادة بنجاح!                     ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.
echo 🚀 النسخة المستقرة جاهزة للاستخدام!
echo.
echo 📋 الميزات المستعادة:
echo    ✅ تحكم المحابس يعمل
echo    ✅ فلاتر المراحل تعمل  
echo    ✅ نافذة اختيار الأسماء تعمل
echo    ✅ نظام الحذف آمن
echo    ✅ جميع التقارير تعمل
echo.

set /p choice="هل تريد تشغيل البرنامج الآن؟ (y/n): "
if /i "%choice%"=="y" (
    echo.
    echo 🚀 تشغيل البرنامج...
    start cmd /k "npm run dev"
)

echo.
echo 🎯 تم! يمكنك الآن استخدام النسخة المستقرة.
echo.
pause