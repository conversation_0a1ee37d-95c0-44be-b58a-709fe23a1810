import { useState, useEffect, useMemo, useCallback } from 'react';

interface SearchOptions {
  searchTerm: string;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
  filterBy?: Record<string, any>;
  page?: number;
  pageSize?: number;
}

interface SearchResult<T> {
  data: T[];
  totalCount: number;
  hasMore: boolean;
  currentPage: number;
  totalPages: number;
}

export const useSearch = <T extends Record<string, any>>(
  items: T[],
  searchFields: (keyof T)[],
  options: SearchOptions = { searchTerm: '', page: 1, pageSize: 10 }
): SearchResult<T> => {
  const [currentOptions, setCurrentOptions] = useState<SearchOptions>(options);

  // تحديث الخيارات
  const updateOptions = useCallback((newOptions: Partial<SearchOptions>) => {
    setCurrentOptions(prev => ({ ...prev, ...newOptions }));
  }, []);

  // البحث والفلترة
  const filteredAndSearchedItems = useMemo(() => {
    let result = [...items];

    // البحث في الحقول المحددة
    if (currentOptions.searchTerm) {
      const searchTerm = currentOptions.searchTerm.toLowerCase();
      result = result.filter(item => 
        searchFields.some(field => {
          const value = item[field];
          if (value == null) return false;
          return String(value).toLowerCase().includes(searchTerm);
        })
      );
    }

    // الفلترة حسب المعايير المحددة
    if (currentOptions.filterBy) {
      Object.entries(currentOptions.filterBy).forEach(([key, value]) => {
        if (value !== null && value !== undefined && value !== '') {
          result = result.filter(item => {
            if (Array.isArray(value)) {
              return value.includes(item[key]);
            }
            return item[key] === value;
          });
        }
      });
    }

    // الترتيب
    if (currentOptions.sortBy) {
      result.sort((a, b) => {
        const aValue = a[currentOptions.sortBy!];
        const bValue = b[currentOptions.sortBy!];
        
        let comparison = 0;
        if (aValue < bValue) comparison = -1;
        if (aValue > bValue) comparison = 1;
        
        return currentOptions.sortOrder === 'desc' ? -comparison : comparison;
      });
    }

    return result;
  }, [items, searchFields, currentOptions]);

  // التقسيم إلى صفحات (Pagination)
  const paginatedResult = useMemo(() => {
    const page = currentOptions.page || 1;
    const pageSize = currentOptions.pageSize || 10;
    const startIndex = (page - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    
    const data = filteredAndSearchedItems.slice(startIndex, endIndex);
    const totalCount = filteredAndSearchedItems.length;
    const totalPages = Math.ceil(totalCount / pageSize);
    const hasMore = page < totalPages;

    return {
      data,
      totalCount,
      hasMore,
      currentPage: page,
      totalPages
    };
  }, [filteredAndSearchedItems, currentOptions.page, currentOptions.pageSize]);

  return {
    ...paginatedResult,
    updateSearch: (searchTerm: string) => updateOptions({ searchTerm, page: 1 }),
    updateSort: (sortBy: string, sortOrder: 'asc' | 'desc' = 'asc') => 
      updateOptions({ sortBy, sortOrder, page: 1 }),
    updateFilter: (filterBy: Record<string, any>) => 
      updateOptions({ filterBy, page: 1 }),
    updatePage: (page: number) => updateOptions({ page }),
    updatePageSize: (pageSize: number) => updateOptions({ pageSize, page: 1 }),
    resetFilters: () => setCurrentOptions({ 
      searchTerm: '', 
      page: 1, 
      pageSize: currentOptions.pageSize || 10 
    })
  };
};

// Hook خاص للبحث في الوقت الفعلي مع debouncing
export const useDebouncedSearch = <T extends Record<string, any>>(
  items: T[],
  searchFields: (keyof T)[],
  debounceMs: number = 300
) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState('');

  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchTerm(searchTerm);
    }, debounceMs);

    return () => clearTimeout(timer);
  }, [searchTerm, debounceMs]);

  const searchResult = useSearch(items, searchFields, { 
    searchTerm: debouncedSearchTerm,
    pageSize: 20 // حجم صفحة أكبر للبحث
  });

  return {
    ...searchResult,
    searchTerm,
    setSearchTerm,
    isSearching: searchTerm !== debouncedSearchTerm
  };
};

// Hook للتحميل التدريجي (Lazy Loading)
export const useLazyLoading = <T>(
  loadMore: () => Promise<T[]>,
  initialPageSize: number = 20
) => {
  const [items, setItems] = useState<T[]>([]);
  const [loading, setLoading] = useState(false);
  const [hasMore, setHasMore] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const loadNextPage = useCallback(async () => {
    if (loading || !hasMore) return;

    try {
      setLoading(true);
      setError(null);
      
      const newItems = await loadMore();
      
      if (newItems.length === 0) {
        setHasMore(false);
      } else {
        setItems(prev => [...prev, ...newItems]);
        // إذا كان عدد العناصر المحملة أقل من حجم الصفحة، فلا يوجد المزيد
        if (newItems.length < initialPageSize) {
          setHasMore(false);
        }
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'حدث خطأ أثناء التحميل');
    } finally {
      setLoading(false);
    }
  }, [loadMore, loading, hasMore, initialPageSize]);

  const reset = useCallback(() => {
    setItems([]);
    setHasMore(true);
    setError(null);
  }, []);

  return {
    items,
    loading,
    hasMore,
    error,
    loadNextPage,
    reset
  };
};

// Hook للفلترة المتقدمة
export const useAdvancedFilter = <T extends Record<string, any>>(
  items: T[]
) => {
  const [filters, setFilters] = useState<Record<string, any>>({});
  const [dateRange, setDateRange] = useState<{ start?: string; end?: string }>({});

  const filteredItems = useMemo(() => {
    let result = [...items];

    // تطبيق الفلاتر العادية
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== null && value !== undefined && value !== '') {
        if (Array.isArray(value) && value.length > 0) {
          result = result.filter(item => value.includes(item[key]));
        } else if (!Array.isArray(value)) {
          result = result.filter(item => item[key] === value);
        }
      }
    });

    // تطبيق فلتر النطاق الزمني
    if (dateRange.start || dateRange.end) {
      result = result.filter(item => {
        // افتراض أن هناك حقل تاريخ (date أو createdAt)
        const itemDate = new Date(item.date || item.createdAt);
        const startDate = dateRange.start ? new Date(dateRange.start) : new Date('1900-01-01');
        const endDate = dateRange.end ? new Date(dateRange.end) : new Date();
        
        return itemDate >= startDate && itemDate <= endDate;
      });
    }

    return result;
  }, [items, filters, dateRange]);

  const addFilter = useCallback((key: string, value: any) => {
    setFilters(prev => ({ ...prev, [key]: value }));
  }, []);

  const removeFilter = useCallback((key: string) => {
    setFilters(prev => {
      const newFilters = { ...prev };
      delete newFilters[key];
      return newFilters;
    });
  }, []);

  const clearAllFilters = useCallback(() => {
    setFilters({});
    setDateRange({});
  }, []);

  const setDateRangeFilter = useCallback((start?: string, end?: string) => {
    setDateRange({ start, end });
  }, []);

  return {
    filteredItems,
    filters,
    dateRange,
    addFilter,
    removeFilter,
    clearAllFilters,
    setDateRangeFilter,
    hasActiveFilters: Object.keys(filters).length > 0 || 
                    dateRange.start !== undefined || 
                    dateRange.end !== undefined
  };
};