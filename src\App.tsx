import React, { useState } from 'react';
import DashboardView from './components/DashboardView';
import ZonesView from './components/ZonesView';
import SprayingView from './components/SprayingView';
import FertilizationView from './components/FertilizationView';
import InventoryView from './components/InventoryView';
import FarmMapView from './components/FarmMapView';
import EmployeesView from './components/EmployeesView';
import SuppliersView from './components/SuppliersView';
import AdvancesView from './components/AdvancesView';
import SalesView from './components/SalesView';
import ReportsView from './components/ReportsView';
import IrrigationScheduleView from './components/IrrigationScheduleView';
import ExpensesRevenuesView from './components/ExpensesRevenuesView';
import CloudSyncView from './components/CloudSyncView';
import { ToastContainer } from './components/Toast';
import ErrorBoundary from './components/ErrorBoundary';
import { useToast } from './hooks/useToast';
import { useDashboard } from './hooks/useDashboard';
import { useZones, useEmployees, useSuppliers, useAdvances, useSales, useInventory, useSprayings, useFertilizations } from './hooks/useDatabase';
import { useBackup } from './hooks/useBackup';
import { BarChart3, Map, Leaf, Droplets, FlaskConical, Package, Users, Truck, DollarSign, ShoppingCart, ClipboardList, Settings, TrendingUp, Cloud } from 'lucide-react';

function App() {
  const [currentView, setCurrentView] = useState('dashboard');
  
  // استخدام الـ Hooks الجديدة
  const toast = useToast();
  const { stats, loading: dashboardLoading } = useDashboard();
  const zonesHook = useZones();
  const employeesHook = useEmployees();
  const suppliersHook = useSuppliers();
  const advancesHook = useAdvances();
  const salesHook = useSales();
  const inventoryHook = useInventory();
  const sprayingsHook = useSprayings();
  const fertilizationsHook = useFertilizations();
  const backupHook = useBackup();

  // وظائف النسخ الاحتياطي المحسنة
  const handleBackup = async () => {
    const result = await backupHook.createBackup({ compress: false });
    if (result.success) {
      // نجح الإنشاء - سيظهر التوست تلقائياً من الـ Hook
    }
  };

  const handleImport = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    const result = await backupHook.restoreBackup(file);
    if (result.success) {
      // نجحت الاستعادة - سيتم إعادة تحميل الصفحة تلقائياً
    }
    
    // إعادة تعيين input
    event.target.value = '';
  };

  const menuItems = [
    { id: 'dashboard', name: 'لوحة التحكم', icon: BarChart3 },
    { id: 'farm-map', name: 'خريطة المزرعة', icon: Map },
    { id: 'zones', name: 'المراحل الزراعية', icon: Leaf },
    { id: 'irrigation-schedule', name: 'جدولة الري', icon: Droplets },
    { id: 'spraying', name: 'الرش', icon: Droplets },
    { id: 'fertilization', name: 'التسميد', icon: FlaskConical },
    { id: 'inventory', name: 'المستودع', icon: Package },
    { id: 'employees', name: 'الموظفين', icon: Users },
    { id: 'suppliers', name: 'الموردين', icon: Truck },
    { id: 'advances', name: 'السلف', icon: DollarSign },
    { id: 'sales', name: 'المبيعات', icon: ShoppingCart },
    { id: 'expenses-revenues', name: 'المصروفات والإيرادات', icon: TrendingUp },
    { id: 'reports', name: 'التقارير', icon: ClipboardList },
    { id: 'cloud-sync', name: 'التزامن السحابي', icon: Cloud },
    { id: 'settings', name: 'الإعدادات', icon: Settings }
  ];

  const renderView = () => {
    switch (currentView) {
      case 'irrigation-schedule':
        return <IrrigationScheduleView />;
      case 'dashboard':
        return <DashboardView stats={stats} />;
      case 'farm-map':
        return <FarmMapView zones={zonesHook.zones} onZoneUpdate={zonesHook.updateZone} />;
      case 'zones':
        return <ZonesView 
          zones={zonesHook.zones} 
          onZoneUpdate={zonesHook.updateZone}
          onZoneDelete={zonesHook.deleteZone}
          onZoneAdd={zonesHook.addZone}
        />;
      case 'spraying':
        return <SprayingView records={sprayingsHook.sprayings} />;
      case 'fertilization':
        return <FertilizationView records={fertilizationsHook.fertilizations} />;
      case 'inventory':
        return <InventoryView items={inventoryHook.inventory} />;
      case 'employees':
        return (
          <EmployeesView 
            employees={employeesHook.employees}
            onEmployeeUpdate={employeesHook.updateEmployee}
            onEmployeeAdd={employeesHook.addEmployee}
            onEmployeeDelete={employeesHook.deleteEmployee}
          />
        );
      case 'suppliers':
        return (
          <SuppliersView 
            suppliers={suppliersHook.suppliers}
            onSupplierUpdate={suppliersHook.updateSupplier}
            onSupplierAdd={suppliersHook.addSupplier}
            onSupplierDelete={suppliersHook.deleteSupplier}
          />
        );
      case 'advances':
        return (
          <AdvancesView 
            advances={advancesHook.advances}
            employees={employeesHook.employees}
            onAdvanceUpdate={advancesHook.updateAdvance}
            onAdvanceAdd={advancesHook.addAdvance}
            onAdvanceDelete={advancesHook.deleteAdvance}
          />
        );
      case 'sales':
        return (
          <SalesView 
            sales={salesHook.sales}
            onSaleUpdate={salesHook.updateSale}
            onSaleAdd={salesHook.addSale}
            onSaleDelete={salesHook.deleteSale}
          />
        );
      case 'expenses-revenues':
        return <ExpensesRevenuesView />;
      case 'reports':
        return (
          <ReportsView 
            zones={zonesHook.zones}
            employees={employeesHook.employees}
            suppliers={suppliersHook.suppliers}
            advances={advancesHook.advances}
            sales={salesHook.sales}
            sprayingRecords={sprayingsHook.sprayings}
            fertilizationRecords={fertilizationsHook.fertilizations}
            inventoryItems={inventoryHook.inventory}
          />
        );
      case 'cloud-sync':
        return <CloudSyncView />;
      default:
        return <DashboardView stats={stats} />;
    }
  };

  // عرض شاشة التحميل فقط إذا كانت جميع البيانات الأساسية تتحمل
  const isLoading = dashboardLoading || zonesHook.loading || employeesHook.loading;
  
  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-screen bg-gradient-to-br from-green-50 to-blue-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-green-600 mx-auto mb-4"></div>
          <p className="text-gray-600">جاري تحميل البيانات...</p>
        </div>
      </div>
    );
  }

  return (
    <ErrorBoundary>
      <div className="flex h-screen bg-gray-100">
      {/* Sidebar */}
      <div className="w-72 bg-gradient-to-b from-emerald-700 to-blue-800 text-white shadow-2xl flex flex-col">
        <div className="p-8 flex flex-col items-center justify-center">
          <div className="w-16 h-16 rounded-2xl bg-white/20 flex items-center justify-center mb-3 shadow-lg">
            <Leaf className="w-10 h-10 text-white" />
          </div>
          <h1 className="text-2xl font-extrabold text-center mb-1 tracking-wide">شركة الشفق</h1>
          <p className="text-sm text-center text-emerald-100">للزراعة الحديثة</p>
        </div>
        <nav className="flex-1 mt-4 px-2 space-y-1">
          {menuItems.map((item) => {
            const Icon = item.icon;
            const active = currentView === item.id;
            return (
              <button
                key={item.id}
                onClick={() => setCurrentView(item.id)}
                className={`group w-full flex items-center gap-3 px-5 py-3 my-1 rounded-xl transition-all duration-200 font-bold text-lg hover:bg-emerald-600/80 hover:shadow-lg ${active ? 'bg-white text-emerald-700 shadow-xl border-r-8 border-emerald-400' : 'bg-emerald-800/0 text-white'}`}
              >
                <Icon className={`w-6 h-6 ${active ? 'text-emerald-600' : 'text-white/80'} transition-all`} />
                <span className="flex-1 text-right">{item.name}</span>
              </button>
            );
          })}
        </nav>
        {/* Backup/Import Section */}
        <div className="mt-8 mb-4 px-4">
          <div className="bg-white/10 rounded-2xl p-4 shadow-inner flex flex-col items-center">
            <h3 className="text-sm font-semibold mb-3 text-emerald-100">النسخ الاحتياطي</h3>
            <button
              onClick={handleBackup}
              className="w-full bg-emerald-600 hover:bg-emerald-700 text-white text-xs py-2 px-3 rounded-lg font-bold transition-colors mb-2"
            >
              إنشاء نسخة احتياطية
            </button>
            <label className="block w-full">
              <input
                type="file"
                accept=".json"
                onChange={handleImport}
                className="hidden"
              />
              <span className="w-full bg-blue-600 hover:bg-blue-700 text-white text-xs py-2 px-3 rounded-lg font-bold transition-colors cursor-pointer block text-center">
                استيراد بيانات
              </span>
            </label>
          </div>
        </div>
      </div>
      {/* Main Content */}
      <div className="flex-1 overflow-auto">
        {renderView()}
      </div>
      
      {/* Toast Notifications */}
      <ToastContainer 
        toasts={toast.toasts} 
        onRemove={toast.removeToast} 
      />
      </div>
    </ErrorBoundary>
  );
}

export default App;