// مكتبة مساعدة لمعالجة النصوص العربية في PDF
import { jsPDF } from 'jspdf';

// دالة لإنشاء PDF من HTML مع دعم العربية الكامل
export const createPDFFromHTML = async (htmlContent, filename = 'report.pdf') => {
  try {
    console.log('🔄 بدء إنشاء PDF...');
    console.log('📄 محتوى HTML:', htmlContent.substring(0, 200) + '...');

    // استيراد html2pdf ديناميكياً
    const html2pdf = await import('html2pdf.js');
    console.log('✅ تم تحميل مكتبة html2pdf');

    // إعدادات PDF محسنة مع دعم العربية
    const options = {
      margin: [0.5, 0.5, 0.5, 0.5],
      filename: filename,
      image: {
        type: 'jpeg',
        quality: 1.0
      },
      html2canvas: {
        scale: 3,
        useCORS: true,
        allowTaint: true,
        letterRendering: true,
        logging: true,
        width: 794,
        height: 1123
      },
      jsPDF: {
        unit: 'pt',
        format: 'a4',
        orientation: 'portrait',
        compress: false
      },
      pagebreak: { mode: ['avoid-all', 'css', 'legacy'] }
    };

    // إنشاء عنصر HTML مؤقت
    const element = document.createElement('div');
    element.innerHTML = htmlContent;

    // تطبيق الأنماط المطلوبة
    element.style.direction = 'rtl';
    element.style.fontFamily = 'Cairo, Tajawal, Noto Sans Arabic, Arial, sans-serif';
    element.style.fontSize = '14px';
    element.style.lineHeight = '1.6';
    element.style.width = '794px';
    element.style.minHeight = '1000px';
    element.style.padding = '20px';
    element.style.backgroundColor = 'white';
    element.style.color = '#333';

    // إضافة العنصر للصفحة مؤقتاً (مرئي للتأكد من المحتوى)
    element.style.position = 'fixed';
    element.style.left = '-2000px';
    element.style.top = '0px';
    element.style.zIndex = '-1000';
    document.body.appendChild(element);

    console.log('📝 تم إنشاء العنصر HTML:', element.offsetHeight, 'px');

    // انتظار قصير للتأكد من تحميل الخطوط
    await new Promise(resolve => setTimeout(resolve, 1000));

    // إنشاء PDF
    console.log('🔄 بدء تحويل HTML إلى PDF...');
    await html2pdf.default().set(options).from(element).save();
    console.log('✅ تم إنشاء PDF بنجاح');

    // إزالة العنصر المؤقت
    document.body.removeChild(element);

    return true;
  } catch (error) {
    console.error('❌ خطأ في إنشاء PDF:', error);

    // في حالة فشل html2pdf، استخدم الطريقة التقليدية
    console.log('🔄 محاولة استخدام الطريقة التقليدية...');
    return await createPDFTraditional(htmlContent, filename);
  }
};

// دالة احتياطية لإنشاء PDF بالطريقة التقليدية
const createPDFTraditional = async (htmlContent, filename) => {
  try {
    const { jsPDF } = await import('jspdf');
    const pdf = new jsPDF('p', 'pt', 'a4');

    // إضافة النص بشكل بسيط
    pdf.setFont('helvetica');
    pdf.setFontSize(16);
    pdf.text('تقرير شركة الشفق للزراعة الحديثة', 400, 50, { align: 'center' });

    pdf.setFontSize(12);
    pdf.text('تم إنشاء هذا التقرير بالطريقة التقليدية', 400, 100, { align: 'center' });
    pdf.text('نظراً لمشكلة في تحويل HTML', 400, 120, { align: 'center' });

    // إضافة التاريخ
    const currentDate = new Date().toLocaleDateString('ar-SA');
    pdf.text(`التاريخ: ${currentDate}`, 500, 160, { align: 'right' });

    pdf.save(filename);
    return true;
  } catch (error) {
    console.error('❌ خطأ في الطريقة التقليدية:', error);
    throw error;
  }
};

// دالة لمعالجة النص العربي وتحسين عرضه في PDF
export const processArabicText = (text) => {
  if (!text) return '';
  
  // تحويل النص إلى string
  let processedText = text.toString();
  
  // إزالة التشكيل
  processedText = processedText.replace(/[\u064B-\u0652]/g, '');
  
  // تحويل الأرقام العربية إلى إنجليزية
  const arabicNumbers = '٠١٢٣٤٥٦٧٨٩';
  const englishNumbers = '0123456789';
  
  for (let i = 0; i < arabicNumbers.length; i++) {
    processedText = processedText.replace(new RegExp(arabicNumbers[i], 'g'), englishNumbers[i]);
  }
  
  return processedText;
};

// دالة لإعداد PDF مع دعم اللغة العربية
export const setupArabicPDF = (pdf) => {
  // تعيين اتجاه النص من اليمين إلى اليسار
  pdf.setR2L(true);
  
  // تعيين خط يدعم العربية (استخدام خط النظام)
  pdf.setFont('helvetica');
  
  return pdf;
};

// دالة لإضافة نص عربي إلى PDF
export const addArabicText = (pdf, text, x, y, options = {}) => {
  const processedText = processArabicText(text);
  
  // إعدادات افتراضية
  const defaultOptions = {
    align: 'right',
    fontSize: 12,
    fontStyle: 'normal'
  };
  
  const finalOptions = { ...defaultOptions, ...options };
  
  // تطبيق الإعدادات
  pdf.setFontSize(finalOptions.fontSize);
  
  // إضافة النص
  pdf.text(processedText, x, y, {
    align: finalOptions.align,
    ...finalOptions
  });
  
  return pdf;
};

// دالة لإنشاء جدول مع نصوص عربية
export const createArabicTable = (pdf, headers, data, startY = 60) => {
  // معالجة العناوين
  const processedHeaders = headers.map(header => processArabicText(header));
  
  // معالجة البيانات
  const processedData = data.map(row => 
    row.map(cell => processArabicText(cell))
  );
  
  // إنشاء الجدول
  pdf.autoTable({
    head: [processedHeaders],
    body: processedData,
    startY: startY,
    styles: {
      font: 'helvetica',
      fontSize: 10,
      cellPadding: 3,
      overflow: 'linebreak',
      halign: 'center',
      valign: 'middle'
    },
    headStyles: {
      fillColor: [34, 197, 94],
      textColor: [255, 255, 255],
      fontSize: 11,
      fontStyle: 'bold',
      halign: 'center'
    },
    alternateRowStyles: {
      fillColor: [249, 250, 251]
    },
    columnStyles: {
      // يمكن تخصيص أعمدة محددة هنا
    },
    margin: { left: 20, right: 20 },
    tableWidth: 'auto',
    theme: 'grid'
  });
  
  return pdf;
};

// دالة لإضافة رأس عربي للتقرير
export const addArabicHeader = (pdf, title, subtitle = '', companyName = '') => {
  // العنوان الرئيسي
  pdf.setFontSize(20);
  pdf.setTextColor(21, 128, 61);
  addArabicText(pdf, title, 105, 20, { align: 'center', fontSize: 20 });
  
  // العنوان الفرعي
  if (subtitle) {
    pdf.setFontSize(16);
    pdf.setTextColor(34, 197, 94);
    addArabicText(pdf, subtitle, 105, 35, { align: 'center', fontSize: 16 });
  }
  
  // اسم الشركة
  if (companyName) {
    pdf.setFontSize(14);
    pdf.setTextColor(107, 114, 128);
    addArabicText(pdf, companyName, 105, 50, { align: 'center', fontSize: 14 });
  }
  
  // خط فاصل
  pdf.setDrawColor(34, 197, 94);
  pdf.setLineWidth(1);
  pdf.line(20, 60, 190, 60);
  
  return pdf;
};

// دالة لإضافة تذييل عربي
export const addArabicFooter = (pdf, pageHeight = 297) => {
  const yPosition = pageHeight - 30;
  
  // خط فاصل
  pdf.setDrawColor(34, 197, 94);
  pdf.line(20, yPosition - 10, 190, yPosition - 10);
  
  // نص التذييل
  pdf.setFontSize(8);
  pdf.setTextColor(107, 114, 128);
  
  addArabicText(pdf, 'تم إنشاؤه بواسطة نظام إدارة المخزون', 105, yPosition, { 
    align: 'center', 
    fontSize: 8 
  });
  
  addArabicText(pdf, 'شركة الشفق للزراعة الحديثة', 105, yPosition + 8, { 
    align: 'center', 
    fontSize: 8 
  });
  
  addArabicText(pdf, '"نحو زراعة مستدامة ومستقبل أخضر"', 105, yPosition + 16, { 
    align: 'center', 
    fontSize: 8 
  });
  
  return pdf;
};

// دالة لتنسيق التاريخ والوقت بالعربية (ميلادي)
export const formatArabicDateTime = (date = new Date()) => {
  const arabicMonths = [
    'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
    'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
  ];

  const day = date.getDate();
  const month = arabicMonths[date.getMonth()];
  const year = date.getFullYear();

  // تنسيق الوقت يدوياً
  const hours = date.getHours();
  const minutes = date.getMinutes();
  const ampm = hours >= 12 ? 'PM' : 'AM';
  const displayHours = hours % 12 || 12;
  const time = `${displayHours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')} ${ampm}`;

  return {
    date: `${day} ${month} ${year}`,
    time: time,
    full: `${day} ${month} ${year} - ${time}`
  };
};

// دالة لإنشاء PDF كامل مع دعم العربية
export const createArabicPDF = (title, data, options = {}) => {
  // إنشاء PDF جديد
  const pdf = new jsPDF('p', 'mm', 'a4');
  
  // إعداد PDF للعربية
  setupArabicPDF(pdf);
  
  // إضافة الرأس
  addArabicHeader(pdf, title, options.subtitle, options.companyName);
  
  // إضافة معلومات التاريخ
  const dateTime = formatArabicDateTime();
  pdf.setFontSize(10);
  pdf.setTextColor(107, 114, 128);
  
  addArabicText(pdf, `تاريخ التصدير: ${dateTime.date}`, 190, 75, { 
    align: 'right', 
    fontSize: 10 
  });
  
  addArabicText(pdf, `وقت التصدير: ${dateTime.time}`, 190, 85, { 
    align: 'right', 
    fontSize: 10 
  });
  
  // إضافة البيانات إذا كانت موجودة
  if (data && data.headers && data.rows) {
    createArabicTable(pdf, data.headers, data.rows, 100);
  }
  
  // إضافة التذييل
  addArabicFooter(pdf);
  
  return pdf;
};

// دالة لإنشاء HTML لتقرير المخزون
export const createInventoryReportHTML = (movements, title = 'تقرير حركات المخزون') => {
  console.log('📊 إنشاء تقرير المخزون...');
  console.log('📦 عدد الحركات:', movements ? movements.length : 0);

  const currentDate = new Date().toLocaleDateString('ar-SA');
  const currentTime = new Date().toLocaleTimeString('ar-SA');

  // التحقق من وجود بيانات
  if (!movements || movements.length === 0) {
    console.log('⚠️ لا توجد حركات مخزون');
    const movementsHTML = `
      <tr>
        <td colspan="6" style="text-align: center; padding: 40px; color: #6b7280;">
          لا توجد حركات مخزون مسجلة
        </td>
      </tr>
    `;

    return createReportHTML(title, currentDate, currentTime, movements || [], movementsHTML, 'inventory');
  }

  const movementsHTML = movements.map((movement, index) => {
    console.log(`📝 حركة ${index + 1}:`, movement);
    return `
      <tr>
        <td>${movement.type === 'in' ? 'إدخال' : 'إخراج'}</td>
        <td>${movement.itemName || 'غير محدد'}</td>
        <td>${movement.quantity || 0}</td>
        <td>${movement.notes || 'لا توجد ملاحظات'}</td>
        <td>${movement.date || 'غير محدد'}</td>
        <td>${movement.time || 'غير محدد'}</td>
      </tr>
    `;
  }).join('');

  return createReportHTML(title, currentDate, currentTime, movements, movementsHTML, 'inventory');
};

// دالة مساعدة لإنشاء HTML موحد
const createReportHTML = (title, currentDate, currentTime, data, contentHTML, type = 'inventory') => {
  const isInventory = type === 'inventory';
  const primaryColor = isInventory ? '#22c55e' : '#3b82f6';
  const darkColor = isInventory ? '#15803d' : '#1e40af';

  const summaryContent = isInventory ? `
    <h3>ملخص التقرير</h3>
    <p>إجمالي الحركات: ${data.length}</p>
    <p>حركات الإدخال: ${data.filter(m => m.type === 'in').length}</p>
    <p>حركات الإخراج: ${data.filter(m => m.type === 'out').length}</p>
  ` : `
    <h3>الملخص المالي</h3>
    <p>إجمالي المعاملات: ${data.length}</p>
    <p>الإيرادات: ${data.filter(t => t.type === 'income').length}</p>
    <p>المصروفات: ${data.filter(t => t.type === 'expense').length}</p>
  `;

  const tableHeaders = isInventory ? `
    <th>نوع الحركة</th>
    <th>اسم الصنف</th>
    <th>الكمية</th>
    <th>الملاحظات</th>
    <th>التاريخ</th>
    <th>الوقت</th>
  ` : `
    <th>نوع المعاملة</th>
    <th>المبلغ</th>
    <th>الوصف</th>
    <th>التصنيف</th>
    <th>التاريخ</th>
  `;

  return `
    <!DOCTYPE html>
    <html lang="ar" dir="rtl">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <style>
        @import url('https://fonts.googleapis.com/css2?family=Cairo:wght@400;600;700&display=swap');
        * {
          margin: 0;
          padding: 0;
          box-sizing: border-box;
        }
        body {
          font-family: 'Cairo', 'Tajawal', 'Noto Sans Arabic', Arial, sans-serif !important;
          direction: rtl;
          text-align: right;
          line-height: 1.6;
          color: #333;
          background: white;
          padding: 30px;
          font-size: 14px;
        }
        .header {
          text-align: center;
          margin-bottom: 40px;
          border-bottom: 3px solid ${primaryColor};
          padding-bottom: 25px;
        }
        .company-name {
          font-size: 28px;
          font-weight: 700;
          color: ${darkColor};
          margin-bottom: 15px;
        }
        .report-title {
          font-size: 22px;
          font-weight: 600;
          color: ${primaryColor};
          margin-bottom: 20px;
        }
        .report-info {
          font-size: 16px;
          color: #6b7280;
        }
        .summary {
          background: ${isInventory ? '#f0fdf4' : '#eff6ff'};
          padding: 20px;
          border-radius: 10px;
          margin-bottom: 30px;
          border-right: 5px solid ${primaryColor};
        }
        .summary h3 {
          color: ${darkColor};
          margin-bottom: 15px;
          font-size: 18px;
        }
        .summary p {
          margin-bottom: 8px;
          font-size: 14px;
        }
        table {
          width: 100%;
          border-collapse: collapse;
          margin-bottom: 40px;
          font-size: 13px;
          box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        th, td {
          border: 1px solid #d1d5db;
          padding: 12px 8px;
          text-align: center;
        }
        th {
          background: ${primaryColor};
          color: white;
          font-weight: 600;
          font-size: 14px;
        }
        tr:nth-child(even) {
          background: #f9fafb;
        }
        tr:hover {
          background: #f3f4f6;
        }
        .footer {
          text-align: center;
          margin-top: 50px;
          padding-top: 25px;
          border-top: 2px solid ${primaryColor};
          font-size: 13px;
          color: #6b7280;
        }
        .footer p {
          margin-bottom: 8px;
        }
        .no-data {
          text-align: center;
          padding: 60px 20px;
          color: #9ca3af;
          font-style: italic;
          font-size: 16px;
        }
      </style>
    </head>
    <body>
      <div class="header">
        <div class="company-name">شركة الشفق للزراعة الحديثة</div>
        <div class="report-title">${title}</div>
        <div class="report-info">
          التاريخ: ${currentDate} | الوقت: ${currentTime}
        </div>
      </div>

      <div class="summary">
        ${summaryContent}
      </div>

      <table>
        <thead>
          <tr>
            ${tableHeaders}
          </tr>
        </thead>
        <tbody>
          ${contentHTML}
        </tbody>
      </table>

      <div class="footer">
        <p>تم إنشاؤه بواسطة نظام إدارة ${isInventory ? 'المخزون' : 'الأموال'}</p>
        <p>شركة الشفق للزراعة الحديثة</p>
        <p>"نحو زراعة مستدامة ومستقبل أخضر"</p>
      </div>
    </body>
    </html>
  `;
};

// دالة لإنشاء HTML للتقرير المالي
export const createFinancialReportHTML = (transactions, title = 'التقرير المالي') => {
  console.log('💰 إنشاء التقرير المالي...');
  console.log('💳 عدد المعاملات:', transactions ? transactions.length : 0);

  const currentDate = new Date().toLocaleDateString('ar-SA');
  const currentTime = new Date().toLocaleTimeString('ar-SA');

  // التحقق من وجود بيانات
  if (!transactions || transactions.length === 0) {
    console.log('⚠️ لا توجد معاملات مالية');
    const transactionsHTML = `
      <tr>
        <td colspan="5" style="text-align: center; padding: 40px; color: #6b7280;">
          لا توجد معاملات مالية مسجلة
        </td>
      </tr>
    `;

    return createReportHTML(title, currentDate, currentTime, transactions || [], transactionsHTML, 'financial');
  }

  const totalIncome = transactions.filter(t => t.type === 'income').reduce((sum, t) => sum + (t.amount || 0), 0);
  const totalExpense = transactions.filter(t => t.type === 'expense').reduce((sum, t) => sum + (t.amount || 0), 0);
  const balance = totalIncome - totalExpense;

  console.log('💚 إجمالي الإيرادات:', totalIncome);
  console.log('💸 إجمالي المصروفات:', totalExpense);
  console.log('💎 الرصيد:', balance);

  const transactionsHTML = transactions.map((transaction, index) => {
    console.log(`💳 معاملة ${index + 1}:`, transaction);
    return `
      <tr>
        <td>${transaction.type === 'income' ? 'إيراد' : 'مصروف'}</td>
        <td>${(transaction.amount || 0).toLocaleString()} دينار أردني</td>
        <td>${transaction.description || 'بدون وصف'}</td>
        <td>${transaction.category || 'غير مصنف'}</td>
        <td>${transaction.date || 'غير محدد'}</td>
      </tr>
    `;
  }).join('');

  return createReportHTML(title, currentDate, currentTime, transactions, transactionsHTML, 'financial');
};

// تصدير جميع الدوال
export default {
  processArabicText,
  setupArabicPDF,
  addArabicText,
  createArabicTable,
  addArabicHeader,
  addArabicFooter,
  formatArabicDateTime,
  createArabicPDF,
  createPDFFromHTML,
  createInventoryReportHTML,
  createFinancialReportHTML
};
