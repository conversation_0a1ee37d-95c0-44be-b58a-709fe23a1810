import React, { useState, useEffect } from 'react';
import { Phone, Plus, Edit, Trash2, Search, User, Mail, MapPin, Save, X, Home } from 'lucide-react';

interface Contact {
  id: string;
  name: string;
  phone: string;
  email: string;
  company: string;
  address: string;
  notes: string;
}

interface ContactsViewProps {
  setCurrentView?: (view: string) => void;
}

const ContactsView: React.FC<ContactsViewProps> = ({ setCurrentView }) => {
  const [contacts, setContacts] = useState<Contact[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [showAddForm, setShowAddForm] = useState(false);
  const [editingId, setEditingId] = useState<string | null>(null);
  
  // نموذج جهة اتصال فارغة
  const emptyContact: Contact = {
    id: '',
    name: '',
    phone: '',
    email: '',
    company: '',
    address: '',
    notes: ''
  };

  const [formData, setFormData] = useState<Contact>(emptyContact);

  // تحميل البيانات عند بدء التشغيل
  useEffect(() => {
    loadContacts();
  }, []);

  const loadContacts = () => {
    try {
      const saved = localStorage.getItem('contacts');
      if (saved) {
        setContacts(JSON.parse(saved));
      }
    } catch (error) {
      console.error('خطأ في تحميل جهات الاتصال:', error);
    }
  };

  const saveContacts = (contactsList: Contact[]) => {
    try {
      localStorage.setItem('contacts', JSON.stringify(contactsList));
      setContacts(contactsList);
    } catch (error) {
      console.error('خطأ في حفظ جهات الاتصال:', error);
    }
  };

  const handleInputChange = (field: keyof Contact, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.name.trim() || !formData.phone.trim()) {
      alert('يرجى إدخال الاسم ورقم الهاتف على الأقل');
      return;
    }

    const newContact: Contact = {
      ...formData,
      id: editingId || Date.now().toString(),
      name: formData.name.trim(),
      phone: formData.phone.trim(),
      email: formData.email.trim(),
      company: formData.company.trim(),
      address: formData.address.trim(),
      notes: formData.notes.trim()
    };

    let updatedContacts: Contact[];
    
    if (editingId) {
      // تعديل جهة اتصال موجودة
      updatedContacts = contacts.map(contact => 
        contact.id === editingId ? newContact : contact
      );
    } else {
      // إضافة جهة اتصال جديدة
      updatedContacts = [...contacts, newContact];
    }

    saveContacts(updatedContacts);
    resetForm();
    alert(editingId ? 'تم تحديث جهة الاتصال بنجاح!' : 'تم إضافة جهة الاتصال بنجاح!');
  };

  const resetForm = () => {
    setFormData(emptyContact);
    setShowAddForm(false);
    setEditingId(null);
  };

  const handleEdit = (contact: Contact) => {
    setFormData(contact);
    setEditingId(contact.id);
    setShowAddForm(true);
  };

  const handleDelete = (id: string) => {
    if (window.confirm('هل أنت متأكد من حذف جهة الاتصال هذه؟')) {
      const updatedContacts = contacts.filter(contact => contact.id !== id);
      saveContacts(updatedContacts);
      alert('تم حذف جهة الاتصال بنجاح!');
    }
  };

  const filteredContacts = contacts.filter(contact =>
    contact.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    contact.phone.includes(searchTerm) ||
    contact.company.toLowerCase().includes(searchTerm.toLowerCase()) ||
    contact.email.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 p-6">
      <div className="max-w-6xl mx-auto">
        {/* Header */}
        <div className="bg-white rounded-3xl shadow-2xl p-6 mb-6 border-2 border-indigo-100">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-2xl flex items-center justify-center">
                <Phone size={24} className="text-white" />
              </div>
              <div>
                <h1 className="text-2xl font-bold text-indigo-800 font-arabic drop-shadow">جهات الاتصال</h1>
                <p className="text-blue-700 text-sm">إدارة جهات الاتصال والعملاء</p>
              </div>
            </div>
            <div className="flex items-center gap-4">
              {setCurrentView && (
                <button
                  onClick={() => setCurrentView('home')}
                  className="bg-gradient-to-r from-indigo-500 to-blue-600 text-white px-4 py-3 rounded-xl hover:from-blue-600 hover:to-indigo-700 transition-all duration-300 flex items-center gap-2 shadow-xl hover:shadow-2xl transform hover:scale-105"
                >
                  <Home size={20} />
                  <span className="hidden sm:inline">الرئيسية</span>
                </button>
              )}
              <button
                onClick={() => setShowAddForm(true)}
                className="bg-gradient-to-r from-blue-500 to-indigo-600 text-white px-6 py-3 rounded-xl hover:from-indigo-600 hover:to-blue-700 transition-all duration-300 flex items-center gap-2 shadow-xl hover:shadow-2xl transform hover:scale-105 font-arabic font-medium tracking-wide"
              >
                <Plus size={20} />
                إضافة جهة اتصال
              </button>
            </div>
          </div>
        </div>

        {/* Search */}
        <div className="bg-white rounded-2xl shadow-xl p-6 mb-6 border-2 border-indigo-100">
          <div className="relative">
            <Search size={20} className="absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400" />
            <input
              type="text"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              placeholder="البحث في جهات الاتصال..."
              className="w-full pr-12 pl-4 py-3 border-2 border-indigo-100 rounded-xl focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-all duration-300"
              dir="rtl"
            />
          </div>
        </div>

        {/* Add/Edit Form */}
        {showAddForm && (
          <div className="bg-white rounded-2xl shadow-xl p-6 mb-6 border-2 border-indigo-100">
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-xl font-bold text-indigo-800">
                {editingId ? 'تعديل جهة الاتصال' : 'إضافة جهة اتصال جديدة'}
              </h2>
              <button
                onClick={resetForm}
                className="text-indigo-400 hover:text-indigo-700 transition-colors"
              >
                <X size={24} />
              </button>
            </div>
            
            <form onSubmit={handleSubmit} className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-semibold text-gray-700 mb-2">الاسم *</label>
                  <input
                    type="text"
                    value={formData.name}
                    onChange={(e) => handleInputChange('name', e.target.value)}
                    className="w-full p-3 border-2 border-indigo-100 rounded-xl focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-all duration-200"
                    placeholder="أدخل الاسم"
                    dir="rtl"
                    required
                  />
                </div>
                <div>
                  <label className="block text-sm font-semibold text-gray-700 mb-2">رقم الهاتف *</label>
                  <input
                    type="tel"
                    value={formData.phone}
                    onChange={(e) => handleInputChange('phone', e.target.value)}
                    className="w-full p-3 border-2 border-indigo-100 rounded-xl focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-all duration-200"
                    placeholder="أدخل رقم الهاتف"
                    dir="ltr"
                    required
                  />
                </div>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-semibold text-gray-700 mb-2">البريد الإلكتروني</label>
                  <input
                    type="email"
                    value={formData.email}
                    onChange={(e) => handleInputChange('email', e.target.value)}
                    className="w-full p-3 border-2 border-indigo-100 rounded-xl focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-all duration-200"
                    placeholder="أدخل البريد الإلكتروني"
                    dir="ltr"
                  />
                </div>
                <div>
                  <label className="block text-sm font-semibold text-gray-700 mb-2">الشركة</label>
                  <input
                    type="text"
                    value={formData.company}
                    onChange={(e) => handleInputChange('company', e.target.value)}
                    className="w-full p-3 border-2 border-indigo-100 rounded-xl focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-all duration-200"
                    placeholder="أدخل اسم الشركة"
                    dir="rtl"
                  />
                </div>
              </div>
              
              <div>
                <label className="block text-sm font-semibold text-gray-700 mb-2">العنوان</label>
                <input
                  type="text"
                  value={formData.address}
                  onChange={(e) => handleInputChange('address', e.target.value)}
                  className="w-full p-3 border-2 border-indigo-100 rounded-xl focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-all duration-200"
                  placeholder="أدخل العنوان"
                  dir="rtl"
                />
              </div>
              
              <div>
                <label className="block text-sm font-semibold text-gray-700 mb-2">ملاحظات</label>
                <textarea
                  value={formData.notes}
                  onChange={(e) => handleInputChange('notes', e.target.value)}
                  className="w-full p-3 border-2 border-indigo-100 rounded-xl focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-all duration-200"
                  placeholder="أدخل أي ملاحظات إضافية"
                  rows={3}
                  dir="rtl"
                />
              </div>
              
              <div className="flex gap-4 pt-4">
                <button
                  type="submit"
                  className="flex-1 bg-gradient-to-r from-green-600 to-green-700 text-white py-3 px-6 rounded-xl hover:from-green-700 hover:to-green-800 transition-all duration-300 flex items-center justify-center gap-2"
                >
                  <Save size={20} />
                  {editingId ? 'حفظ التعديلات' : 'حفظ جهة الاتصال'}
                </button>
                <button
                  type="button"
                  onClick={resetForm}
                  className="flex-1 bg-gradient-to-r from-gray-500 to-gray-600 text-white py-3 px-6 rounded-xl hover:from-gray-600 hover:to-gray-700 transition-all duration-300 flex items-center justify-center gap-2"
                >
                  <X size={20} />
                  إلغاء
                </button>
              </div>
            </form>
          </div>
        )}

        {/* Contacts List */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredContacts.length === 0 ? (
            <div className="col-span-full bg-white rounded-2xl shadow-lg p-12 text-center">
              <Phone size={64} className="mx-auto mb-4 text-gray-300" />
              <h3 className="text-xl font-bold text-gray-600 mb-2">
                {searchTerm ? 'لا توجد نتائج' : 'لا توجد جهات اتصال'}
              </h3>
              <p className="text-gray-500">
                {searchTerm ? 'جرب البحث بكلمات مختلفة' : 'ابدأ بإضافة جهة اتصال جديدة'}
              </p>
            </div>
          ) : (
            filteredContacts.map((contact) => (
              <div key={contact.id} className="bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden border-2 border-indigo-100">
                <div className="bg-gradient-to-r from-blue-500 to-indigo-600 text-white p-4">
                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 bg-white/20 rounded-full flex items-center justify-center">
                      <User size={20} className="text-white" />
                    </div>
                    <div className="flex-1">
                      <h3 className="font-bold text-lg">{contact.name}</h3>
                      {contact.company && (
                        <p className="text-blue-100 text-sm">{contact.company}</p>
                      )}
                    </div>
                  </div>
                </div>

                <div className="p-4 space-y-3">
                  <div className="flex items-center gap-3">
                    <Phone size={16} className="text-green-500" />
                    <span className="text-gray-700" dir="ltr">{contact.phone}</span>
                  </div>

                  {contact.email && (
                    <div className="flex items-center gap-3">
                      <Mail size={16} className="text-blue-500" />
                      <span className="text-gray-700 text-sm">{contact.email}</span>
                    </div>
                  )}

                  {contact.address && (
                    <div className="flex items-center gap-3">
                      <MapPin size={16} className="text-red-500" />
                      <span className="text-gray-700 text-sm">{contact.address}</span>
                    </div>
                  )}

                  {contact.notes && (
                    <div className="bg-gray-50 rounded-lg p-3">
                      <p className="text-gray-600 text-sm">{contact.notes}</p>
                    </div>
                  )}
                </div>

                <div className="p-4 border-t border-gray-100 flex gap-2">
                  <button
                    onClick={() => handleEdit(contact)}
                    className="flex-1 bg-blue-500 text-white py-2 px-4 rounded-lg hover:bg-blue-600 transition-colors flex items-center justify-center gap-2"
                  >
                    <Edit size={16} />
                    تعديل
                  </button>
                  <button
                    onClick={() => handleDelete(contact.id)}
                    className="flex-1 bg-red-500 text-white py-2 px-4 rounded-lg hover:bg-red-600 transition-colors flex items-center justify-center gap-2"
                  >
                    <Trash2 size={16} />
                    حذف
                  </button>
                </div>
              </div>
            ))
          )}
        </div>
      </div>
    </div>
  );
};

export default ContactsView;
