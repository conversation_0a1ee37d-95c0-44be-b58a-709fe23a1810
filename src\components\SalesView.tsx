import React, { useState } from 'react';
import { Users, Plus, Search, ShoppingCart, CreditCard, CheckCircle, Clock, XCircle, DollarSign } from 'lucide-react';

interface Sale {
  id: string;
  customerName: string;
  customerPhone: string;
  items: SaleItem[];
  totalAmount: number;
  date: string;
  paymentMethod: 'cash' | 'card' | 'transfer';
  status: 'completed' | 'pending' | 'cancelled';
  notes: string;
}

interface SaleItem {
  id: string;
  productName: string;
  quantity: number;
  unitPrice: number;
  totalPrice: number;
}

interface SalesViewProps {
  sales: Sale[];
  onSaleUpdate: (saleId: string, updatedSale: Sale) => void;
  onSaleAdd: (sale: Omit<Sale, 'id'>) => void;
  onSaleDelete: (saleId: string) => void;
}

const SalesView: React.FC<SalesViewProps> = ({ 
  sales, 
  onSaleUpdate, 
  onSaleAdd, 
  onSaleDelete 
}) => {
  const [showAddModal, setShowAddModal] = useState(false);
  const [editingSale, setEditingSale] = useState<Sale | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [activeTab, setActiveTab] = useState<'sales' | 'payments' | 'customers' | 'advances' | 'reports'>('sales');
  const [statusFilter, setStatusFilter] = useState<'all' | 'completed' | 'pending' | 'cancelled'>('all');

  const filteredSales = sales.filter(sale =>
    (statusFilter === 'all' || sale.status === statusFilter) &&
    (sale.customerName.includes(searchTerm) ||
      sale.customerPhone.includes(searchTerm) ||
      sale.status.includes(searchTerm))
  );

  const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    const formData = new FormData(e.currentTarget);
    
    // جمع بيانات العناصر من النموذج
    const items: SaleItem[] = [];
    const itemCount = parseInt(formData.get('itemCount') as string) || 1;
    
    for (let i = 0; i < itemCount; i++) {
      const productName = formData.get(`item_${i}_name`) as string;
      const quantity = parseFloat(formData.get(`item_${i}_quantity`) as string);
      const unitPrice = parseFloat(formData.get(`item_${i}_price`) as string);
      
      if (productName && quantity && unitPrice) {
        items.push({
          id: `item_${Date.now()}_${i}`,
          productName,
          quantity,
          unitPrice,
          totalPrice: quantity * unitPrice
        });
      }
    }

    const saleData = {
      customerName: formData.get('customerName') as string,
      customerPhone: formData.get('customerPhone') as string,
      items,
      totalAmount: items.reduce((sum, item) => sum + item.totalPrice, 0),
      date: formData.get('date') as string,
      paymentMethod: formData.get('paymentMethod') as 'cash' | 'card' | 'transfer',
      status: 'completed' as 'completed' | 'pending' | 'cancelled',
      notes: formData.get('notes') as string
    };

    if (editingSale) {
      onSaleUpdate(editingSale.id, { ...editingSale, ...saleData });
      setEditingSale(null);
    } else {
      onSaleAdd(saleData);
      setShowAddModal(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'bg-green-100 text-green-800';
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      case 'cancelled': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'completed': return 'مكتمل';
      case 'pending': return 'قيد الانتظار';
      case 'cancelled': return 'ملغي';
      default: return 'غير محدد';
    }
  };

  const getPaymentMethodText = (method: string) => {
    switch (method) {
      case 'cash': return 'نقداً';
      case 'card': return 'بطاقة ائتمان';
      case 'transfer': return 'تحويل بنكي';
      default: return 'غير محدد';
    }
  };

  const totalSales = sales.reduce((sum, sale) => sum + sale.totalAmount, 0);
  const completedSales = sales.filter(sale => sale.status === 'completed');
  const pendingSales = sales.filter(sale => sale.status === 'pending');
  const cancelledSales = sales.filter(sale => sale.status === 'cancelled');

  // أضف دالة توحيد العملة في الأعلى
  const formatCurrency = (amount: number) =>
    new Intl.NumberFormat('ar-JO', { style: 'currency', currency: 'JOD' }).format(amount);

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 via-blue-50 to-indigo-100 p-6 space-y-6">
      {/* Header مع التدرج */}
      <div className="bg-gradient-to-r from-indigo-700 via-blue-600 to-green-600 text-white p-6 shadow-2xl rounded-b-3xl">
        <div className="max-w-7xl mx-auto">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4 space-x-reverse">
              <div className="bg-white/30 p-3 rounded-lg shadow-lg border-2 border-indigo-300">
                <ShoppingCart className="h-8 w-8" />
              </div>
              <div>
                <h1 className="text-3xl font-bold text-white drop-shadow-lg">إدارة المبيعات</h1>
                <p className="text-indigo-100 mt-1">مبيعات المنتجات والعملاء</p>
              </div>
            </div>
            <button
              onClick={() => setShowAddModal(true)}
              className="bg-gradient-to-r from-green-500 to-blue-500 text-white px-6 py-3 rounded-xl hover:from-blue-600 hover:to-green-600 transition-all duration-300 flex items-center gap-2 shadow-xl hover:shadow-2xl transform hover:scale-105 font-arabic font-medium tracking-wide"
            >
              <Plus size={20} />
              <span>إضافة مبيعات</span>
            </button>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto p-6">
        {/* شريط التنقل */}
        <div className="bg-white rounded-xl shadow-sm p-4 mb-6">
          <div className="flex items-center justify-center space-x-4 space-x-reverse">
            <button className={`px-6 py-3 rounded-lg font-semibold flex items-center space-x-2 space-x-reverse ${activeTab === 'sales' ? 'bg-green-600 text-white' : 'bg-gray-100 text-green-700'}`} onClick={() => setActiveTab('sales')}>
              <ShoppingCart size={20} />
              <span>المبيعات</span>
            </button>
            <button className={`px-6 py-3 rounded-lg font-semibold flex items-center space-x-2 space-x-reverse ${activeTab === 'payments' ? 'bg-blue-600 text-white' : 'bg-gray-100 text-blue-700'}`} onClick={() => setActiveTab('payments')}>
              <CreditCard size={20} />
              <span>المدفوعات</span>
            </button>
            <button className={`px-6 py-3 rounded-lg font-semibold flex items-center space-x-2 space-x-reverse ${activeTab === 'customers' ? 'bg-emerald-600 text-white' : 'bg-gray-100 text-emerald-700'}`} onClick={() => setActiveTab('customers')}>
              <Users size={20} />
              <span>العملاء</span>
            </button>
            <button className={`px-6 py-3 rounded-lg font-semibold flex items-center space-x-2 space-x-reverse ${activeTab === 'advances' ? 'bg-orange-600 text-white' : 'bg-gray-100 text-orange-700'}`} onClick={() => setActiveTab('advances')}>
              <DollarSign size={20} />
              <span>السلف</span>
            </button>
            <button className={`px-6 py-3 rounded-lg font-semibold flex items-center space-x-2 space-x-reverse ${activeTab === 'reports' ? 'bg-yellow-600 text-white' : 'bg-gray-100 text-yellow-700'}`} onClick={() => setActiveTab('reports')}>
              <DollarSign size={20} />
              <span>التقارير</span>
            </button>
          </div>
        </div>

        {/* محتوى التبويبات */}
        {activeTab === 'sales' && (
          <>
            {/* بطاقات الإحصائيات */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
              <div onClick={() => setStatusFilter('all')} className={`cursor-pointer bg-white rounded-2xl shadow-2xl p-6 border-2 border-green-100 transition-all duration-200 ${statusFilter === 'all' ? 'ring-4 ring-green-300' : ''}`}>
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-green-700 text-sm font-medium">إجمالي المبيعات</p>
                    <p className="text-3xl font-bold text-blue-700">{formatCurrency(totalSales)}</p>
                  </div>
                  <div className="bg-green-100 p-3 rounded-lg">
                    <ShoppingCart className="h-8 w-8 text-green-600" />
                  </div>
                </div>
              </div>

              <div onClick={() => setStatusFilter('completed')} className={`cursor-pointer bg-white rounded-2xl shadow-2xl p-6 border-2 border-emerald-100 transition-all duration-200 ${statusFilter === 'completed' ? 'ring-4 ring-emerald-300' : ''}`}>
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-emerald-700 text-sm font-medium">المبيعات المكتملة</p>
                    <p className="text-3xl font-bold text-green-700">{completedSales.length}</p>
                  </div>
                  <div className="bg-emerald-100 p-3 rounded-lg">
                    <CheckCircle className="h-8 w-8 text-emerald-600" />
                  </div>
                </div>
              </div>



              <div onClick={() => setStatusFilter('cancelled')} className={`cursor-pointer bg-white rounded-2xl shadow-2xl p-6 border-2 border-red-100 transition-all duration-200 ${statusFilter === 'cancelled' ? 'ring-4 ring-red-300' : ''}`}>
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-red-700 text-sm font-medium">ملغية</p>
                    <p className="text-3xl font-bold text-green-700">{cancelledSales.length}</p>
                  </div>
                  <div className="bg-red-100 p-3 rounded-lg">
                    <XCircle className="h-8 w-8 text-red-600" />
                  </div>
                </div>
              </div>
            </div>

            {/* شريط البحث */}
            <div className="bg-white rounded-2xl shadow-xl p-4 mb-6 border-2 border-indigo-100">
              <div className="flex items-center justify-between">
                <div className="relative flex-1 max-w-md">
                  <input
                    type="text"
                    placeholder="البحث في المبيعات..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                  />
                  <Search className="absolute left-3 top-3 h-5 w-5 text-gray-400" />
                </div>
                <button
                  onClick={() => setShowAddModal(true)}
                  className="bg-gradient-to-r from-green-500 to-blue-500 text-white px-6 py-3 rounded-xl hover:from-blue-600 hover:to-green-600 transition-all duration-300 flex items-center gap-2 shadow-xl hover:shadow-2xl transform hover:scale-105 font-arabic font-medium tracking-wide"
                >
                  <Plus size={20} />
                  <span>إضافة مبيعات جديدة</span>
                </button>
              </div>
            </div>

            {/* جدول المبيعات */}
            <div className="bg-white rounded-3xl shadow-2xl border-2 border-indigo-100 overflow-hidden">
              <div className="px-6 py-4 border-b border-indigo-200">
                <h3 className="text-lg font-bold text-indigo-800 drop-shadow">قائمة المبيعات</h3>
              </div>

              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-4 text-right text-sm font-semibold text-gray-900">العميل</th>
                      <th className="px-6 py-4 text-right text-sm font-semibold text-gray-900">المنتجات</th>
                      <th className="px-6 py-4 text-right text-sm font-semibold text-gray-900">المبلغ الإجمالي</th>
                      <th className="px-6 py-4 text-right text-sm font-semibold text-gray-900">التاريخ</th>
                      <th className="px-6 py-4 text-right text-sm font-semibold text-gray-900">طريقة الدفع</th>
                      <th className="px-6 py-4 text-right text-sm font-semibold text-gray-900">الحالة</th>
                      <th className="px-6 py-4 text-right text-sm font-semibold text-gray-900">الإجراءات</th>
                    </tr>
                  </thead>
                  <tbody className="divide-y divide-gray-200">
                    {filteredSales.map((sale) => (
                      <tr key={sale.id} className="hover:bg-gray-50 transition-colors">
                        <td className="px-6 py-4">
                          <div className="flex items-center">
                            <div className="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center">
                              <span className="text-green-600 font-semibold">
                                {sale.customerName.charAt(0)}
                              </span>
                            </div>
                            <div className="mr-4">
                              <div className="text-sm font-medium text-gray-900">{sale.customerName}</div>
                              <div className="text-sm text-gray-500">{sale.customerPhone}</div>
                            </div>
                          </div>
                        </td>
                        <td className="px-6 py-4 text-sm text-gray-900">
                          <div className="max-w-xs">
                            {sale.items.map((item, index) => (
                              <div key={item.id} className="text-xs">
                                {item.productName} - {item.quantity} × {formatCurrency(item.unitPrice)}
                              </div>
                            ))}
                          </div>
                        </td>
                        <td className="px-6 py-4 text-sm font-bold text-gray-900">
                          {formatCurrency(sale.totalAmount)}
                        </td>
                        <td className="px-6 py-4 text-sm text-gray-900">
                          {new Date(sale.date).toLocaleDateString('ar-SA')}
                        </td>
                        <td className="px-6 py-4 text-sm text-gray-900">
                          {getPaymentMethodText(sale.paymentMethod)}
                        </td>
                        <td className="px-6 py-4">
                          <span className={`px-3 py-1 rounded-full text-xs font-medium ${getStatusColor(sale.status)}`}>
                            {getStatusText(sale.status)}
                          </span>
                        </td>
                        <td className="px-6 py-4">
                          <div className="flex items-center space-x-2 space-x-reverse">
                            <button
                              onClick={() => setEditingSale(sale)}
                              className="bg-green-100 text-green-600 px-3 py-1 rounded-lg text-xs font-medium hover:bg-green-200 transition-colors"
                            >
                              تعديل
                            </button>
                            <button
                              onClick={() => onSaleDelete(sale.id)}
                              className="bg-red-100 text-red-600 px-3 py-1 rounded-lg text-xs font-medium hover:bg-red-200 transition-colors"
                            >
                              حذف
                            </button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>

            {/* نافذة إضافة/تعديل مبيعات */}
            {(showAddModal || editingSale) && (
              <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
                <div className="bg-white rounded-xl shadow-2xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
                  <div className="bg-green-600 text-white p-6 rounded-t-xl">
                    <h2 className="text-xl font-bold">
                      {editingSale ? 'تعديل مبيعات' : 'إضافة مبيعات جديدة'}
                    </h2>
                  </div>
                  
                  <form onSubmit={handleSubmit} className="p-6 space-y-4">
                    <input type="hidden" name="itemCount" value="1" />
                    
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">اسم العميل</label>
                        <input
                          type="text"
                          name="customerName"
                          defaultValue={editingSale?.customerName}
                          required
                          className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                        />
                      </div>
                      
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">رقم الهاتف</label>
                        <input
                          type="tel"
                          name="customerPhone"
                          defaultValue={editingSale?.customerPhone}
                          required
                          className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                        />
                      </div>
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">تاريخ البيع</label>
                      <input
                        type="date"
                        name="date"
                        defaultValue={editingSale?.date}
                        required
                        className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                      />
                    </div>
                    
                                        <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">طريقة الدفع</label>
                      <select
                        name="paymentMethod"
                        defaultValue={editingSale?.paymentMethod || 'cash'}
                        className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                      >
                        <option value="cash">نقداً</option>
                        <option value="card">بطاقة ائتمان</option>
                        <option value="transfer">تحويل بنكي</option>
                      </select>
                    </div>
                    
                    {/* Items Section */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">المنتجات</label>
                      <div id="itemsContainer" className="space-y-3">
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
                          <input
                            type="text"
                            name="item_0_name"
                            placeholder="اسم المنتج"
                            defaultValue={editingSale?.items[0]?.productName}
                            required
                            className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                          />
                          <input
                            type="number"
                            name="item_0_quantity"
                            placeholder="الكمية"
                            defaultValue={editingSale?.items[0]?.quantity}
                            required
                            min="1"
                            className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                          />
                          <input
                            type="number"
                            name="item_0_price"
                            placeholder="سعر الوحدة"
                            defaultValue={editingSale?.items[0]?.unitPrice}
                            required
                            min="0"
                            step="0.01"
                            className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                          />
                        </div>
                      </div>
                      <button
                        type="button"
                        onClick={() => {
                          const container = document.getElementById('itemsContainer');
                          const itemCount = container?.children.length || 1;
                          const newItem = document.createElement('div');
                          newItem.className = 'grid grid-cols-1 md:grid-cols-3 gap-3';
                          newItem.innerHTML = `
                            <input type="text" name="item_${itemCount}_name" placeholder="اسم المنتج" required class="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent" />
                            <input type="number" name="item_${itemCount}_quantity" placeholder="الكمية" required min="1" class="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent" />
                            <input type="number" name="item_${itemCount}_price" placeholder="سعر الوحدة" required min="0" step="0.01" class="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent" />
                          `;
                          container?.appendChild(newItem);
                          const itemCountInput = document.querySelector('input[name="itemCount"]') as HTMLInputElement;
                          if (itemCountInput) {
                            itemCountInput.value = (itemCount + 1).toString();
                          }
                        }}
                        className="mt-2 text-sm text-green-600 hover:text-green-700"
                      >
                        + إضافة منتج آخر
                      </button>
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">ملاحظات</label>
                      <textarea
                        name="notes"
                        defaultValue={editingSale?.notes}
                        rows={3}
                        className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                      />
                    </div>
                    
                    <div className="flex gap-3 pt-4">
                      <button
                        type="submit"
                        className="flex-1 bg-green-600 text-white py-3 px-4 rounded-lg font-semibold hover:bg-green-700 transition-colors"
                      >
                        {editingSale ? 'تحديث' : 'إضافة'}
                      </button>
                      <button
                        type="button"
                        onClick={() => {
                          setShowAddModal(false);
                          setEditingSale(null);
                        }}
                        className="flex-1 bg-gray-200 text-gray-700 py-3 px-4 rounded-lg font-semibold hover:bg-gray-300 transition-colors"
                      >
                        إلغاء
                      </button>
                    </div>
                  </form>
                </div>
              </div>
            )}
          </>
        )}
        {activeTab === 'payments' && (
          <div className="bg-white rounded-xl shadow-lg p-8 text-center text-blue-700 text-xl font-bold">قائمة المدفوعات ستظهر هنا</div>
        )}
        {activeTab === 'customers' && (
          <div className="bg-white rounded-xl shadow-lg p-8 text-center text-emerald-700 text-xl font-bold">قائمة العملاء ستظهر هنا</div>
        )}
        {activeTab === 'advances' && (
          <div className="bg-white rounded-xl shadow-lg p-8 text-center text-orange-700 text-xl font-bold">قائمة السلف ستظهر هنا</div>
        )}
        {activeTab === 'reports' && (
          <div className="bg-white rounded-xl shadow-lg p-8 text-center text-yellow-700 text-xl font-bold">تقارير المبيعات ستظهر هنا</div>
        )}
      </div>
    </div>
  );
};

export default SalesView; 