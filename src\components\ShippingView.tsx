import React, { useState, useEffect } from 'react';
import {
  Plus,
  Building2,
  Phone,
  User,
  Edit,
  Leaf,
  Trash2,
  Save,
  X
} from 'lucide-react';
import Modal from './Modal';

interface ShippingOffice {
  id: number;
  name: string;
  phone: string;
  contactPerson: string;
  addedDate: string;
  notes?: string;
}

interface ShippingViewProps {
  setCurrentView: (view: string) => void;
  navigationItems: any[];
  currentView: string;
}

const ShippingView: React.FC<ShippingViewProps> = ({
  setCurrentView,
  navigationItems,
  currentView
}) => {
  // حالة مكاتب الشحن المحفوظة محلياً
  const [shippingOffices, setShippingOffices] = useState<ShippingOffice[]>([]);

  // حالات النوافذ المنبثقة
  const [isAddModalOpen, setIsAddModalOpen] = useState<boolean>(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState<boolean>(false);

  // حالات البيانات الجديدة
  const [newOffice, setNewOffice] = useState<{
    name: string;
    phone: string;
    contactPerson: string;
    notes: string;
  }>({
    name: '',
    phone: '',
    contactPerson: '',
    notes: ''
  });

  const [editingOffice, setEditingOffice] = useState<ShippingOffice | null>(null);

  // تحميل البيانات من localStorage عند بدء التطبيق
  useEffect(() => {
    const savedOffices = localStorage.getItem('shippingOffices');
    if (savedOffices) {
      setShippingOffices(JSON.parse(savedOffices));
    }
  }, []);

  // حفظ البيانات في localStorage عند تغييرها
  useEffect(() => {
    localStorage.setItem('shippingOffices', JSON.stringify(shippingOffices));
  }, [shippingOffices]);

  // دالة التنسيق
  const formatDate = (date: Date) => {
    const day = date.getDate().toString().padStart(2, '0');
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const year = date.getFullYear();
    return `${day}/${month}/${year}`;
  };

  // دوال المعالجة
  const handleAddOffice = () => {
    try {
      if (!newOffice.name.trim()) {
        alert('يرجى إدخال اسم مكتب الشحن');
        return;
      }
      if (!newOffice.phone.trim()) {
        alert('يرجى إدخال رقم الهاتف');
        return;
      }
      if (!newOffice.contactPerson.trim()) {
        alert('يرجى إدخال اسم الشخص المسؤول');
        return;
      }

      const newOfficeItem: ShippingOffice = {
        id: Date.now(),
        name: newOffice.name.trim(),
        phone: newOffice.phone.trim(),
        contactPerson: newOffice.contactPerson.trim(),
        addedDate: formatDate(new Date()),
        notes: newOffice.notes
      };

      setShippingOffices([newOfficeItem, ...shippingOffices]);
      setNewOffice({
        name: '',
        phone: '',
        contactPerson: '',
        notes: ''
      });
      setIsAddModalOpen(false);
      alert('تم إضافة مكتب الشحن بنجاح!');
    } catch (error) {
      console.error('خطأ في إضافة مكتب الشحن:', error);
      alert('حدث خطأ أثناء إضافة مكتب الشحن');
    }
  };

  const handleEditOffice = (office: ShippingOffice) => {
    setEditingOffice(office);
    setIsEditModalOpen(true);
  };

  const handleSaveEdit = () => {
    if (!editingOffice) return;

    try {
      const updatedOffices = shippingOffices.map(office =>
        office.id === editingOffice.id ? editingOffice : office
      );
      setShippingOffices(updatedOffices);
      setIsEditModalOpen(false);
      setEditingOffice(null);
      alert('تم تحديث البيانات بنجاح!');
    } catch (error) {
      console.error('خطأ في تحديث البيانات:', error);
      alert('حدث خطأ أثناء تحديث البيانات');
    }
  };

  const handleDeleteOffice = (id: number) => {
    if (window.confirm('هل أنت متأكد من حذف مكتب الشحن هذا؟')) {
      setShippingOffices(shippingOffices.filter(office => office.id !== id));
      alert('تم حذف مكتب الشحن بنجاح!');
    }
  };

  // حساب الإحصائيات
  const totalOffices = shippingOffices.length;

  // تاريخ اليوم بالميلادي
  const today = new Date();
  const weekdays = ['الأحد', 'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت'];
  const months = ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو', 'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'];
  const formattedDate = `${weekdays[today.getDay()]}، ${today.getDate()} ${months[today.getMonth()]} ${today.getFullYear()}`;

  return (
    <div className="min-h-screen bg-gradient-to-br from-indigo-50 via-purple-50 to-green-100 p-6 space-y-6">
      {/* Header */}
      <div className="bg-gradient-to-r from-indigo-700 via-purple-600 to-green-600 text-white p-6 shadow-2xl rounded-b-3xl">
        <div className="container mx-auto">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <div className="w-12 h-12 bg-white/30 rounded-2xl flex items-center justify-center backdrop-blur-md shadow-lg border-2 border-indigo-300">
                <Building2 size={24} className="text-white" />
              </div>
              <div>
                <h1 className="text-2xl font-bold font-arabic tracking-wide text-white drop-shadow-lg">إدارة مكاتب الشحن</h1>
                <p className="text-indigo-100 text-sm font-medium tracking-wider">Shipping Offices Management</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Navigation */}
      <div className="bg-gradient-to-r from-indigo-500 to-purple-500 p-4 shadow-lg">
        <div className="container mx-auto">
          <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 xl:grid-cols-9 gap-3">
            {navigationItems.map((item) => {
              const IconComponent = item.icon;
              return (
                <button
                  key={item.id}
                  onClick={() => setCurrentView(item.id)}
                  className={`p-4 rounded-2xl transition-all duration-300 transform hover:scale-105 hover:shadow-lg ${
                    currentView === item.id
                      ? 'bg-white text-indigo-600 shadow-xl border-2 border-indigo-500'
                      : 'bg-white text-indigo-600 hover:bg-gray-50 shadow-md'
                  }`}
                >
                  <IconComponent size={24} className="mx-auto mb-2 text-indigo-600" />
                  <span className="text-sm font-bold block font-arabic text-indigo-600">{item.name}</span>
                </button>
              );
            })}
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="container mx-auto p-6">
        {/* Page Header */}
        <div className="flex items-center justify-between mb-8">
          <div className="flex items-center gap-4">
            <div className="w-12 h-12 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-2xl flex items-center justify-center shadow-lg">
              <Building2 size={24} className="text-white" />
            </div>
            <div>
              <h2 className="text-3xl font-bold font-arabic text-gray-800 tracking-wide">إدارة مكاتب الشحن</h2>
              <p className="text-gray-600 text-sm mt-1">{formattedDate}</p>
            </div>
          </div>
          
          {/* Action Buttons */}
          <div className="flex gap-3">
            <button
              onClick={() => setIsAddModalOpen(true)}
              className="bg-gradient-to-r from-indigo-500 to-indigo-600 text-white px-6 py-3 rounded-xl hover:from-indigo-600 hover:to-indigo-700 transition-all duration-300 flex items-center gap-2 shadow-lg hover:shadow-xl transform hover:scale-105 font-arabic font-medium tracking-wide"
            >
              <Plus size={20} />
              إضافة مكتب شحن
            </button>
          </div>
        </div>

        {/* Statistics Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          <div className="bg-white rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 border border-indigo-100">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-gray-600 text-sm font-medium mb-2 font-arabic tracking-wide">إجمالي مكاتب الشحن</h3>
                <p className="text-3xl font-bold text-indigo-600 font-arabic">{totalOffices}</p>
              </div>
              <div className="w-12 h-12 bg-gradient-to-br from-indigo-500 to-indigo-600 rounded-2xl flex items-center justify-center">
                <Building2 size={24} className="text-white" />
              </div>
            </div>
          </div>

          <div className="bg-white rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 border border-green-100">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-gray-600 text-sm font-medium mb-2 font-arabic tracking-wide">مكاتب نشطة</h3>
                <p className="text-3xl font-bold text-green-600 font-arabic">{totalOffices}</p>
              </div>
              <div className="w-12 h-12 bg-gradient-to-br from-green-500 to-green-600 rounded-2xl flex items-center justify-center">
                <Building2 size={24} className="text-white" />
              </div>
            </div>
          </div>

          <div className="bg-white rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 border border-blue-100">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-gray-600 text-sm font-medium mb-2 font-arabic tracking-wide">أحدث إضافة</h3>
                <p className="text-lg font-bold text-blue-600 font-arabic">
                  {shippingOffices.length > 0 ? shippingOffices[0].addedDate : 'لا يوجد'}
                </p>
              </div>
              <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-2xl flex items-center justify-center">
                <Phone size={24} className="text-white" />
              </div>
            </div>
          </div>
        </div>

        {/* Shipping Offices Table */}
        <div className="bg-white rounded-2xl shadow-xl overflow-hidden">
          <div className="p-6 bg-gradient-to-r from-indigo-500 to-purple-500 text-white">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <Building2 size={24} />
                <h3 className="text-xl font-bold font-arabic">قائمة مكاتب الشحن</h3>
              </div>
              <div className="text-sm bg-white/20 px-3 py-1 rounded-lg backdrop-blur-sm">
                {formattedDate}
              </div>
            </div>
          </div>

          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-4 text-right text-sm font-medium text-gray-500 tracking-wider">#</th>
                  <th className="px-6 py-4 text-right text-sm font-medium text-gray-500 tracking-wider">اسم المكتب</th>
                  <th className="px-6 py-4 text-right text-sm font-medium text-gray-500 tracking-wider">رقم الهاتف</th>
                  <th className="px-6 py-4 text-right text-sm font-medium text-gray-500 tracking-wider">الشخص المسؤول</th>
                  <th className="px-6 py-4 text-right text-sm font-medium text-gray-500 tracking-wider">تاريخ الإضافة</th>
                  <th className="px-6 py-4 text-right text-sm font-medium text-gray-500 tracking-wider">ملاحظات</th>
                  <th className="px-6 py-4 text-right text-sm font-medium text-gray-500 tracking-wider">إجراءات</th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-200">
                {shippingOffices.length === 0 ? (
                  <tr>
                    <td colSpan={7} className="px-6 py-12 text-center text-gray-500">
                      <Building2 size={48} className="mx-auto mb-4 text-gray-300" />
                      <p className="text-lg font-medium">لا يوجد مكاتب شحن مسجلة</p>
                      <p className="text-sm">اضغط على "إضافة مكتب شحن" لإضافة مكتب جديد</p>
                    </td>
                  </tr>
                ) : (
                  shippingOffices.map((office, index) => (
                    <tr key={office.id} className={index % 2 === 0 ? 'bg-white' : 'bg-gray-50'}>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{index + 1}</td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <div className="w-8 h-8 bg-indigo-100 rounded-full flex items-center justify-center mr-3">
                            <Building2 size={16} className="text-indigo-600" />
                          </div>
                          <div className="font-medium text-gray-900">{office.name}</div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center text-gray-500">
                          <Phone size={14} className="mr-1" />
                          {office.phone}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center text-gray-500">
                          <User size={14} className="mr-1" />
                          {office.contactPerson}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{office.addedDate}</td>
                      <td className="px-6 py-4">
                        <div className="text-sm text-gray-500 max-w-xs truncate">
                          {office.notes || '-'}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <div className="flex gap-2">
                          <button
                            onClick={() => handleEditOffice(office)}
                            className="text-blue-600 hover:text-blue-900 bg-blue-100 hover:bg-blue-200 p-2 rounded-lg transition-colors"
                            title="تعديل"
                          >
                            <Edit size={16} />
                          </button>
                          <button
                            onClick={() => handleDeleteOffice(office.id)}
                            className="text-red-600 hover:text-red-900 bg-red-100 hover:bg-red-200 p-2 rounded-lg transition-colors"
                            title="حذف"
                          >
                            <Trash2 size={16} />
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))
                )}
              </tbody>
            </table>
          </div>
        </div>
      </div>

      {/* Add Shipping Office Modal */}
      <Modal isOpen={isAddModalOpen} onClose={() => {
        setIsAddModalOpen(false);
        setNewOffice({
          name: '',
          phone: '',
          contactPerson: '',
          notes: ''
        });
      }} title="إضافة مكتب شحن جديد">
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">اسم مكتب الشحن</label>
            <input
              type="text"
              value={newOffice.name}
              onChange={(e) => setNewOffice({...newOffice, name: e.target.value})}
              className="w-full p-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-all duration-200"
              placeholder="أدخل اسم مكتب الشحن"
              dir="rtl"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">رقم الهاتف</label>
            <input
              type="tel"
              value={newOffice.phone}
              onChange={(e) => setNewOffice({...newOffice, phone: e.target.value})}
              className="w-full p-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-all duration-200"
              placeholder="أدخل رقم الهاتف"
              dir="ltr"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">الشخص المسؤول</label>
            <input
              type="text"
              value={newOffice.contactPerson}
              onChange={(e) => setNewOffice({...newOffice, contactPerson: e.target.value})}
              className="w-full p-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-all duration-200"
              placeholder="أدخل اسم الشخص المسؤول"
              dir="rtl"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">الملاحظات (اختياري)</label>
            <textarea
              value={newOffice.notes}
              onChange={(e) => setNewOffice({...newOffice, notes: e.target.value})}
              className="w-full p-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-all duration-200 resize-none"
              rows={3}
              placeholder="أدخل أي ملاحظات إضافية (اختياري)"
              dir="rtl"
            />
          </div>

          <div className="flex gap-3 pt-4">
            <button
              onClick={handleAddOffice}
              className="flex-1 bg-gradient-to-r from-indigo-500 to-indigo-600 text-white py-3 rounded-xl hover:from-indigo-600 hover:to-indigo-700 transition-all duration-300 flex items-center justify-center gap-2 shadow-lg"
            >
              <Save size={20} />
              حفظ
            </button>
            <button
              onClick={() => {
                setIsAddModalOpen(false);
                setNewOffice({
                  name: '',
                  phone: '',
                  contactPerson: '',
                  notes: ''
                });
              }}
              className="flex-1 bg-gray-500 text-white py-3 rounded-xl hover:bg-gray-600 transition-all duration-300"
            >
              إلغاء
            </button>
          </div>
        </div>
      </Modal>

      {/* Edit Shipping Office Modal */}
      <Modal isOpen={isEditModalOpen} onClose={() => setIsEditModalOpen(false)} title="تعديل بيانات مكتب الشحن">
        {editingOffice && (
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">اسم مكتب الشحن</label>
              <input
                type="text"
                value={editingOffice.name}
                onChange={(e) => setEditingOffice({...editingOffice, name: e.target.value})}
                className="w-full p-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-all duration-200"
                dir="rtl"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">رقم الهاتف</label>
              <input
                type="tel"
                value={editingOffice.phone}
                onChange={(e) => setEditingOffice({...editingOffice, phone: e.target.value})}
                className="w-full p-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-all duration-200"
                dir="ltr"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">الشخص المسؤول</label>
              <input
                type="text"
                value={editingOffice.contactPerson}
                onChange={(e) => setEditingOffice({...editingOffice, contactPerson: e.target.value})}
                className="w-full p-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-all duration-200"
                dir="rtl"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">الملاحظات</label>
              <textarea
                value={editingOffice.notes || ''}
                onChange={(e) => setEditingOffice({...editingOffice, notes: e.target.value})}
                className="w-full p-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-all duration-200"
                rows={3}
                dir="rtl"
              />
            </div>

            <div className="flex gap-3 pt-4">
              <button
                onClick={handleSaveEdit}
                className="flex-1 bg-gradient-to-r from-indigo-500 to-indigo-600 text-white py-3 rounded-xl hover:from-indigo-600 hover:to-indigo-700 transition-all duration-300 flex items-center justify-center gap-2 shadow-lg"
              >
                <Save size={20} />
                حفظ التعديل
              </button>
              <button
                onClick={() => setIsEditModalOpen(false)}
                className="flex-1 bg-gray-500 text-white py-3 rounded-xl hover:bg-gray-600 transition-all duration-300"
              >
                إلغاء
              </button>
            </div>
          </div>
        )}
      </Modal>
    </div>
  );
};

export default ShippingView;