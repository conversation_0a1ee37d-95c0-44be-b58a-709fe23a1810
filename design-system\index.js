/**
 * نظام التصميم - Lettuce Farm Management System
 * Design System Index File
 * 
 * هذا الملف يحتوي على جميع عناصر نظام التصميم المستخرجة من المشروع
 * كما هي بالضبط دون أي تغيير أو تعديل
 */

// ========================================
// تصدير جميع عناصر نظام التصميم
// ========================================

// 1. نظام التصميم الكامل
export { default as designSystem } from './design-system.js';

// 2. الألوان
export { colors } from './design-system.js';

// 3. الخطوط
export { fonts } from './design-system.js';

// 4. المسافات
export { spacing } from './design-system.js';

// 5. الحواف
export { borderRadius } from './design-system.js';

// 6. الظلال
export { shadows } from './design-system.js';

// 7. المؤثرات الحركية
export { animations } from './design-system.js';

// 8. الانتقالات
export { transitions } from './design-system.js';

// 9. أنماط الأزرار
export { buttonStyles } from './design-system.js';

// 10. أنماط البطاقات
export { cardStyles } from './design-system.js';

// 11. أنماط حقول الإدخال
export { inputStyles } from './design-system.js';

// 12. أنماط النوافذ المنبثقة
export { modalStyles } from './design-system.js';

// 13. أنماط الجداول
export { tableStyles } from './design-system.js';

// 14. أنماط التنقل
export { navigationStyles } from './design-system.js';

// 15. أنماط حالات الحالة
export { statusStyles } from './design-system.js';

// 16. الخلفيات المتدرجة
export { gradients } from './design-system.js';

// 17. نقاط التوقف
export { breakpoints } from './design-system.js';

// 18. الأيقونات
export { icons } from './design-system.js';

// ========================================
// دوال مساعدة سريعة
// ========================================

/**
 * الحصول على لون من لوحة الألوان
 * @param {string} colorPath - مسار اللون (مثل: 'primary.600')
 * @returns {string} قيمة اللون
 */
export const getColor = (colorPath) => {
  const path = colorPath.split('.');
  let result = colors;
  for (const key of path) {
    result = result[key];
  }
  return result;
};

/**
 * الحصول على مسافة من نظام المسافات
 * @param {string|number} size - حجم المسافة
 * @returns {string} قيمة المسافة
 */
export const getSpacing = (size) => spacing[size] || size;

/**
 * الحصول على حجم خط من نظام الخطوط
 * @param {string} size - حجم الخط
 * @returns {string} قيمة حجم الخط
 */
export const getFontSize = (size) => fonts.size[size] || size;

/**
 * الحصول على نصف قطر الحد من نظام الحواف
 * @param {string} size - حجم الحافة
 * @returns {string} قيمة نصف قطر الحافة
 */
export const getBorderRadius = (size) => borderRadius[size] || size;

/**
 * الحصول على ظل من نظام الظلال
 * @param {string} type - نوع الظل
 * @returns {string} قيمة الظل
 */
export const getShadow = (type) => shadows[type] || shadows.default;

// ========================================
// توليد CSS المتغيرات
// ========================================

/**
 * توليد متغيرات CSS الأساسية
 * @returns {string} CSS المتغيرات
 */
export const generateCSSVariables = () => {
  return `
    :root {
      --primary-color: ${colors.primary[600]};
      --primary-dark: ${colors.primary[700]};
      --primary-light: ${colors.primary[500]};
      --secondary-color: ${colors.secondary[600]};
      --accent-color: ${colors.accent[500]};
      --success-color: ${colors.success[600]};
      --warning-color: ${colors.warning[500]};
      --error-color: ${colors.error[600]};
      --background-color: ${colors.background.primary};
      --surface-color: ${colors.background.secondary};
      --text-primary: ${colors.text.primary};
      --text-secondary: ${colors.text.secondary};
      --border-color: ${colors.border.primary};
    }
  `;
};

// ========================================
// أنماط سريعة جاهزة للاستخدام
// ========================================

/**
 * أنماط الأزرار الجاهزة
 */
export const quickButtonStyles = {
  primary: {
    ...buttonStyles.base,
    ...buttonStyles.primary,
  },
  secondary: {
    ...buttonStyles.base,
    ...buttonStyles.secondary,
  },
  success: {
    ...buttonStyles.base,
    ...buttonStyles.success,
  },
  warning: {
    ...buttonStyles.base,
    ...buttonStyles.warning,
  },
  danger: {
    ...buttonStyles.base,
    ...buttonStyles.danger,
  },
  outline: {
    ...buttonStyles.base,
    ...buttonStyles.outline,
  },
};

/**
 * أنماط البطاقات الجاهزة
 */
export const quickCardStyles = {
  default: {
    ...cardStyles.base,
  },
  hover: {
    ...cardStyles.base,
    ...cardStyles.hover,
  },
  dashboard: {
    ...cardStyles.dashboard,
  },
  zone: {
    ...cardStyles.zone,
  },
};

/**
 * أنماط حقول الإدخال الجاهزة
 */
export const quickInputStyles = {
  default: {
    ...inputStyles.base,
  },
  focus: {
    ...inputStyles.base,
    ...inputStyles.focus,
  },
  error: {
    ...inputStyles.base,
    ...inputStyles.error,
  },
};

// ========================================
// معلومات النظام
// ========================================

export const systemInfo = {
  name: 'Lettuce Farm Management System Design System',
  version: '1.0.0',
  description: 'نظام تصميم شامل مستخرج من مشروع نظام إدارة مزرعة الخس',
  author: 'Lettuce Farm Management',
  created: '2025',
  source: 'Original project styles extracted without modification',
  compatibility: ['React', 'Tailwind CSS', 'Vanilla CSS', 'JavaScript'],
  icons: {
    library: 'Lucide React',
    url: 'https://lucide.dev/',
  },
  fonts: {
    primary: 'Cairo',
    secondary: 'Tajawal',
    source: 'Google Fonts',
  },
};

// ========================================
// تصدير افتراضي
// ========================================

export default {
  designSystem,
  colors,
  fonts,
  spacing,
  borderRadius,
  shadows,
  animations,
  transitions,
  buttonStyles,
  cardStyles,
  inputStyles,
  modalStyles,
  tableStyles,
  navigationStyles,
  statusStyles,
  gradients,
  breakpoints,
  icons,
  quickButtonStyles,
  quickCardStyles,
  quickInputStyles,
  getColor,
  getSpacing,
  getFontSize,
  getBorderRadius,
  getShadow,
  generateCSSVariables,
  systemInfo,
}; 