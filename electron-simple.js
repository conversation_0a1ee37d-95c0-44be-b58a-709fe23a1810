const { app, BrowserWindow } = require('electron');
const path = require('path');
const fs = require('fs');

let mainWindow;

function createWindow() {
    mainWindow = new BrowserWindow({
        width: 1400,
        height: 900,
        minWidth: 1000,
        minHeight: 700,
        webPreferences: {
            nodeIntegration: true,
            contextIsolation: false,
            webSecurity: false
        },
        icon: path.join(__dirname, 'public', 'favicon.ico'),
        show: false
    });

    // تحقق من وجود ملف البناء
    const distPath = path.join(__dirname, 'dist', 'index.html');
    if (fs.existsSync(distPath)) {
        mainWindow.loadFile(distPath);
    } else {
        console.log('ملف البناء غير موجود، يرجى تشغيل npm run build أولاً');
        app.quit();
        return;
    }

    mainWindow.once('ready-to-show', () => {
        mainWindow.show();
        console.log('تم تشغيل التطبيق بنجاح');
    });

    mainWindow.setMenuBarVisibility(false);

    mainWindow.on('closed', () => {
        mainWindow = null;
    });
}

app.whenReady().then(createWindow);

app.on('window-all-closed', () => {
    if (process.platform !== 'darwin') {
        app.quit();
    }
});

app.on('activate', () => {
    if (BrowserWindow.getAllWindows().length === 0) {
        createWindow();
    }
});