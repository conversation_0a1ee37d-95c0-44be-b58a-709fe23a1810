import React from 'react';
import { X, Sparkles } from 'lucide-react';
import { ModalProps } from '../types';

const Modal: React.FC<ModalProps> = ({ isOpen, onClose, title, children }) => {
  if (!isOpen) return null;

  return (
    <div className="modal-overlay fade-in">
      <div className="modal-content relative">
        <div className="flex justify-between items-center mb-6">
          <h3 className="text-xl font-bold text-gray-800 flex items-center gap-2 w-full">
            <div className="w-9 h-9 bg-gradient-to-br from-emerald-500 to-teal-600 rounded-xl flex items-center justify-center shadow-md">
              <Sparkles size={18} className="text-white" />
            </div>
            <span className="ml-2 bg-gradient-to-br from-emerald-500 to-blue-500 bg-clip-text text-transparent">
              {title}
            </span>
          </h3>
          <button
            onClick={onClose}
            className="absolute top-4 left-4 bg-white border border-gray-200 shadow hover:bg-gray-100 text-gray-400 hover:text-emerald-600 transition-colors p-2 rounded-full z-10"
            aria-label="إغلاق"
          >
            <X size={22} />
          </button>
        </div>
        <div className="overflow-y-auto max-h-[calc(90vh-120px)]">
          {children}
        </div>
      </div>
    </div>
  );
};

export default Modal;
