import { useCallback, useState } from 'react';
import { enhancedDB } from '../utils/enhancedDatabase';
import { useToast } from './useToast';

interface BackupOptions {
  includeImages?: boolean;
  compress?: boolean;
  encrypt?: boolean;
}

interface BackupInfo {
  name: string;
  date: string;
  size: number;
  version: string;
  recordCount: number;
}

export const useBackup = () => {
  const [isBackingUp, setIsBackingUp] = useState(false);
  const [isRestoring, setIsRestoring] = useState(false);
  const { showSuccess, showError, showInfo, showWarning } = useToast();

  // إنشاء نسخة احتياطية
  const createBackup = useCallback(async (options: BackupOptions = {}) => {
    try {
      setIsBackingUp(true);
      showInfo('جاري إنشاء النسخة الاحتياطية', 'يرجى الانتظار...');

      // جلب جميع البيانات
      const allData = await enhancedDB.exportAllData();
      
      // إضافة معلومات إضافية للنسخة الاحتياطية
      const backupData = {
        ...allData,
        backupInfo: {
          name: `نسخة احتياطية - ${new Date().toLocaleDateString('ar-SA')}`,
          createdAt: new Date().toISOString(),
          appVersion: '1.0.0',
          databaseVersion: 2,
          options
        }
      };

      // حساب عدد السجلات
      let totalRecords = 0;
      Object.keys(allData).forEach(key => {
        if (Array.isArray(allData[key])) {
          totalRecords += allData[key].length;
        }
      });

      // تحويل البيانات إلى JSON
      let jsonData = JSON.stringify(backupData, null, 2);
      
      // ضغط البيانات إذا طُلب ذلك
      if (options.compress) {
        // يمكن إضافة ضغط هنا لاحقاً
        showWarning('الضغط غير متاح حالياً', 'سيتم حفظ النسخة بدون ضغط');
      }

      // إنشاء ملف للتحميل
      const blob = new Blob([jsonData], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      
      // إنشاء رابط التحميل
      const link = document.createElement('a');
      link.href = url;
      link.download = `farm-backup-${new Date().toISOString().split('T')[0]}.json`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);

      showSuccess(
        'تم إنشاء النسخة الاحتياطية بنجاح', 
        `تم حفظ ${totalRecords} سجل في النسخة الاحتياطية`
      );

      return {
        success: true,
        recordCount: totalRecords,
        size: blob.size
      };

    } catch (error) {
      console.error('خطأ في إنشاء النسخة الاحتياطية:', error);
      showError(
        'فشل في إنشاء النسخة الاحتياطية', 
        'حدث خطأ أثناء إنشاء النسخة الاحتياطية'
      );
      return { success: false };
    } finally {
      setIsBackingUp(false);
    }
  }, [showSuccess, showError, showInfo, showWarning]);

  // استعادة نسخة احتياطية
  const restoreBackup = useCallback(async (file: File) => {
    try {
      setIsRestoring(true);
      showInfo('جاري استعادة النسخة الاحتياطية', 'يرجى الانتظار...');

      // قراءة محتوى الملف
      const fileContent = await new Promise<string>((resolve, reject) => {
        const reader = new FileReader();
        reader.onload = (e) => resolve(e.target?.result as string);
        reader.onerror = () => reject(new Error('فشل في قراءة الملف'));
        reader.readAsText(file);
      });

      // تحليل JSON
      let backupData;
      try {
        backupData = JSON.parse(fileContent);
      } catch {
        throw new Error('تنسيق الملف غير صحيح');
      }

      // التحقق من صحة النسخة الاحتياطية
      if (!backupData.exportDate && !backupData.backupInfo) {
        throw new Error('الملف ليس نسخة احتياطية صحيحة');
      }

      // التحقق من توافق الإصدار
      if (backupData.version && backupData.version > 2) {
        showWarning(
          'إصدار النسخة الاحتياطية أحدث', 
          'قد تواجه مشاكل في التوافق'
        );
      }

      // حساب عدد السجلات المراد استعادتها
      let totalRecords = 0;
      Object.keys(backupData).forEach(key => {
        if (Array.isArray(backupData[key])) {
          totalRecords += backupData[key].length;
        }
      });

      // تأكيد الاستعادة
      const confirmRestore = window.confirm(
        `هذا سيحذف جميع البيانات الحالية ويستعيد ${totalRecords} سجل من النسخة الاحتياطية. هل أنت متأكد؟`
      );

      if (!confirmRestore) {
        showInfo('تم إلغاء الاستعادة', 'لم يتم تغيير أي بيانات');
        return { success: false, cancelled: true };
      }

      // استعادة البيانات
      await enhancedDB.importAllData(backupData);

      showSuccess(
        'تم استعادة النسخة الاحتياطية بنجاح', 
        `تم استعادة ${totalRecords} سجل بنجاح`
      );

      // إعادة تحميل الصفحة لضمان تحديث جميع البيانات
      setTimeout(() => {
        window.location.reload();
      }, 2000);

      return {
        success: true,
        recordCount: totalRecords
      };

    } catch (error) {
      console.error('خطأ في استعادة النسخة الاحتياطية:', error);
      showError(
        'فشل في استعادة النسخة الاحتياطية', 
        error instanceof Error ? error.message : 'حدث خطأ غير معروف'
      );
      return { success: false };
    } finally {
      setIsRestoring(false);
    }
  }, [showSuccess, showError, showInfo, showWarning]);

  // تصدير بيانات محددة
  const exportSpecificData = useCallback(async (dataTypes: string[]) => {
    try {
      showInfo('جاري تصدير البيانات المحددة', 'يرجى الانتظار...');

      const allData = await enhancedDB.exportAllData();
      const selectedData: any = {
        exportDate: new Date().toISOString(),
        exportType: 'partial',
        includedTypes: dataTypes
      };

      // إضافة البيانات المطلوبة فقط
      dataTypes.forEach(type => {
        if (allData[type]) {
          selectedData[type] = allData[type];
        }
      });

      // حساب عدد السجلات
      let totalRecords = 0;
      dataTypes.forEach(type => {
        if (Array.isArray(selectedData[type])) {
          totalRecords += selectedData[type].length;
        }
      });

      // إنشاء ملف للتحميل
      const jsonData = JSON.stringify(selectedData, null, 2);
      const blob = new Blob([jsonData], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      
      const link = document.createElement('a');
      link.href = url;
      link.download = `farm-partial-export-${new Date().toISOString().split('T')[0]}.json`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);

      showSuccess(
        'تم تصدير البيانات بنجاح', 
        `تم تصدير ${totalRecords} سجل من ${dataTypes.length} نوع`
      );

      return { success: true, recordCount: totalRecords };

    } catch (error) {
      console.error('خطأ في تصدير البيانات:', error);
      showError('فشل في تصدير البيانات', 'حدث خطأ أثناء تصدير البيانات');
      return { success: false };
    }
  }, [showInfo, showSuccess, showError]);

  // التحقق من سلامة البيانات
  const verifyDataIntegrity = useCallback(async () => {
    try {
      showInfo('جاري فحص سلامة البيانات', 'يرجى الانتظار...');

      const issues: string[] = [];
      
      // فحص المناطق
      const zones = await enhancedDB.getZones();
      zones.forEach(zone => {
        if (!zone.name || !zone.status) {
          issues.push(`منطقة غير مكتملة: ${zone.id}`);
        }
        if (zone.valves) {
          zone.valves.forEach(valve => {
            if (valve.zoneId !== zone.id) {
              issues.push(`محبس غير صحيح في المنطقة: ${zone.id}`);
            }
          });
        }
      });

      // فحص الموظفين
      const employees = await enhancedDB.getEmployees();
      employees.forEach(employee => {
        if (!employee.name || !employee.position) {
          issues.push(`موظف غير مكتمل: ${employee.id}`);
        }
        if (employee.salary < 0) {
          issues.push(`راتب غير صحيح للموظف: ${employee.name}`);
        }
      });

      // فحص المخزون
      const inventory = await enhancedDB.getInventoryItems();
      inventory.forEach(item => {
        if (!item.name || item.quantity < 0) {
          issues.push(`مادة غير صحيحة في المخزون: ${item.id}`);
        }
      });

      if (issues.length === 0) {
        showSuccess('البيانات سليمة', 'جميع البيانات في حالة جيدة');
      } else {
        showWarning(
          `تم العثور على ${issues.length} مشكلة`, 
          'يرجى مراجعة البيانات وإصلاح المشاكل'
        );
        console.warn('مشاكل سلامة البيانات:', issues);
      }

      return { success: true, issues };

    } catch (error) {
      console.error('خطأ في فحص سلامة البيانات:', error);
      showError('فشل في فحص البيانات', 'حدث خطأ أثناء فحص سلامة البيانات');
      return { success: false, issues: [] };
    }
  }, [showInfo, showSuccess, showWarning, showError]);

  return {
    isBackingUp,
    isRestoring,
    createBackup,
    restoreBackup,
    exportSpecificData,
    verifyDataIntegrity
  };
};