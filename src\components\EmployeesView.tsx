import React, { useState, useEffect } from 'react';
import { Users, Plus, Search, UserPlus, DollarSign, Calendar, Phone } from 'lucide-react';
import { Employee } from '../types';

interface EmployeesViewProps {
  employees: Employee[];
  onEmployeeUpdate: (employeeId: string, updatedEmployee: Employee) => void;
  onEmployeeAdd: (employee: Omit<Employee, 'id'>) => void;
  onEmployeeDelete: (employeeId: string) => void;
}

const EmployeesView: React.FC<EmployeesViewProps> = ({ 
  employees, 
  onEmployeeUpdate, 
  onEmployeeAdd, 
  onEmployeeDelete 
}) => {
  const [showAddModal, setShowAddModal] = useState(false);
  const [editingEmployee, setEditingEmployee] = useState<Employee | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<'all' | 'active' | 'inactive'>('all');

  const filteredEmployees = employees.filter(emp =>
    statusFilter === 'all' || emp.status === statusFilter
  );

  const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    const formData = new FormData(e.currentTarget);
    
    const employeeData = {
      name: formData.get('name') as string,
      position: formData.get('position') as string,
      phone: formData.get('phone') as string,
      salary: parseFloat(formData.get('salary') as string),
      hireDate: formData.get('hireDate') as string,
      status: formData.get('status') as 'active' | 'inactive',
      notes: formData.get('notes') as string,
      createdAt: new Date().toISOString()
    };

    if (editingEmployee) {
      onEmployeeUpdate(editingEmployee.id.toString(), { ...editingEmployee, ...employeeData });
      setEditingEmployee(null);
    } else {
      onEmployeeAdd(employeeData);
      setShowAddModal(false);
    }
  };

  const getStatusColor = (status: string) => {
    return status === 'active' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800';
  };

  const getStatusText = (status: string) => {
    return status === 'active' ? 'نشط' : 'غير نشط';
  };

  const formatCurrency = (amount: number) => new Intl.NumberFormat('ar-JO', { style: 'currency', currency: 'JOD' }).format(amount);

  // حساب الإحصائيات
  const totalEmployees = employees.length;
  const activeEmployees = employees.filter(emp => emp.status === 'active').length;
  const totalSalary = employees.reduce((sum, emp) => sum + emp.salary, 0);
  const avgSalary = totalEmployees > 0 ? totalSalary / totalEmployees : 0;

  return (
    <div className="min-h-screen bg-gradient-to-br from-indigo-50 via-green-50 to-blue-100 p-6 space-y-6">
      {/* Header مع التدرج الأخضر */}
      <div className="bg-gradient-to-r from-indigo-700 via-blue-600 to-green-600 text-white p-6 shadow-2xl rounded-b-3xl">
        <div className="max-w-7xl mx-auto">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4 space-x-reverse">
              <div className="bg-white/30 p-3 rounded-lg shadow-lg border-2 border-indigo-300">
                <Users className="h-8 w-8" />
              </div>
              <div>
                <h1 className="text-3xl font-bold text-white drop-shadow-lg">إدارة الموظفين</h1>
                <p className="text-indigo-100 mt-1">نظام إدارة الموظفين والرواتب</p>
              </div>
            </div>
            <button
              onClick={() => setShowAddModal(true)}
              className="bg-gradient-to-r from-green-500 to-blue-500 text-white px-6 py-3 rounded-xl hover:from-blue-600 hover:to-green-600 transition-all duration-300 flex items-center gap-2 shadow-xl hover:shadow-2xl transform hover:scale-105 font-arabic font-medium tracking-wide"
            >
              <Plus size={20} />
              <span>إضافة موظف</span>
            </button>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto p-6">
        {/* شريط التنقل */}
        <div className="bg-white rounded-xl shadow-sm p-4 mb-6">
          <div className="flex items-center justify-center space-x-4 space-x-reverse">
            <button 
              onClick={() => alert(`إجمالي عدد الموظفين: ${totalEmployees}`)}
              className="bg-emerald-600 text-white px-6 py-3 rounded-lg font-semibold flex items-center space-x-2 space-x-reverse hover:bg-emerald-700 transition-colors"
            >
              <Users size={20} />
              <span>إدارة الموظفين</span>
            </button>
            <button 
              onClick={() => alert(`إجمالي الرواتب: ${formatCurrency(totalSalary)}\nمتوسط الراتب: ${formatCurrency(avgSalary)}`)}
              className="bg-blue-600 text-white px-6 py-3 rounded-lg font-semibold flex items-center space-x-2 space-x-reverse hover:bg-blue-700 transition-colors"
            >
              <DollarSign size={20} />
              <span>إدارة الرواتب</span>
            </button>
            <button 
              onClick={() => {
                const phoneNumbers = employees.map(emp => emp.phone).join('\n');
                alert(`أرقام هواتف الموظفين:\n${phoneNumbers}`);
              }}
              className="bg-orange-600 text-white px-6 py-3 rounded-lg font-semibold flex items-center space-x-2 space-x-reverse hover:bg-orange-700 transition-colors"
            >
              <Calendar size={20} />
              <span>سجل الحضور</span>
            </button>
            <button 
              onClick={() => setShowAddModal(true)}
              className="bg-green-600 text-white px-6 py-3 rounded-lg font-semibold flex items-center space-x-2 space-x-reverse hover:bg-green-700 transition-colors"
            >
              <UserPlus size={20} />
              <span>إضافة موظف</span>
            </button>
          </div>
        </div>

        {/* بطاقات الإحصائيات */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          <div onClick={() => setStatusFilter('all')} className={`cursor-pointer bg-white rounded-2xl shadow-2xl p-6 border-2 border-emerald-100 transition-all duration-200 ${statusFilter === 'all' ? 'ring-4 ring-emerald-300' : ''}`}>
            <div className="flex items-center justify-between">
              <div>
                <p className="text-blue-700 text-sm font-medium">إجمالي الموظفين</p>
                <p className="text-3xl font-bold text-green-700">{totalEmployees}</p>
              </div>
              <div className="bg-blue-100 p-3 rounded-lg">
                <Users className="h-8 w-8 text-blue-600" />
              </div>
            </div>
          </div>

          <div onClick={() => setStatusFilter('active')} className={`cursor-pointer bg-white rounded-2xl shadow-2xl p-6 border-2 border-green-100 transition-all duration-200 ${statusFilter === 'active' ? 'ring-4 ring-green-300' : ''}`}>
            <div className="flex items-center justify-between">
              <div>
                <p className="text-green-700 text-sm font-medium">الموظفين النشطين</p>
                <p className="text-3xl font-bold text-blue-700">{activeEmployees}</p>
              </div>
              <div className="bg-green-100 p-3 rounded-lg">
                <UserPlus className="h-8 w-8 text-green-600" />
              </div>
            </div>
          </div>

          <div onClick={() => setStatusFilter('inactive')} className={`cursor-pointer bg-white rounded-2xl shadow-2xl p-6 border-2 border-gray-100 transition-all duration-200 ${statusFilter === 'inactive' ? 'ring-4 ring-gray-300' : ''}`}>
            <div className="flex items-center justify-between">
              <div>
                <p className="text-orange-700 text-sm font-medium">إجمالي الرواتب</p>
                <p className="text-3xl font-bold text-green-700">{formatCurrency(totalSalary)}</p>
              </div>
              <div className="bg-orange-100 p-3 rounded-lg">
                <DollarSign className="h-8 w-8 text-orange-600" />
              </div>
            </div>
          </div>
        </div>

        {/* شريط البحث */}
        <div className="bg-white rounded-2xl shadow-xl p-4 mb-6 border-2 border-indigo-100">
          <div className="flex items-center justify-between">
            <div className="relative flex-1 max-w-md">
              <input
                type="text"
                placeholder="البحث في الموظفين..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
              />
              <Search className="absolute left-3 top-3 h-5 w-5 text-gray-400" />
            </div>
            <button
              onClick={() => setShowAddModal(true)}
              className="bg-gradient-to-r from-green-500 to-blue-500 text-white px-6 py-3 rounded-xl hover:from-blue-600 hover:to-green-600 transition-all duration-300 flex items-center gap-2 shadow-xl hover:shadow-2xl transform hover:scale-105 font-arabic font-medium tracking-wide"
            >
              <Plus size={20} />
              <span>إضافة موظف جديد</span>
            </button>
          </div>
        </div>

        {/* جدول الموظفين */}
        <div className="bg-white rounded-3xl shadow-2xl border-2 border-indigo-100 overflow-hidden">
          <div className="px-6 py-4 border-b border-indigo-200">
            <h3 className="text-lg font-bold text-indigo-800 drop-shadow">قائمة الموظفين</h3>
          </div>

          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-indigo-50">
                <tr>
                  <th className="px-6 py-4 text-right text-sm font-semibold text-indigo-700">الموظف</th>
                  <th className="px-6 py-4 text-right text-sm font-semibold text-indigo-700">المنصب</th>
                  <th className="px-6 py-4 text-right text-sm font-semibold text-indigo-700">الهاتف</th>
                  <th className="px-6 py-4 text-right text-sm font-semibold text-indigo-700">الراتب</th>
                  <th className="px-6 py-4 text-right text-sm font-semibold text-indigo-700">تاريخ التعيين</th>
                  <th className="px-6 py-4 text-right text-sm font-semibold text-indigo-700">الحالة</th>
                  <th className="px-6 py-4 text-right text-sm font-semibold text-indigo-700">الإجراءات</th>
                </tr>
              </thead>
              <tbody className="divide-y divide-indigo-100">
                {filteredEmployees.map((employee) => (
                  <tr key={employee.id} className="hover:bg-blue-50 transition-colors">
                    <td className="px-6 py-4">
                      <div className="flex items-center">
                        <div className="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center">
                          <span className="text-green-600 font-semibold">
                            {employee.name.charAt(0)}
                          </span>
                        </div>
                        <div className="mr-4">
                          <div className="text-sm font-medium text-indigo-900">{employee.name}</div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 text-sm text-blue-900">{employee.position}</td>
                    <td className="px-6 py-4 text-sm text-blue-900">{employee.phone}</td>
                    <td className="px-6 py-4 text-sm font-medium text-green-900">{formatCurrency(employee.salary)}</td>
                    <td className="px-6 py-4 text-sm text-indigo-900">
                      {new Date(employee.hireDate).toLocaleDateString('ar-SA')}
                    </td>
                    <td className="px-6 py-4">
                      <span className={`px-3 py-1 rounded-full text-xs font-medium ${getStatusColor(employee.status)}`}>
                        {getStatusText(employee.status)}
                      </span>
                    </td>
                    <td className="px-6 py-4">
                      <div className="flex items-center space-x-2 space-x-reverse">
                        <button
                          onClick={() => setEditingEmployee(employee)}
                          className="bg-blue-100 text-blue-600 px-3 py-1 rounded-lg text-xs font-medium hover:bg-blue-200 transition-colors"
                        >
                          تعديل
                        </button>
                        <button
                          onClick={() => onEmployeeDelete(employee.id.toString())}
                          className="bg-red-100 text-red-600 px-3 py-1 rounded-lg text-xs font-medium hover:bg-red-200 transition-colors"
                        >
                          حذف
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>

        {/* نافذة إضافة/تعديل موظف */}
        {(showAddModal || editingEmployee) && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
            <div className="bg-white rounded-xl shadow-2xl max-w-md w-full">
              <div className="bg-emerald-600 text-white p-6 rounded-t-xl">
                <h2 className="text-xl font-bold">
                  {editingEmployee ? 'تعديل موظف' : 'إضافة موظف جديد'}
                </h2>
              </div>
              
              <form onSubmit={handleSubmit} className="p-6 space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">الاسم الكامل</label>
                  <input
                    type="text"
                    name="name"
                    defaultValue={editingEmployee?.name}
                    required
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">المنصب</label>
                  <input
                    type="text"
                    name="position"
                    defaultValue={editingEmployee?.position}
                    required
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">رقم الهاتف</label>
                  <input
                    type="tel"
                    name="phone"
                    defaultValue={editingEmployee?.phone}
                    required
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">الراتب الشهري</label>
                  <input
                    type="number"
                    name="salary"
                    defaultValue={editingEmployee?.salary || ''}
                    required
                    min="0"
                    step="0.01"
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">تاريخ التعيين</label>
                  <input
                    type="date"
                    name="hireDate"
                    defaultValue={editingEmployee?.hireDate || ''}
                    required
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">الحالة</label>
                  <select
                    name="status"
                    defaultValue={editingEmployee?.status || 'active'}
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
                  >
                    <option value="active">نشط</option>
                    <option value="inactive">غير نشط</option>
                  </select>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">ملاحظات</label>
                  <textarea
                    name="notes"
                    defaultValue={editingEmployee?.notes}
                    rows={3}
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
                  />
                </div>
                
                <div className="flex gap-3 pt-4">
                  <button
                    type="submit"
                    className="flex-1 bg-emerald-600 text-white py-3 px-4 rounded-lg font-semibold hover:bg-emerald-700 transition-colors"
                  >
                    {editingEmployee ? 'تحديث' : 'إضافة'}
                  </button>
                  <button
                    type="button"
                    onClick={() => {
                      setShowAddModal(false);
                      setEditingEmployee(null);
                    }}
                    className="flex-1 bg-gray-200 text-gray-700 py-3 px-4 rounded-lg font-semibold hover:bg-gray-300 transition-colors"
                  >
                    إلغاء
                  </button>
                </div>
              </form>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default EmployeesView;