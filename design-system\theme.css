/* ========================================
   نظام التصميم - Lettuce Farm Management System
   ======================================== */

/* ========================================
   1. استيراد الخطوط العربية
   ======================================== */
@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@200;300;400;500;600;700;800;900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Tajawal:wght@200;300;400;500;700;800;900&display=swap');

/* ========================================
   2. متغيرات CSS الأساسية
   ======================================== */
:root {
  /* لوحة الألوان الأساسية */
  --primary-color: #059669;
  --primary-dark: #047857;
  --primary-light: #10b981;
  --secondary-color: #1e40af;
  --accent-color: #f59e0b;
  --success-color: #10b981;
  --warning-color: #f59e0b;
  --error-color: #ef4444;
  
  /* ألوان الخلفية */
  --background-color: #f8fafc;
  --surface-color: #ffffff;
  
  /* ألوان النصوص */
  --text-primary: #1e293b;
  --text-secondary: #64748b;
  
  /* ألوان الحدود */
  --border-color: #e2e8f0;
  
  /* ألوان الحالة */
  --status-active-bg: #dcfce7;
  --status-active-text: #166534;
  --status-inactive-bg: #f1f5f9;
  --status-inactive-text: #1e293b;
  --status-warning-bg: #fef3c7;
  --status-warning-text: #92400e;
  --status-error-bg: #fee2e2;
  --status-error-text: #991b1b;
}

/* ========================================
   3. إعدادات CSS الأساسية
   ======================================== */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Cairo', 'Tajawal', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  background-color: var(--background-color);
  color: var(--text-primary);
  direction: rtl;
  text-align: right;
  line-height: 1.6;
}

/* ========================================
   4. شريط التمرير المخصص
   ======================================== */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* ========================================
   5. المؤثرات الحركية والانتقالات
   ======================================== */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideIn {
  from {
    transform: translateX(100%);
  }
  to {
    transform: translateX(0);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.fade-in {
  animation: fadeIn 0.3s ease-out;
}

.slide-in {
  animation: slideIn 0.3s ease-out;
}

.pulse {
  animation: pulse 2s infinite;
}

/* ========================================
   6. أنماط الأزرار
   ======================================== */
.btn-primary {
  background-color: #059669;
  color: white;
  font-weight: 500;
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  transition: all 0.2s;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  border: none;
  cursor: pointer;
  font-size: 0.875rem;
  line-height: 1.25rem;
}

.btn-primary:hover {
  background-color: #047857;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.btn-secondary {
  background-color: #2563eb;
  color: white;
  font-weight: 500;
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  transition: all 0.2s;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  border: none;
  cursor: pointer;
  font-size: 0.875rem;
  line-height: 1.25rem;
}

.btn-secondary:hover {
  background-color: #1d4ed8;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.btn-success {
  background-color: #16a34a;
  color: white;
  font-weight: 500;
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  transition: all 0.2s;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  border: none;
  cursor: pointer;
  font-size: 0.875rem;
  line-height: 1.25rem;
}

.btn-success:hover {
  background-color: #15803d;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.btn-warning {
  background-color: #eab308;
  color: white;
  font-weight: 500;
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  transition: all 0.2s;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  border: none;
  cursor: pointer;
  font-size: 0.875rem;
  line-height: 1.25rem;
}

.btn-warning:hover {
  background-color: #ca8a04;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.btn-danger {
  background-color: #dc2626;
  color: white;
  font-weight: 500;
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  transition: all 0.2s;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  border: none;
  cursor: pointer;
  font-size: 0.875rem;
  line-height: 1.25rem;
}

.btn-danger:hover {
  background-color: #b91c1c;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.btn-outline {
  border: 2px solid #d1d5db;
  color: #374151;
  font-weight: 500;
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  transition: all 0.2s;
  background-color: transparent;
  cursor: pointer;
  font-size: 0.875rem;
  line-height: 1.25rem;
}

.btn-outline:hover {
  border-color: #9ca3af;
  color: #111827;
}

/* ========================================
   7. أنماط البطاقات
   ======================================== */
.card {
  background-color: white;
  border-radius: 0.75rem;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  border: 1px solid #e5e7eb;
  padding: 1.5rem;
}

.card-hover {
  transition: box-shadow 0.2s;
}

.card-hover:hover {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.dashboard-card {
  background-color: white;
  border-radius: 0.75rem;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  border: 1px solid #e5e7eb;
  padding: 1.5rem;
  transition: all 0.2s;
}

.dashboard-card:hover {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.zone-card {
  background-color: white;
  border-radius: 0.75rem;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  border: 1px solid #e5e7eb;
  padding: 1rem;
  transition: all 0.2s;
  cursor: pointer;
}

.zone-card:hover {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

/* ========================================
   8. أنماط حقول الإدخال
   ======================================== */
.input-field {
  width: 100%;
  padding: 0.5rem 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 0.5rem;
  transition: all 0.2s;
  font-size: 0.875rem;
  line-height: 1.25rem;
}

.input-field:focus {
  outline: none;
  border-color: #059669;
  box-shadow: 0 0 0 3px rgba(5, 150, 105, 0.1);
}

/* ========================================
   9. أنماط النوافذ المنبثقة
   ======================================== */
.modal-overlay {
  position: fixed;
  inset: 0;
  background-color: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(4px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 50;
  padding: 1rem;
}

.modal-content {
  background-color: white;
  border-radius: 1rem;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  padding: 1.5rem;
  width: 100%;
  max-width: 32rem;
  margin: 0 1rem;
  max-height: 90vh;
  overflow-y: auto;
  transform: translateZ(0);
  transition: all 0.3s;
}

/* ========================================
   10. أنماط حالات الحالة
   ======================================== */
.status-active {
  background-color: var(--status-active-bg);
  color: var(--status-active-text);
  padding: 0.25rem 0.5rem;
  border-radius: 9999px;
  font-size: 0.875rem;
  font-weight: 500;
}

.status-inactive {
  background-color: var(--status-inactive-bg);
  color: var(--status-inactive-text);
  padding: 0.25rem 0.5rem;
  border-radius: 9999px;
  font-size: 0.875rem;
  font-weight: 500;
}

.status-warning {
  background-color: var(--status-warning-bg);
  color: var(--status-warning-text);
  padding: 0.25rem 0.5rem;
  border-radius: 9999px;
  font-size: 0.875rem;
  font-weight: 500;
}

.status-error {
  background-color: var(--status-error-bg);
  color: var(--status-error-text);
  padding: 0.25rem 0.5rem;
  border-radius: 9999px;
  font-size: 0.875rem;
  font-weight: 500;
}

/* ========================================
   11. أنماط الجداول
   ======================================== */
.table-container {
  overflow-x: auto;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  border-radius: 0.5rem;
  border: 1px solid #e5e7eb;
}

.table {
  min-width: 100%;
  border-collapse: collapse;
}

.table-header {
  background-color: #f9fafb;
}

.table-header-cell {
  padding: 0.75rem 1.5rem;
  text-align: right;
  font-size: 0.75rem;
  font-weight: 500;
  color: #6b7280;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.table-body {
  background-color: white;
}

.table-row {
  transition: background-color 0.15s;
}

.table-row:hover {
  background-color: #f9fafb;
}

.table-cell {
  padding: 1rem 1.5rem;
  white-space: nowrap;
  font-size: 0.875rem;
  color: #111827;
}

/* ========================================
   12. أنماط التنقل
   ======================================== */
.nav-item {
  display: flex;
  align-items: center;
  padding: 0.75rem 1rem;
  color: #374151;
  border-radius: 0.5rem;
  transition: all 0.2s;
  cursor: pointer;
  text-decoration: none;
}

.nav-item:hover {
  background-color: #ecfdf5;
  color: #059669;
}

.nav-item.active {
  background-color: #d1fae5;
  color: #059669;
  font-weight: 500;
}

.nav-icon {
  width: 1.25rem;
  height: 1.25rem;
  margin-left: 0.75rem;
}

/* ========================================
   13. أنماط لوحة التحكم
   ======================================== */
.dashboard-stat {
  font-size: 1.875rem;
  font-weight: 700;
  color: #111827;
}

.dashboard-label {
  font-size: 0.875rem;
  font-weight: 500;
  color: #6b7280;
  margin-top: 0.25rem;
}

/* ========================================
   14. أنماط البطاقات الزراعية
   ======================================== */
.zone-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 0.75rem;
}

.zone-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: #111827;
}

.zone-status {
  font-size: 0.875rem;
  font-weight: 500;
}

.zone-details {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  font-size: 0.875rem;
  color: #4b5563;
}

/* ========================================
   15. التصميم المتجاوب
   ======================================== */
@media (max-width: 768px) {
  .modal-content {
    max-width: 24rem;
    margin: 0 0.5rem;
  }
  
  .card {
    padding: 1rem;
  }
  
  .table-container {
    font-size: 0.75rem;
  }
  
  .table-cell {
    padding: 0.75rem;
  }
}

/* ========================================
   16. أنماط إضافية للخلفيات المتدرجة
   ======================================== */
.bg-gradient-primary {
  background: linear-gradient(135deg, #059669 0%, #10b981 100%);
}

.bg-gradient-secondary {
  background: linear-gradient(135deg, #2563eb 0%, #3b82f6 100%);
}

.bg-gradient-success {
  background: linear-gradient(135deg, #16a34a 0%, #22c55e 100%);
}

.bg-gradient-warning {
  background: linear-gradient(135deg, #eab308 0%, #f59e0b 100%);
}

.bg-gradient-danger {
  background: linear-gradient(135deg, #dc2626 0%, #ef4444 100%);
}

/* ========================================
   17. أنماط الظلال
   ======================================== */
.shadow-sm {
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
}

.shadow-md {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.shadow-lg {
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
}

.shadow-xl {
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
}

.shadow-2xl {
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
}

/* ========================================
   18. أنماط الحواف
   ======================================== */
.rounded-sm {
  border-radius: 0.125rem;
}

.rounded {
  border-radius: 0.25rem;
}

.rounded-md {
  border-radius: 0.375rem;
}

.rounded-lg {
  border-radius: 0.5rem;
}

.rounded-xl {
  border-radius: 0.75rem;
}

.rounded-2xl {
  border-radius: 1rem;
}

.rounded-3xl {
  border-radius: 1.5rem;
}

.rounded-full {
  border-radius: 9999px;
}

/* ========================================
   19. أنماط التباعد
   ======================================== */
.space-y-1 > * + * {
  margin-top: 0.25rem;
}

.space-y-2 > * + * {
  margin-top: 0.5rem;
}

.space-y-3 > * + * {
  margin-top: 0.75rem;
}

.space-y-4 > * + * {
  margin-top: 1rem;
}

.space-y-6 > * + * {
  margin-top: 1.5rem;
}

.space-y-8 > * + * {
  margin-top: 2rem;
}

/* ========================================
   20. أنماط النصوص
   ======================================== */
.text-xs {
  font-size: 0.75rem;
  line-height: 1rem;
}

.text-sm {
  font-size: 0.875rem;
  line-height: 1.25rem;
}

.text-base {
  font-size: 1rem;
  line-height: 1.5rem;
}

.text-lg {
  font-size: 1.125rem;
  line-height: 1.75rem;
}

.text-xl {
  font-size: 1.25rem;
  line-height: 1.75rem;
}

.text-2xl {
  font-size: 1.5rem;
  line-height: 2rem;
}

.text-3xl {
  font-size: 1.875rem;
  line-height: 2.25rem;
}

.text-4xl {
  font-size: 2.25rem;
  line-height: 2.5rem;
}

/* ========================================
   21. أنماط أوزان الخطوط
   ======================================== */
.font-extralight {
  font-weight: 200;
}

.font-light {
  font-weight: 300;
}

.font-normal {
  font-weight: 400;
}

.font-medium {
  font-weight: 500;
}

.font-semibold {
  font-weight: 600;
}

.font-bold {
  font-weight: 700;
}

.font-extrabold {
  font-weight: 800;
}

.font-black {
  font-weight: 900;
} 