export interface ElectronAPI {
  // ===== وظائف الأصناف =====
  getAllItems: () => Promise<{ success: boolean; data?: any[]; error?: string }>;
  addItem: (name: string, category?: string, description?: string) => Promise<{ success: boolean; id?: number; error?: string }>;
  updateItem: (id: number, name: string, category: string, description: string) => Promise<{ success: boolean; changes?: number; error?: string }>;
  deleteItem: (id: number) => Promise<{ success: boolean; changes?: number; error?: string }>;

  // ===== وظائف المخزون =====
  getInventory: () => Promise<{ success: boolean; data?: any[]; error?: string }>;
  updateInventory: (itemId: number, pallets: number, boxes: number, totalBoxes: number, notes?: string) => Promise<{ success: boolean; changes?: number; error?: string }>;

  // ===== وظائف الحركات =====
  getMovements: () => Promise<{ success: boolean; data?: any[]; error?: string }>;
  addMovement: (itemId: number, type: string, quantity: number, notes?: string) => Promise<{ success: boolean; id?: number; error?: string }>;
  clearMovements: () => Promise<{ success: boolean; changes?: number; error?: string }>;

  // ===== وظائف عامة =====
  getStats: () => Promise<{ success: boolean; data?: any; error?: string }>;
  getAppVersion: () => Promise<string>;

  isElectron: boolean;
}

declare global {
  interface Window {
    electronAPI: ElectronAPI;
  }
}

export {};
