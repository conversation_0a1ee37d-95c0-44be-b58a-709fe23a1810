import React from 'react';
import { 
  Leaf, 
  Droplets, 
  Users, 
  ShoppingCart, 
  Package, 
  TrendingUp, 
  Calendar,
  AlertTriangle,
  CheckCircle,
  Clock
} from 'lucide-react';
import { DashboardStats } from '../types';

interface DashboardViewProps {
  stats: DashboardStats | null;
}

const DashboardView: React.FC<DashboardViewProps> = ({ stats }) => {
  if (!stats) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-emerald-600"></div>
      </div>
    );
  }

  const dashboardCards = [
    {
      title: 'المراحل النشطة',
      value: stats.activeZones,
      total: stats.totalZones,
      icon: Leaf,
      gradient: 'from-emerald-400 to-emerald-600',
      iconBg: 'bg-emerald-500',
      textColor: 'text-white',
      border: 'border-emerald-500'
    },
    {
      title: 'المراحل الجاهزة للحصاد',
      value: stats.harvestingZones,
      total: stats.totalZones,
      icon: CheckCircle,
      gradient: 'from-yellow-400 to-yellow-600',
      iconBg: 'bg-yellow-500',
      textColor: 'text-white',
      border: 'border-yellow-500'
    },
    {
      title: 'عمليات الري اليوم',
      value: stats.todayIrrigations,
      total: stats.pendingIrrigations + stats.todayIrrigations,
      icon: Droplets,
      gradient: 'from-blue-400 to-blue-600',
      iconBg: 'bg-blue-500',
      textColor: 'text-white',
      border: 'border-blue-500'
    },
    {
      title: 'الموظفين النشطين',
      value: stats.activeEmployees,
      total: stats.totalEmployees,
      icon: Users,
      gradient: 'from-purple-400 to-purple-600',
      iconBg: 'bg-purple-500',
      textColor: 'text-white',
      border: 'border-purple-500'
    },
    {
      title: 'المبيعات الشهرية',
      value: stats.monthlySales,
      total: stats.totalSales,
      icon: ShoppingCart,
      gradient: 'from-green-400 to-green-600',
      iconBg: 'bg-green-500',
      textColor: 'text-white',
      border: 'border-green-500',
      isCurrency: true
    },
    {
      title: 'العناصر منخفضة المخزون',
      value: stats.lowStockItems,
      total: 0,
      icon: AlertTriangle,
      gradient: 'from-red-400 to-red-600',
      iconBg: 'bg-red-500',
      textColor: 'text-white',
      border: 'border-red-500'
    }
  ];

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('ar-JO', {
      style: 'currency',
      currency: 'JOD'
    }).format(amount);
  };

  return (
    <div className="space-y-8">
      {/* عنوان الصفحة */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">لوحة التحكم</h1>
          <p className="text-gray-600 mt-2">نظرة عامة على حالة المزرعة</p>
        </div>
        <div className="text-right">
          <p className="text-sm text-gray-500">آخر تحديث</p>
          <p className="text-sm font-medium text-gray-900">
            {(() => {
              const now = new Date();
              const date = now.toLocaleDateString('en-GB');
              const time = now.toLocaleTimeString('en-GB', { hour: '2-digit', minute: '2-digit' });
              return `${date} ${time}`;
            })()}
          </p>
        </div>
      </div>

      {/* البطاقات الإحصائية الملونة */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
        {dashboardCards.map((card, index) => {
          const Icon = card.icon;
          return (
            <div
              key={index}
              className={`relative overflow-hidden rounded-3xl shadow-2xl border-t-4 ${card.border} bg-gradient-to-br ${card.gradient} p-8 flex items-center min-h-[160px] transition-transform duration-300 hover:scale-105 hover:shadow-emerald-400/40`}
            >
              <div className={`absolute -left-8 -top-8 w-32 h-32 ${card.iconBg} opacity-20 rounded-full`}></div>
              <div className="flex-1 z-10">
                <p className="text-xl font-bold text-white/90 mb-3 tracking-wide drop-shadow">{card.title}</p>
                <p className="text-5xl font-extrabold text-white drop-shadow-lg mb-2">
                  {card.isCurrency ? formatCurrency(card.value) : card.value}
                </p>
                {card.total > 0 && (
                  <p className="text-xs text-white/80 mt-1 font-semibold">
                    من أصل {card.total}
                  </p>
                )}
              </div>
              <div className="z-10">
                <div className={`w-20 h-20 flex items-center justify-center rounded-2xl ${card.iconBg} shadow-xl border-4 border-white/30`}>
                  <Icon className="w-12 h-12 text-white drop-shadow" />
                </div>
              </div>
            </div>
          );
        })}
      </div>

      {/* معلومات سريعة */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* حالة الري */}
        <div className="card shadow-lg rounded-2xl p-6 bg-white">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-900">حالة الري</h3>
            <Droplets className="w-5 h-5 text-blue-600" />
          </div>
          <div className="space-y-3">
            <div className="flex items-center justify-between p-3 bg-blue-50 rounded-lg">
              <div className="flex items-center gap-2">
                <Clock className="w-4 h-4 text-blue-600" />
                <span className="text-sm font-medium text-gray-700">الري المخطط له اليوم</span>
              </div>
              <span className="text-sm font-bold text-blue-600">{stats.pendingIrrigations}</span>
            </div>
            <div className="flex items-center justify-between p-3 bg-green-50 rounded-lg">
              <div className="flex items-center gap-2">
                <CheckCircle className="w-4 h-4 text-green-600" />
                <span className="text-sm font-medium text-gray-700">الري المكتمل اليوم</span>
              </div>
              <span className="text-sm font-bold text-green-600">{stats.todayIrrigations}</span>
            </div>
            <div className="flex items-center justify-between p-3 bg-purple-50 rounded-lg">
              <div className="flex items-center gap-2">
                <Package className="w-4 h-4 text-purple-600" />
                <span className="text-sm font-medium text-gray-700">إجمالي محابس الري</span>
              </div>
              <span className="text-sm font-bold text-purple-600">{stats.totalIrrigationValves}</span>
            </div>
          </div>
        </div>

        {/* المبيعات والإيرادات */}
        <div className="card shadow-lg rounded-2xl p-6 bg-white">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-900">المبيعات والإيرادات</h3>
            <TrendingUp className="w-5 h-5 text-green-600" />
          </div>
          <div className="space-y-3">
            <div className="flex items-center justify-between p-3 bg-green-50 rounded-lg">
              <div className="flex items-center gap-2">
                <ShoppingCart className="w-4 h-4 text-green-600" />
                <span className="text-sm font-medium text-gray-700">المبيعات الشهرية</span>
              </div>
              <span className="text-sm font-bold text-green-600">{formatCurrency(stats.monthlySales)}</span>
            </div>
            <div className="flex items-center justify-between p-3 bg-blue-50 rounded-lg">
              <div className="flex items-center gap-2">
                <TrendingUp className="w-4 h-4 text-blue-600" />
                <span className="text-sm font-medium text-gray-700">إجمالي المبيعات</span>
              </div>
              <span className="text-sm font-bold text-blue-600">{formatCurrency(stats.totalSales)}</span>
            </div>
            <div className="flex items-center justify-between p-3 bg-yellow-50 rounded-lg">
              <div className="flex items-center gap-2">
                <AlertTriangle className="w-4 h-4 text-yellow-600" />
                <span className="text-sm font-medium text-gray-700">السلف المعلقة</span>
              </div>
              <span className="text-sm font-bold text-yellow-600">{stats.pendingAdvances}</span>
            </div>
          </div>
        </div>
      </div>

      {/* رسالة ترحيب */}
      <div className="card bg-gradient-to-r from-emerald-50 to-teal-50 border-emerald-200 shadow-lg rounded-2xl p-6">
        <div className="flex items-center gap-4">
          <div className="w-12 h-12 bg-emerald-600 rounded-xl flex items-center justify-center">
            <Leaf className="w-6 h-6 text-white" />
          </div>
          <div>
            <h3 className="text-lg font-semibold text-emerald-800">مرحباً بك في نظام إدارة مزرعة الخس</h3>
            <p className="text-emerald-700 mt-1">
              يمكنك إدارة جميع جوانب المزرعة من هذه اللوحة المركزية
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DashboardView; 