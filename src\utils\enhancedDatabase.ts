// ===== نظام قاعدة البيانات المحسن مع IndexedDB =====

import {
  Zone,
  IrrigationValve,
  Spraying,
  Fertilization,
  InventoryItem,
  InventoryMovement,
  Employee,
  Supplier,
  Advance,
  LocalSale,
  IrrigationSchedule,
  Transaction
} from '../types';

export class EnhancedDatabase {
  private dbName = 'LettuceFarmDB';
  private version = 2;
  private db: IDBDatabase | null = null;

  // أسماء المخازن
  private stores = {
    zones: 'zones',
    sprayings: 'sprayings',
    fertilizations: 'fertilizations',
    inventory: 'inventory',
    inventoryMovements: 'inventoryMovements',
    employees: 'employees',
    suppliers: 'suppliers',
    advances: 'advances',
    sales: 'sales',
    irrigationSchedules: 'irrigationSchedules',
    transactions: 'transactions',
    settings: 'settings'
  };

  async init(): Promise<void> {
    return new Promise((resolve, reject) => {
      const request = indexedDB.open(this.dbName, this.version);

      request.onerror = () => {
        reject(new Error('فشل في فتح قاعدة البيانات'));
      };

      request.onsuccess = () => {
        this.db = request.result;
        resolve();
      };

      request.onupgradeneeded = (event) => {
        const db = (event.target as IDBOpenDBRequest).result;

        // إنشاء مخازن البيانات
        Object.values(this.stores).forEach(storeName => {
          if (!db.objectStoreNames.contains(storeName)) {
            const store = db.createObjectStore(storeName, { keyPath: 'id', autoIncrement: true });
            
            // إضافة فهارس للبحث السريع
            switch (storeName) {
              case 'zones':
                store.createIndex('status', 'status', { unique: false });
                store.createIndex('lettuceType', 'lettuceType', { unique: false });
                break;
              case 'employees':
                store.createIndex('status', 'status', { unique: false });
                store.createIndex('position', 'position', { unique: false });
                break;
              case 'inventory':
                store.createIndex('category', 'category', { unique: false });
                store.createIndex('quantity', 'quantity', { unique: false });
                break;
              case 'sprayings':
              case 'fertilizations':
                store.createIndex('date', 'date', { unique: false });
                store.createIndex('targetZone', 'targetZone', { unique: false });
                break;
              case 'sales':
                store.createIndex('date', 'date', { unique: false });
                store.createIndex('customerName', 'customerName', { unique: false });
                break;
              case 'advances':
                store.createIndex('employeeId', 'employeeId', { unique: false });
                store.createIndex('status', 'status', { unique: false });
                break;
            }
          }
        });
      };
    });
  }

  private async getStore(storeName: string, mode: IDBTransactionMode = 'readonly'): Promise<IDBObjectStore> {
    if (!this.db) {
      await this.init();
    }
    
    if (!this.db) {
      throw new Error('قاعدة البيانات غير متاحة');
    }

    const transaction = this.db.transaction([storeName], mode);
    return transaction.objectStore(storeName);
  }

  // ===== العمليات العامة =====
  
  async add<T>(storeName: string, data: Omit<T, 'id'>): Promise<number> {
    const store = await this.getStore(storeName, 'readwrite');
    const dataWithTimestamp = {
      ...data,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };
    
    return new Promise((resolve, reject) => {
      const request = store.add(dataWithTimestamp);
      request.onsuccess = () => resolve(request.result as number);
      request.onerror = () => reject(request.error);
    });
  }

  async update<T extends { id: number }>(storeName: string, data: T): Promise<void> {
    const store = await this.getStore(storeName, 'readwrite');
    const dataWithTimestamp = {
      ...data,
      updatedAt: new Date().toISOString()
    };
    
    return new Promise((resolve, reject) => {
      const request = store.put(dataWithTimestamp);
      request.onsuccess = () => resolve();
      request.onerror = () => reject(request.error);
    });
  }

  async delete(storeName: string, id: number): Promise<void> {
    console.log('🗑️ حذف من قاعدة البيانات:', storeName, 'ID:', id);
    const store = await this.getStore(storeName, 'readwrite');
    
    return new Promise((resolve, reject) => {
      const request = store.delete(id);
      request.onsuccess = () => {
        console.log('✅ تم الحذف من قاعدة البيانات بنجاح');
        resolve();
      };
      request.onerror = () => {
        console.error('❌ خطأ في حذف من قاعدة البيانات:', request.error);
        reject(request.error);
      };
    });
  }

  async getById<T>(storeName: string, id: number): Promise<T | null> {
    const store = await this.getStore(storeName);
    
    return new Promise((resolve, reject) => {
      const request = store.get(id);
      request.onsuccess = () => resolve(request.result || null);
      request.onerror = () => reject(request.error);
    });
  }

  async getAll<T>(storeName: string): Promise<T[]> {
    const store = await this.getStore(storeName);
    
    return new Promise((resolve, reject) => {
      const request = store.getAll();
      request.onsuccess = () => resolve(request.result);
      request.onerror = () => reject(request.error);
    });
  }

  async getByIndex<T>(storeName: string, indexName: string, value: any): Promise<T[]> {
    const store = await this.getStore(storeName);
    const index = store.index(indexName);
    
    return new Promise((resolve, reject) => {
      const request = index.getAll(value);
      request.onsuccess = () => resolve(request.result);
      request.onerror = () => reject(request.error);
    });
  }

  async count(storeName: string): Promise<number> {
    const store = await this.getStore(storeName);
    
    return new Promise((resolve, reject) => {
      const request = store.count();
      request.onsuccess = () => resolve(request.result);
      request.onerror = () => reject(request.error);
    });
  }

  // ===== وظائف خاصة بالمناطق =====
  
  async getZones(): Promise<Zone[]> {
    const zones = await this.getAll<Zone>(this.stores.zones);
    console.log('📊 getZones: تم العثور على', zones.length, 'مرحلة');
    return zones;
  }
  
  // إضافة البيانات التجريبية
  private async initializeSampleZones(): Promise<void> {
    const sampleZones = [
      {
        name: 'المرحلة الأولى',
        status: 'active' as const,
        plantingDate: '2024-01-15',
        harvestDate: '2024-03-15',
        lettuceType: 'iceberg' as const,
        irrigationStatus: 'scheduled' as const,
        lastIrrigation: '2024-01-20',
        nextIrrigation: '2024-01-25',
        notes: 'مرحلة تجريبية للخس الأيسبيرغ',
        valves: [
          { id: 1, zoneId: 1, valveNumber: 1, lettuceType: 'iceberg' as const, status: 'active' as const, lastIrrigation: '2024-01-20', nextIrrigation: '2024-01-25' },
          { id: 2, zoneId: 1, valveNumber: 2, lettuceType: 'iceberg' as const, status: 'inactive' as const, lastIrrigation: '2024-01-18', nextIrrigation: '2024-01-26' }
        ]
      },
      {
        name: 'المرحلة الثانية',
        status: 'active' as const,
        plantingDate: '2024-01-20',
        harvestDate: '2024-03-20',
        lettuceType: 'romaine' as const,
        irrigationStatus: 'completed' as const,
        lastIrrigation: '2024-01-25',
        nextIrrigation: '2024-01-30',
        notes: 'مرحلة خس رومين عالي الجودة',
        valves: [
          { id: 3, zoneId: 2, valveNumber: 1, lettuceType: 'romaine' as const, status: 'active' as const, lastIrrigation: '2024-01-25', nextIrrigation: '2024-01-30' },
          { id: 4, zoneId: 2, valveNumber: 2, lettuceType: 'romaine' as const, status: 'active' as const, lastIrrigation: '2024-01-24', nextIrrigation: '2024-01-31' }
        ]
      },
      {
        name: 'المرحلة الثالثة',
        status: 'active' as const,
        plantingDate: '2024-02-01',
        harvestDate: '2024-04-01',
        lettuceType: 'iceberg' as const,
        irrigationStatus: 'scheduled' as const,
        lastIrrigation: '2024-02-05',
        nextIrrigation: '2024-02-10',
        notes: 'مرحلة خس أيسبيرغ للتصدير',
        valves: [
          { id: 5, zoneId: 3, valveNumber: 1, lettuceType: 'iceberg' as const, status: 'active' as const, lastIrrigation: '2024-02-05', nextIrrigation: '2024-02-10' },
          { id: 6, zoneId: 3, valveNumber: 2, lettuceType: 'iceberg' as const, status: 'active' as const, lastIrrigation: '2024-02-04', nextIrrigation: '2024-02-11' },
          { id: 7, zoneId: 3, valveNumber: 3, lettuceType: 'iceberg' as const, status: 'inactive' as const, lastIrrigation: '2024-02-03', nextIrrigation: '2024-02-12' }
        ]
      },
      {
        name: 'المرحلة الرابعة',
        status: 'active' as const,
        plantingDate: '2024-02-10',
        harvestDate: '2024-04-10',
        lettuceType: 'romaine' as const,
        irrigationStatus: 'completed' as const,
        lastIrrigation: '2024-02-15',
        nextIrrigation: '2024-02-20',
        notes: 'مرحلة رومين للأسواق المحلية',
        valves: [
          { id: 8, zoneId: 4, valveNumber: 1, lettuceType: 'romaine' as const, status: 'active' as const, lastIrrigation: '2024-02-15', nextIrrigation: '2024-02-20' },
          { id: 9, zoneId: 4, valveNumber: 2, lettuceType: 'romaine' as const, status: 'inactive' as const, lastIrrigation: '2024-02-14', nextIrrigation: '2024-02-21' }
        ]
      }
    ];

    for (const zone of sampleZones) {
      await this.add<Zone>(this.stores.zones, zone);
    }
  }

  async addZone(zone: Omit<Zone, 'id'>): Promise<number> {
    // إنشاء محابس افتراضية إذا لم تكن موجودة
    const zoneWithValves = {
      ...zone,
      valves: zone.valves || [
        {
          id: Date.now() + 1,
          zoneId: 0, // سيتم تحديثه بعد إنشاء المرحلة
          valveNumber: 1,
          lettuceType: zone.lettuceType,
          status: 'inactive' as const,
          lastIrrigation: '',
          nextIrrigation: ''
        },
        {
          id: Date.now() + 2,
          zoneId: 0, // سيتم تحديثه بعد إنشاء المرحلة
          valveNumber: 2,
          lettuceType: zone.lettuceType,
          status: 'inactive' as const,
          lastIrrigation: '',
          nextIrrigation: ''
        }
      ]
    };

    const zoneId = await this.add<Zone>(this.stores.zones, zoneWithValves);
    
    // تحديث zoneId في المحابس
    if (zoneWithValves.valves) {
      const updatedZone = {
        ...zoneWithValves,
        id: zoneId,
        valves: zoneWithValves.valves.map(valve => ({
          ...valve,
          zoneId: zoneId
        }))
      };
      await this.update(this.stores.zones, updatedZone);
    }
    
    return zoneId;
  }

  async updateZone(zone: Zone): Promise<void> {
    return this.update(this.stores.zones, zone);
  }

  async deleteZone(id: number): Promise<void> {
    return this.delete(this.stores.zones, id);
  }

  async getActiveZones(): Promise<Zone[]> {
    return this.getByIndex<Zone>(this.stores.zones, 'status', 'active');
  }

  // ===== وظائف خاصة بالموظفين =====
  
  async getEmployees(): Promise<Employee[]> {
    return this.getAll<Employee>(this.stores.employees);
  }

  async addEmployee(employee: Omit<Employee, 'id'>): Promise<number> {
    return this.add<Employee>(this.stores.employees, employee);
  }

  async updateEmployee(employee: Employee): Promise<void> {
    return this.update(this.stores.employees, employee);
  }

  async deleteEmployee(id: number): Promise<void> {
    return this.delete(this.stores.employees, id);
  }

  async getActiveEmployees(): Promise<Employee[]> {
    return this.getByIndex<Employee>(this.stores.employees, 'status', 'active');
  }

  // ===== وظائف خاصة بالمخزون =====
  
  async getInventoryItems(): Promise<InventoryItem[]> {
    return this.getAll<InventoryItem>(this.stores.inventory);
  }

  async addInventoryItem(item: Omit<InventoryItem, 'id'>): Promise<number> {
    return this.add<InventoryItem>(this.stores.inventory, item);
  }

  async updateInventoryItem(item: InventoryItem): Promise<void> {
    return this.update(this.stores.inventory, item);
  }

  async deleteInventoryItem(id: number): Promise<void> {
    return this.delete(this.stores.inventory, id);
  }

  async getLowStockItems(): Promise<InventoryItem[]> {
    const items = await this.getInventoryItems();
    return items.filter(item => item.quantity <= (item.minQuantity || 0));
  }

  // ===== وظائف خاصة بالرش =====
  
  async getSprayings(): Promise<Spraying[]> {
    return this.getAll<Spraying>(this.stores.sprayings);
  }

  async addSpraying(spraying: Omit<Spraying, 'id'>): Promise<number> {
    return this.add<Spraying>(this.stores.sprayings, spraying);
  }

  async updateSpraying(spraying: Spraying): Promise<void> {
    return this.update(this.stores.sprayings, spraying);
  }

  async deleteSpraying(id: number): Promise<void> {
    return this.delete(this.stores.sprayings, id);
  }

  // ===== وظائف خاصة بالتسميد =====
  
  async getFertilizations(): Promise<Fertilization[]> {
    return this.getAll<Fertilization>(this.stores.fertilizations);
  }

  async addFertilization(fertilization: Omit<Fertilization, 'id'>): Promise<number> {
    return this.add<Fertilization>(this.stores.fertilizations, fertilization);
  }

  async updateFertilization(fertilization: Fertilization): Promise<void> {
    return this.update(this.stores.fertilizations, fertilization);
  }

  async deleteFertilization(id: number): Promise<void> {
    return this.delete(this.stores.fertilizations, id);
  }

  // ===== وظائف خاصة بالموردين =====
  
  async getSuppliers(): Promise<Supplier[]> {
    return this.getAll<Supplier>(this.stores.suppliers);
  }

  async addSupplier(supplier: Omit<Supplier, 'id'>): Promise<number> {
    return this.add<Supplier>(this.stores.suppliers, supplier);
  }

  async updateSupplier(supplier: Supplier): Promise<void> {
    return this.update(this.stores.suppliers, supplier);
  }

  async deleteSupplier(id: number): Promise<void> {
    return this.delete(this.stores.suppliers, id);
  }

  // ===== وظائف خاصة بالسلف =====
  
  async getAdvances(): Promise<Advance[]> {
    return this.getAll<Advance>(this.stores.advances);
  }

  async addAdvance(advance: Omit<Advance, 'id'>): Promise<number> {
    return this.add<Advance>(this.stores.advances, advance);
  }

  async updateAdvance(advance: Advance): Promise<void> {
    return this.update(this.stores.advances, advance);
  }

  async deleteAdvance(id: number): Promise<void> {
    return this.delete(this.stores.advances, id);
  }

  async getPendingAdvances(): Promise<Advance[]> {
    return this.getByIndex<Advance>(this.stores.advances, 'status', 'pending');
  }

  // ===== وظائف خاصة بالمبيعات =====
  
  async getSales(): Promise<LocalSale[]> {
    return this.getAll<LocalSale>(this.stores.sales);
  }

  async addSale(sale: Omit<LocalSale, 'id'>): Promise<number> {
    return this.add<LocalSale>(this.stores.sales, sale);
  }

  async updateSale(sale: LocalSale): Promise<void> {
    return this.update(this.stores.sales, sale);
  }

  async deleteSale(id: number): Promise<void> {
    return this.delete(this.stores.sales, id);
  }

  // ===== نسخ احتياطي واستعادة =====
  
  async exportAllData(): Promise<Record<string, unknown>> {
    const data: Record<string, unknown> = {};
    
    for (const [key, storeName] of Object.entries(this.stores)) {
      data[key] = await this.getAll(storeName);
    }
    
    return {
      ...data,
      exportDate: new Date().toISOString(),
      version: this.version
    };
  }

  async importAllData(data: Record<string, unknown>): Promise<void> {
    // حذف البيانات الحالية
    await this.clearAllData();
    
    // استيراد البيانات الجديدة
    for (const [key, storeName] of Object.entries(this.stores)) {
      if (data[key] && Array.isArray(data[key])) {
        for (const item of data[key]) {
          await this.add(storeName, item);
        }
      }
    }
  }

  async clearAllData(): Promise<void> {
    for (const storeName of Object.values(this.stores)) {
      const store = await this.getStore(storeName, 'readwrite');
      await new Promise<void>((resolve, reject) => {
        const request = store.clear();
        request.onsuccess = () => resolve();
        request.onerror = () => reject(request.error);
      });
    }
  }

  // ===== إحصائيات سريعة =====
  
  async getStats() {
    const [
      totalZones,
      activeZones,
      totalEmployees,
      activeEmployees,
      totalSales,
      lowStockItems,
      pendingAdvances
    ] = await Promise.all([
      this.count(this.stores.zones),
      this.getActiveZones().then(zones => zones.length),
      this.count(this.stores.employees),
      this.getActiveEmployees().then(employees => employees.length),
      this.count(this.stores.sales),
      this.getLowStockItems().then(items => items.length),
      this.getPendingAdvances().then(advances => advances.length)
    ]);

    return {
      totalZones,
      activeZones,
      totalEmployees,
      activeEmployees,
      totalSales,
      lowStockItems,
      pendingAdvances
    };
  }
}

// إنشاء مثيل واحد من قاعدة البيانات
export const enhancedDB = new EnhancedDatabase();