// نوع البيانات
import {
  getInventory as getDbInventory,
  getMovements as getDbMovements,
  convertDatabaseInventoryToLegacy,
  convertDatabaseMovementsToLegacy,
  isElectronAvailable
} from './database';
export interface InventoryItem {
  id: number;
  name: string;
  orders: number;
  boxes: number;
  location: string;
  date: string;
  totalBoxes: number;
  notes?: string;
}

export interface Movement {
  id: number;
  type: 'in' | 'out' | 'edit';
  itemName: string;
  quantity: number;
  date: string;
  time: string;
  notes?: string;
}

// التحقق من وجود Electron API
const isElectron = typeof window !== 'undefined' && window.electronAPI !== undefined;

// دالة موحدة للتحقق من Electron
export const isElectronAvailable = () => {
  return typeof window !== 'undefined' && 
         window.electronAPI !== undefined && 
         window.electronAPI.isElectron === true;
};

// وظائف حفظ وتحميل المخزون
export const saveInventoryData = async (data: InventoryItem[]): Promise<boolean> => {
  try {
    if (isElectron) {
      const result = await window.electronAPI.saveInventory(data);
      return result.success;
    } else {
      localStorage.setItem('inventory', JSON.stringify(data));
      return true;
    }
  } catch (error) {
    console.error('خطأ في حفظ المخزون:', error);
    return false;
  }
};

export const loadInventoryData = async (): Promise<InventoryItem[] | null> => {
  try {
    if (isElectronAvailable()) {
      // استخدام قاعدة البيانات الجديدة
      const dbInventory = await getDbInventory();
      return convertDatabaseInventoryToLegacy(dbInventory);
    } else {
      const saved = localStorage.getItem('inventory');
      return saved ? JSON.parse(saved) : null;
    }
  } catch (error) {
    console.error('خطأ في تحميل المخزون:', error);
    return null;
  }
};

// وظائف حفظ وتحميل الحركات
export const saveMovementsData = async (data: Movement[]): Promise<boolean> => {
  try {
    if (isElectron) {
      const result = await window.electronAPI.saveMovements(data);
      return result.success;
    } else {
      localStorage.setItem('movements', JSON.stringify(data));
      return true;
    }
  } catch (error) {
    console.error('خطأ في حفظ الحركات:', error);
    return false;
  }
};

export const loadMovementsData = async (): Promise<Movement[] | null> => {
  try {
    if (isElectronAvailable()) {
      // استخدام قاعدة البيانات الجديدة
      const dbMovements = await getDbMovements();
      return convertDatabaseMovementsToLegacy(dbMovements);
    } else {
      const saved = localStorage.getItem('movements');
      return saved ? JSON.parse(saved) : null;
    }
  } catch (error) {
    console.error('خطأ في تحميل الحركات:', error);
    return null;
  }
};

// التحقق من البيئة
export const getStorageInfo = async () => {
  if (isElectronAvailable()) {
    try {
      const version = await window.electronAPI.getAppVersion();
      return {
        type: 'sqlite',
        version: version,
        platform: 'desktop'
      };
    } catch (error) {
      return {
        type: 'sqlite',
        version: 'unknown',
        platform: 'desktop'
      };
    }
  } else {
    return {
      type: 'browser',
      version: navigator.userAgent,
      platform: 'web'
    };
  }
};
