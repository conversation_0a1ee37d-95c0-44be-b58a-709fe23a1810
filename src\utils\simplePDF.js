// مكتبة بسيطة لإنشاء PDF بالعربية
import { jsPDF } from 'jspdf';

// دالة لإنشاء PDF بسيط للمخزون
export const createSimpleInventoryPDF = async (movements, filename = 'تقرير-المخزون.pdf') => {
  try {
    console.log('🚀 إنشاء PDF بسيط للمخزون...');
    
    const pdf = new jsPDF('p', 'pt', 'a4');
    
    // إعداد الخط
    pdf.setFont('helvetica');
    
    // العنوان الرئيسي
    pdf.setFontSize(20);
    pdf.setTextColor(21, 128, 61);
    pdf.text('شركة الشفق للزراعة الحديثة', 400, 50, { align: 'center' });

    pdf.setFontSize(16);
    pdf.setTextColor(34, 197, 94);
    pdf.text('تقرير حركات المخزون', 400, 80, { align: 'center' });
    
    // خط فاصل
    pdf.setDrawColor(34, 197, 94);
    pdf.setLineWidth(2);
    pdf.line(50, 100, 550, 100);
    
    // معلومات التقرير
    pdf.setFontSize(12);
    pdf.setTextColor(0, 0, 0);
    const currentDate = new Date().toLocaleDateString('ar-SA');
    const currentTime = new Date().toLocaleTimeString('ar-SA');
    
    pdf.text(`التاريخ: ${currentDate}`, 500, 130, { align: 'right' });
    pdf.text(`الوقت: ${currentTime}`, 500, 150, { align: 'right' });
    pdf.text(`عدد الحركات: ${movements ? movements.length : 0}`, 500, 170, { align: 'right' });
    
    // إحصائيات
    if (movements && movements.length > 0) {
      const inCount = movements.filter(m => m.type === 'in').length;
      const outCount = movements.filter(m => m.type === 'out').length;
      
      pdf.text(`حركات الإدخال: ${inCount}`, 500, 190, { align: 'right' });
      pdf.text(`حركات الإخراج: ${outCount}`, 500, 210, { align: 'right' });
    }
    
    // الجدول
    let yPosition = 250;
    
    // رؤوس الجدول
    pdf.setFillColor(34, 197, 94);
    pdf.rect(50, yPosition, 500, 25, 'F');
    
    pdf.setTextColor(255, 255, 255);
    pdf.setFontSize(10);
    pdf.text('النوع', 80, yPosition + 15, { align: 'center' });
    pdf.text('الصنف', 180, yPosition + 15, { align: 'center' });
    pdf.text('الكمية', 280, yPosition + 15, { align: 'center' });
    pdf.text('التاريخ', 380, yPosition + 15, { align: 'center' });
    pdf.text('الملاحظات', 480, yPosition + 15, { align: 'center' });
    
    yPosition += 25;
    
    // بيانات الجدول
    pdf.setTextColor(0, 0, 0);
    
    if (!movements || movements.length === 0) {
      pdf.text('لا توجد حركات مسجلة', 300, yPosition + 30, { align: 'center' });
    } else {
      movements.slice(0, 20).forEach((movement, index) => {
        if (yPosition > 700) {
          pdf.addPage();
          yPosition = 50;
        }
        
        // خلفية متناوبة
        if (index % 2 === 0) {
          pdf.setFillColor(249, 250, 251);
          pdf.rect(50, yPosition, 500, 20, 'F');
        }
        
        const type = movement.type === 'in' ? 'إدخال' : 'إخراج';
        const itemName = movement.itemName || 'غير محدد';
        const quantity = movement.quantity || '0';
        const date = movement.date || 'غير محدد';
        const notes = (movement.notes || 'لا توجد').substring(0, 15);
        
        pdf.text(type, 80, yPosition + 12, { align: 'center' });
        pdf.text(itemName.substring(0, 15), 180, yPosition + 12, { align: 'center' });
        pdf.text(quantity.toString(), 280, yPosition + 12, { align: 'center' });
        pdf.text(date, 380, yPosition + 12, { align: 'center' });
        pdf.text(notes, 480, yPosition + 12, { align: 'center' });
        
        yPosition += 20;
      });
      
      if (movements.length > 20) {
        pdf.text(`... وعدد ${movements.length - 20} حركة أخرى`, 300, yPosition + 20, { align: 'center' });
      }
    }
    
    // التذييل
    pdf.setFontSize(8);
    pdf.setTextColor(107, 114, 128);
    pdf.text('تم إنشاؤه بواسطة نظام إدارة المخزون', 300, 750, { align: 'center' });
    pdf.text('"نحو زراعة مستدامة ومستقبل أخضر"', 300, 765, { align: 'center' });
    
    // حفظ الملف
    pdf.save(filename);
    
    console.log('✅ تم إنشاء PDF بنجاح');
    return true;
    
  } catch (error) {
    console.error('❌ خطأ في إنشاء PDF:', error);
    throw error;
  }
};

// دالة لإنشاء PDF بسيط للتقرير المالي
export const createSimpleFinancialPDF = async (transactions, filename = 'التقرير-المالي.pdf') => {
  try {
    console.log('💰 إنشاء PDF بسيط للتقرير المالي...');
    
    const pdf = new jsPDF('p', 'pt', 'a4');
    
    // إعداد الخط
    pdf.setFont('helvetica');
    
    // العنوان الرئيسي
    pdf.setFontSize(20);
    pdf.setTextColor(21, 128, 61);
    pdf.text('شركة الشفق للزراعة الحديثة', 400, 50, { align: 'center' });

    pdf.setFontSize(16);
    pdf.setTextColor(59, 130, 246);
    pdf.text('التقرير المالي', 400, 80, { align: 'center' });
    
    // خط فاصل
    pdf.setDrawColor(59, 130, 246);
    pdf.setLineWidth(2);
    pdf.line(50, 100, 550, 100);
    
    // معلومات التقرير
    pdf.setFontSize(12);
    pdf.setTextColor(0, 0, 0);
    const currentDate = new Date().toLocaleDateString('ar-SA');
    
    pdf.text(`التاريخ: ${currentDate}`, 500, 150, { align: 'right' });
    pdf.text(`عدد المعاملات: ${transactions ? transactions.length : 0}`, 500, 170, { align: 'right' });
    
    // الملخص المالي
    if (transactions && transactions.length > 0) {
      const totalIncome = transactions.filter(t => t.type === 'income').reduce((sum, t) => sum + (t.amount || 0), 0);
      const totalExpense = transactions.filter(t => t.type === 'expense').reduce((sum, t) => sum + (t.amount || 0), 0);
      const balance = totalIncome - totalExpense;
      
      pdf.setFontSize(14);
      pdf.setTextColor(21, 128, 61);
      pdf.text('الملخص المالي', 300, 210, { align: 'center' });

      pdf.setFontSize(12);
      pdf.setTextColor(22, 163, 74);
      pdf.text(`إجمالي الإيرادات: ${totalIncome.toLocaleString()} دينار أردني`, 500, 240, { align: 'right' });

      pdf.setTextColor(220, 38, 38);
      pdf.text(`إجمالي المصروفات: ${totalExpense.toLocaleString()} دينار أردني`, 500, 260, { align: 'right' });

      pdf.setTextColor(balance >= 0 ? 22 : 220, balance >= 0 ? 163 : 38, balance >= 0 ? 74 : 38);
      pdf.text(`الرصيد الصافي: ${balance.toLocaleString()} دينار أردني`, 500, 280, { align: 'right' });
    }
    
    // التذييل
    pdf.setFontSize(8);
    pdf.setTextColor(107, 114, 128);
    pdf.text('تم إنشاؤه بواسطة نظام إدارة الأموال', 300, 750, { align: 'center' });
    pdf.text('"نحو زراعة مستدامة ومستقبل أخضر"', 300, 765, { align: 'center' });
    
    // حفظ الملف
    pdf.save(filename);
    
    console.log('✅ تم إنشاء PDF المالي بنجاح');
    return true;
    
  } catch (error) {
    console.error('❌ خطأ في إنشاء PDF المالي:', error);
    throw error;
  }
};

// دالة لإنشاء HTML لتقرير المخزون
export const createInventoryHTML = (movements, filename = 'تقرير-المخزون.html') => {
  try {
    console.log('📄 إنشاء HTML لتقرير المخزون...');

    // تنسيق التاريخ والوقت بالميلادي
    const now = new Date();
    const currentDate = `${now.getDate().toString().padStart(2, '0')}/${(now.getMonth() + 1).toString().padStart(2, '0')}/${now.getFullYear()}`;
    const hours = now.getHours();
    const minutes = now.getMinutes();
    const ampm = hours >= 12 ? 'PM' : 'AM';
    const displayHours = hours % 12 || 12;
    const currentTime = `${displayHours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')} ${ampm}`;

    const inCount = movements ? movements.filter(m => m.type === 'in').length : 0;
    const outCount = movements ? movements.filter(m => m.type === 'out').length : 0;

    const movementsHTML = movements && movements.length > 0 ?
      movements.map(movement => `
        <tr>
          <td>${movement.type === 'in' ? 'إدخال' : 'إخراج'}</td>
          <td>${movement.itemName || 'غير محدد'}</td>
          <td>${movement.quantity || '0'}</td>
          <td>${movement.date || 'غير محدد'}</td>
          <td>${movement.notes || 'لا توجد ملاحظات'}</td>
        </tr>
      `).join('') :
      '<tr><td colspan="5" style="text-align: center; padding: 40px; color: #6b7280;">لا توجد حركات مسجلة</td></tr>';

    const htmlContent = `
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تقرير حركات المخزون - شركة الشفق</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Cairo:wght@400;600;700&display=swap');

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        @page {
            size: A4;
            margin: 1.5cm;
        }

        body {
            font-family: 'Cairo', 'Tajawal', Arial, sans-serif;
            direction: rtl;
            text-align: right;
            line-height: 1.4;
            color: #333;
            background: white;
            padding: 0;
            margin: 0;
            width: 21cm;
            min-height: 29.7cm;
            max-width: 21cm;
        }

        .page-container {
            width: 100%;
            max-width: 21cm;
            min-height: 29.7cm;
            padding: 1cm 0.8cm 1cm 1.5cm;
            margin: 0 auto;
            background: white;
            box-sizing: border-box;
        }

        .container {
            width: 100%;
            max-width: 100%;
            margin: 0;
            background: white;
            border-radius: 0;
            box-shadow: none;
            overflow: visible;
        }

        .header {
            background: linear-gradient(135deg, #15803d, #22c55e);
            color: white;
            padding: 20px;
            text-align: center;
            margin-bottom: 20px;
        }

        .company-name {
            font-size: 24px;
            font-weight: 700;
            margin-bottom: 8px;
        }

        .report-title {
            font-size: 18px;
            font-weight: 600;
            opacity: 0.9;
        }

        .info-section {
            padding: 25px 30px;
            background: #f0fdf4;
            border-bottom: 1px solid #e5e7eb;
        }

        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
        }

        .info-item {
            background: white;
            padding: 15px;
            border-radius: 8px;
            border-right: 4px solid #22c55e;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        }

        .info-label {
            font-weight: 600;
            color: #15803d;
            margin-bottom: 5px;
        }

        .info-value {
            font-size: 18px;
            color: #374151;
        }

        .table-section {
            padding: 30px;
        }

        .section-title {
            font-size: 20px;
            font-weight: 700;
            color: #15803d;
            margin-bottom: 20px;
            text-align: center;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
            border-radius: 0;
            overflow: visible;
            box-shadow: none;
            border: 1px solid #d1d5db;
        }

        th {
            background: #22c55e;
            color: white;
            padding: 10px 8px;
            font-weight: 600;
            text-align: center;
            font-size: 12px;
            border: 1px solid #16a34a;
        }

        td {
            padding: 8px 6px;
            text-align: center;
            border: 1px solid #e5e7eb;
            font-size: 11px;
        }

        tr:nth-child(even) {
            background: #f9fafb;
        }

        tr:hover {
            background: #f0fdf4;
        }

        .footer {
            background: #374151;
            color: white;
            padding: 25px;
            text-align: center;
        }

        .footer p {
            margin-bottom: 8px;
        }

        .print-btn {
            position: fixed;
            top: 20px;
            left: 20px;
            background: #22c55e;
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 8px;
            cursor: pointer;
            font-family: inherit;
            font-weight: 600;
            box-shadow: 0 4px 12px rgba(34, 197, 94, 0.3);
            transition: all 0.3s ease;
        }

        .print-btn:hover {
            background: #16a34a;
            transform: translateY(-2px);
        }

        @media print {
            .print-btn {
                display: none;
            }

            body {
                background: white;
                padding: 0;
                margin: 0;
                width: 21cm;
                min-height: 29.7cm;
            }

            .page-container {
                padding: 0.8cm 0.5cm 0.8cm 1cm;
                margin: 0;
                width: 100%;
                max-width: none;
            }

            .container {
                box-shadow: none;
                border-radius: 0;
                margin: 0;
                padding: 0;
            }
        }

        @media (max-width: 768px) {
            .info-grid {
                grid-template-columns: 1fr;
            }

            table {
                font-size: 12px;
            }

            th, td {
                padding: 8px 6px;
            }
        }
    </style>
</head>
<body>
    <button class="print-btn" onclick="window.print()">🖨️ طباعة التقرير</button>

    <div class="page-container">
        <div class="container">
            <div class="header">
                <div class="company-name">شركة الشفق للزراعة الحديثة</div>
                <div class="report-title">تقرير حركات المخزون</div>
            </div>



        <div class="table-section">
            <div class="section-title">تفاصيل حركات المخزون</div>
            <table>
                <thead>
                    <tr>
                        <th>نوع الحركة</th>
                        <th>اسم الصنف</th>
                        <th>الكمية</th>
                        <th>التاريخ</th>
                        <th>الملاحظات</th>
                    </tr>
                </thead>
                <tbody>
                    ${movementsHTML}
                </tbody>
            </table>
        </div>

            <div class="footer">
                <p>تم إنشاؤه بواسطة نظام إدارة المخزون</p>
                <p>شركة الشفق للزراعة الحديثة</p>
                <p>"نحو زراعة مستدامة ومستقبل أخضر"</p>
            </div>
        </div>
    </div>
</body>
</html>`;

    // إنشاء وتحميل الملف
    const blob = new Blob([htmlContent], { type: 'text/html;charset=utf-8' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);

    console.log('✅ تم إنشاء HTML بنجاح');
    return true;

  } catch (error) {
    console.error('❌ خطأ في إنشاء HTML:', error);
    throw error;
  }
};

// دالة لإنشاء HTML للتقرير المالي
export const createFinancialHTML = (transactions, filename = 'التقرير-المالي.html') => {
  try {
    console.log('💰 إنشاء HTML للتقرير المالي...');

    // تنسيق التاريخ والوقت بالميلادي
    const now = new Date();
    const currentDate = `${now.getDate().toString().padStart(2, '0')}/${(now.getMonth() + 1).toString().padStart(2, '0')}/${now.getFullYear()}`;
    const hours = now.getHours();
    const minutes = now.getMinutes();
    const ampm = hours >= 12 ? 'PM' : 'AM';
    const displayHours = hours % 12 || 12;
    const currentTime = `${displayHours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')} ${ampm}`;

    const totalIncome = transactions ? transactions.filter(t => t.type === 'income').reduce((sum, t) => sum + (t.amount || 0), 0) : 0;
    const totalExpense = transactions ? transactions.filter(t => t.type === 'expense').reduce((sum, t) => sum + (t.amount || 0), 0) : 0;
    const balance = totalIncome - totalExpense;

    const transactionsHTML = transactions && transactions.length > 0 ?
      transactions.map(transaction => `
        <tr>
          <td>${transaction.type === 'income' ? 'إيراد' : 'مصروف'}</td>
          <td>${(transaction.amount || 0).toLocaleString()} دينار أردني</td>
          <td>${transaction.description || 'بدون وصف'}</td>
          <td>${transaction.category || 'غير مصنف'}</td>
          <td>${transaction.date || 'غير محدد'}</td>
        </tr>
      `).join('') :
      '<tr><td colspan="5" style="text-align: center; padding: 40px; color: #6b7280;">لا توجد معاملات مالية مسجلة</td></tr>';

    const htmlContent = `
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>التقرير المالي - شركة الشفق</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Cairo:wght@400;600;700&display=swap');

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        @page {
            size: A4;
            margin: 1.5cm;
        }

        body {
            font-family: 'Cairo', 'Tajawal', Arial, sans-serif;
            direction: rtl;
            text-align: right;
            line-height: 1.4;
            color: #333;
            background: white;
            padding: 0;
            margin: 0;
            width: 21cm;
            min-height: 29.7cm;
            max-width: 21cm;
        }

        .page-container {
            width: 100%;
            max-width: 21cm;
            min-height: 29.7cm;
            padding: 1.5cm;
            margin: 0 auto;
            background: white;
            box-sizing: border-box;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #1e40af, #3b82f6);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .company-name {
            font-size: 28px;
            font-weight: 700;
            margin-bottom: 10px;
        }

        .report-title {
            font-size: 20px;
            font-weight: 600;
            opacity: 0.9;
        }

        .summary-section {
            padding: 30px;
            background: #eff6ff;
        }

        .summary-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }

        .summary-card {
            background: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .income-card {
            border-right: 4px solid #22c55e;
        }

        .expense-card {
            border-right: 4px solid #ef4444;
        }

        .balance-card {
            border-right: 4px solid #3b82f6;
        }

        .card-title {
            font-size: 14px;
            color: #6b7280;
            margin-bottom: 8px;
        }

        .card-amount {
            font-size: 24px;
            font-weight: 700;
            margin-bottom: 5px;
        }

        .income { color: #15803d; }
        .expense { color: #dc2626; }
        .balance { color: #1d4ed8; }

        .info-section {
            padding: 25px 30px;
            background: #f9fafb;
            border-bottom: 1px solid #e5e7eb;
        }

        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
        }

        .info-item {
            background: white;
            padding: 15px;
            border-radius: 8px;
            border-right: 4px solid #3b82f6;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        }

        .info-label {
            font-weight: 600;
            color: #1e40af;
            margin-bottom: 5px;
        }

        .info-value {
            font-size: 18px;
            color: #374151;
        }

        .table-section {
            padding: 30px;
        }

        .section-title {
            font-size: 20px;
            font-weight: 700;
            color: #1e40af;
            margin-bottom: 20px;
            text-align: center;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        th {
            background: #3b82f6;
            color: white;
            padding: 15px 12px;
            font-weight: 600;
            text-align: center;
            font-size: 14px;
        }

        td {
            padding: 12px;
            text-align: center;
            border-bottom: 1px solid #e5e7eb;
        }

        tr:nth-child(even) {
            background: #f9fafb;
        }

        tr:hover {
            background: #eff6ff;
        }

        .footer {
            background: #374151;
            color: white;
            padding: 25px;
            text-align: center;
        }

        .footer p {
            margin-bottom: 8px;
        }

        .print-btn {
            position: fixed;
            top: 20px;
            left: 20px;
            background: #3b82f6;
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 8px;
            cursor: pointer;
            font-family: inherit;
            font-weight: 600;
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
            transition: all 0.3s ease;
        }

        .print-btn:hover {
            background: #2563eb;
            transform: translateY(-2px);
        }

        @media print {
            .print-btn {
                display: none;
            }
            body {
                background: white;
                padding: 0;
            }
            .container {
                box-shadow: none;
                border-radius: 0;
            }
        }
    </style>
</head>
<body>
    <button class="print-btn" onclick="window.print()">🖨️ طباعة التقرير</button>

    <div class="container">
        <div class="header">
            <div class="company-name">شركة الشفق للزراعة الحديثة</div>
            <div class="report-title">التقرير المالي</div>
        </div>

        <div class="summary-section">
            <div class="summary-grid">
                <div class="summary-card income-card">
                    <div class="card-title">إجمالي الإيرادات</div>
                    <div class="card-amount income">${totalIncome.toLocaleString()} دينار أردني</div>
                </div>
                <div class="summary-card expense-card">
                    <div class="card-title">إجمالي المصروفات</div>
                    <div class="card-amount expense">${totalExpense.toLocaleString()} دينار أردني</div>
                </div>
                <div class="summary-card balance-card">
                    <div class="card-title">الرصيد الصافي</div>
                    <div class="card-amount balance">${balance.toLocaleString()} دينار أردني</div>
                </div>
            </div>
        </div>



        <div class="table-section">
            <div class="section-title">تفاصيل المعاملات المالية</div>
            <table>
                <thead>
                    <tr>
                        <th>نوع المعاملة</th>
                        <th>المبلغ</th>
                        <th>الوصف</th>
                        <th>التصنيف</th>
                        <th>التاريخ</th>
                    </tr>
                </thead>
                <tbody>
                    ${transactionsHTML}
                </tbody>
            </table>
        </div>

        <div class="footer">
            <p>تم إنشاؤه بواسطة نظام إدارة الأموال</p>
            <p>شركة الشفق للزراعة الحديثة</p>
            <p>"نحو زراعة مستدامة ومستقبل أخضر"</p>
        </div>
    </div>
</body>
</html>`;

    // إنشاء وتحميل الملف
    const blob = new Blob([htmlContent], { type: 'text/html;charset=utf-8' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);

    console.log('✅ تم إنشاء HTML المالي بنجاح');
    return true;

  } catch (error) {
    console.error('❌ خطأ في إنشاء HTML المالي:', error);
    throw error;
  }
};

// دالة لإنشاء HTML للمعاينة فقط (بدون تحميل)
export const createInventoryHTMLPreview = (movements) => {
  try {
    // تنسيق التاريخ والوقت بالميلادي
    const now = new Date();
    const currentDate = `${now.getDate().toString().padStart(2, '0')}/${(now.getMonth() + 1).toString().padStart(2, '0')}/${now.getFullYear()}`;
    const hours = now.getHours();
    const minutes = now.getMinutes();
    const ampm = hours >= 12 ? 'PM' : 'AM';
    const displayHours = hours % 12 || 12;
    const currentTime = `${displayHours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')} ${ampm}`;

    const inCount = movements ? movements.filter(m => m.type === 'in').length : 0;
    const outCount = movements ? movements.filter(m => m.type === 'out').length : 0;

    const movementsHTML = movements && movements.length > 0 ?
      movements.map(movement => `
        <tr>
          <td>${movement.type === 'in' ? 'إدخال' : 'إخراج'}</td>
          <td>${movement.itemName || 'غير محدد'}</td>
          <td>${movement.quantity || '0'}</td>
          <td>${movement.date || 'غير محدد'}</td>
          <td>${movement.notes || 'لا توجد ملاحظات'}</td>
        </tr>
      `).join('') :
      '<tr><td colspan="5" style="text-align: center; padding: 40px; color: #6b7280;">لا توجد حركات مسجلة</td></tr>';

    return `
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تقرير حركات المخزون - شركة الشفق</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Cairo:wght@400;600;700&display=swap');

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        @page {
            size: A4;
            margin: 1.5cm;
        }

        body {
            font-family: 'Cairo', 'Tajawal', Arial, sans-serif;
            direction: rtl;
            text-align: right;
            line-height: 1.4;
            color: #333;
            background: white;
            padding: 0;
            margin: 0;
            width: 21cm;
            min-height: 29.7cm;
            max-width: 21cm;
        }

        .page-container {
            width: 100%;
            max-width: 21cm;
            min-height: 29.7cm;
            padding: 1cm 0.8cm 1cm 1.5cm;
            margin: 0 auto;
            background: white;
            box-sizing: border-box;
        }

        .container {
            width: 100%;
            max-width: 100%;
            margin: 0;
            background: white;
            border-radius: 0;
            box-shadow: none;
            overflow: visible;
        }

        .header {
            background: linear-gradient(135deg, #15803d, #22c55e);
            color: white;
            padding: 20px;
            text-align: center;
            margin-bottom: 20px;
        }

        .company-name {
            font-size: 24px;
            font-weight: 700;
            margin-bottom: 8px;
        }

        .report-title {
            font-size: 18px;
            font-weight: 600;
            opacity: 0.9;
        }

        .info-section {
            padding: 25px 30px;
            background: #f0fdf4;
            border-bottom: 1px solid #e5e7eb;
        }

        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
        }

        .info-item {
            background: white;
            padding: 15px;
            border-radius: 8px;
            border-right: 4px solid #22c55e;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        }

        .info-label {
            font-weight: 600;
            color: #15803d;
            margin-bottom: 5px;
        }

        .info-value {
            font-size: 18px;
            color: #374151;
        }

        .table-section {
            padding: 30px;
        }

        .section-title {
            font-size: 20px;
            font-weight: 700;
            color: #15803d;
            margin-bottom: 20px;
            text-align: center;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
            border-radius: 0;
            overflow: visible;
            box-shadow: none;
            border: 1px solid #d1d5db;
        }

        th {
            background: #22c55e;
            color: white;
            padding: 10px 8px;
            font-weight: 600;
            text-align: center;
            font-size: 12px;
            border: 1px solid #16a34a;
        }

        td {
            padding: 8px 6px;
            text-align: center;
            border: 1px solid #e5e7eb;
            font-size: 11px;
        }

        tr:nth-child(even) {
            background: #f9fafb;
        }

        tr:hover {
            background: #f0fdf4;
        }

        .footer {
            background: #374151;
            color: white;
            padding: 25px;
            text-align: center;
        }

        .footer p {
            margin-bottom: 8px;
        }

        .print-btn {
            position: fixed;
            top: 20px;
            left: 20px;
            background: #22c55e;
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 8px;
            cursor: pointer;
            font-family: inherit;
            font-weight: 600;
            box-shadow: 0 4px 12px rgba(34, 197, 94, 0.3);
            transition: all 0.3s ease;
        }

        .print-btn:hover {
            background: #16a34a;
            transform: translateY(-2px);
        }

        @media print {
            .print-btn {
                display: none;
            }

            body {
                background: white;
                padding: 0;
                margin: 0;
                width: 21cm;
                min-height: 29.7cm;
            }

            .page-container {
                padding: 0.8cm 0.5cm 0.8cm 1cm;
                margin: 0;
                width: 100%;
                max-width: none;
            }

            .container {
                box-shadow: none;
                border-radius: 0;
                margin: 0;
                padding: 0;
            }
        }
    </style>
</head>
<body>
    <button class="print-btn" onclick="window.print()">🖨️ طباعة التقرير</button>

    <div class="page-container">
        <div class="container">
            <div class="header">
                <div class="company-name">شركة الشفق للزراعة الحديثة</div>
                <div class="report-title">تقرير حركات المخزون</div>
            </div>

            <div class="info-section">
                <div class="info-grid">
                    <div class="info-item">
                        <div class="info-label">تاريخ التقرير</div>
                        <div class="info-value">${currentDate}</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">وقت التقرير</div>
                        <div class="info-value">${currentTime}</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">إجمالي الحركات</div>
                        <div class="info-value">${movements ? movements.length : 0}</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">حركات الإدخال</div>
                        <div class="info-value">${inCount}</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">حركات الإخراج</div>
                        <div class="info-value">${outCount}</div>
                    </div>
                </div>
            </div>

            <div class="table-section">
                <div class="section-title">تفاصيل حركات المخزون</div>
                <table>
                    <thead>
                        <tr>
                            <th>نوع الحركة</th>
                            <th>اسم الصنف</th>
                            <th>الكمية</th>
                            <th>التاريخ</th>
                            <th>الملاحظات</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${movementsHTML}
                    </tbody>
                </table>
            </div>

            <div class="footer">
                <p>تم إنشاؤه بواسطة نظام إدارة المخزون</p>
                <p>شركة الشفق للزراعة الحديثة</p>
                <p>"نحو زراعة مستدامة ومستقبل أخضر"</p>
            </div>
        </div>
    </div>
</body>
</html>`;
  } catch (error) {
    console.error('❌ خطأ في إنشاء HTML للمعاينة:', error);
    throw error;
  }
};

// دالة لإنشاء HTML للتقرير المالي للمعاينة فقط
export const createFinancialHTMLPreview = (transactions) => {
  try {
    // تنسيق التاريخ والوقت بالميلادي
    const now = new Date();
    const currentDate = `${now.getDate().toString().padStart(2, '0')}/${(now.getMonth() + 1).toString().padStart(2, '0')}/${now.getFullYear()}`;
    const hours = now.getHours();
    const minutes = now.getMinutes();
    const ampm = hours >= 12 ? 'PM' : 'AM';
    const displayHours = hours % 12 || 12;
    const currentTime = `${displayHours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')} ${ampm}`;

    const totalIncome = transactions ? transactions.filter(t => t.type === 'income').reduce((sum, t) => sum + (t.amount || 0), 0) : 0;
    const totalExpense = transactions ? transactions.filter(t => t.type === 'expense').reduce((sum, t) => sum + (t.amount || 0), 0) : 0;
    const balance = totalIncome - totalExpense;

    const transactionsHTML = transactions && transactions.length > 0 ?
      transactions.map(transaction => `
        <tr>
          <td>${transaction.type === 'income' ? 'إيراد' : 'مصروف'}</td>
          <td>${(transaction.amount || 0).toLocaleString()} دينار أردني</td>
          <td>${transaction.description || 'بدون وصف'}</td>
          <td>${transaction.category || 'غير مصنف'}</td>
          <td>${transaction.date || 'غير محدد'}</td>
        </tr>
      `).join('') :
      '<tr><td colspan="5" style="text-align: center; padding: 40px; color: #6b7280;">لا توجد معاملات مالية مسجلة</td></tr>';

    return `
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>التقرير المالي - شركة الشفق</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Cairo:wght@400;600;700&display=swap');

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        @page {
            size: A4;
            margin: 1.5cm;
        }

        body {
            font-family: 'Cairo', 'Tajawal', Arial, sans-serif;
            direction: rtl;
            text-align: right;
            line-height: 1.4;
            color: #333;
            background: white;
            padding: 0;
            margin: 0;
            width: 21cm;
            min-height: 29.7cm;
            max-width: 21cm;
        }

        .page-container {
            width: 100%;
            max-width: 21cm;
            min-height: 29.7cm;
            padding: 1.5cm;
            margin: 0 auto;
            background: white;
            box-sizing: border-box;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #1e40af, #3b82f6);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .company-name {
            font-size: 28px;
            font-weight: 700;
            margin-bottom: 10px;
        }

        .report-title {
            font-size: 20px;
            font-weight: 600;
            opacity: 0.9;
        }

        .summary-section {
            padding: 30px;
            background: #eff6ff;
        }

        .summary-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }

        .summary-card {
            background: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .income-card {
            border-right: 4px solid #22c55e;
        }

        .expense-card {
            border-right: 4px solid #ef4444;
        }

        .balance-card {
            border-right: 4px solid #3b82f6;
        }

        .card-title {
            font-size: 14px;
            color: #6b7280;
            margin-bottom: 8px;
        }

        .card-amount {
            font-size: 24px;
            font-weight: 700;
            margin-bottom: 5px;
        }

        .income { color: #15803d; }
        .expense { color: #dc2626; }
        .balance { color: #1d4ed8; }

        .info-section {
            padding: 25px 30px;
            background: #f9fafb;
            border-bottom: 1px solid #e5e7eb;
        }

        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
        }

        .info-item {
            background: white;
            padding: 15px;
            border-radius: 8px;
            border-right: 4px solid #3b82f6;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        }

        .info-label {
            font-weight: 600;
            color: #1e40af;
            margin-bottom: 5px;
        }

        .info-value {
            font-size: 18px;
            color: #374151;
        }

        .table-section {
            padding: 30px;
        }

        .section-title {
            font-size: 20px;
            font-weight: 700;
            color: #1e40af;
            margin-bottom: 20px;
            text-align: center;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        th {
            background: #3b82f6;
            color: white;
            padding: 15px 12px;
            font-weight: 600;
            text-align: center;
            font-size: 14px;
        }

        td {
            padding: 12px;
            text-align: center;
            border-bottom: 1px solid #e5e7eb;
        }

        tr:nth-child(even) {
            background: #f9fafb;
        }

        tr:hover {
            background: #eff6ff;
        }

        .footer {
            background: #374151;
            color: white;
            padding: 25px;
            text-align: center;
        }

        .footer p {
            margin-bottom: 8px;
        }

        .print-btn {
            position: fixed;
            top: 20px;
            left: 20px;
            background: #3b82f6;
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 8px;
            cursor: pointer;
            font-family: inherit;
            font-weight: 600;
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
            transition: all 0.3s ease;
        }

        .print-btn:hover {
            background: #2563eb;
            transform: translateY(-2px);
        }

        @media print {
            .print-btn {
                display: none;
            }
            body {
                background: white;
                padding: 0;
            }
            .container {
                box-shadow: none;
                border-radius: 0;
            }
        }
    </style>
</head>
<body>
    <button class="print-btn" onclick="window.print()">🖨️ طباعة التقرير</button>

    <div class="container">
        <div class="header">
            <div class="company-name">شركة الشفق للزراعة الحديثة</div>
            <div class="report-title">التقرير المالي</div>
        </div>

        <div class="summary-section">
            <div class="summary-grid">
                <div class="summary-card income-card">
                    <div class="card-title">إجمالي الإيرادات</div>
                    <div class="card-amount income">${totalIncome.toLocaleString()} دينار أردني</div>
                </div>
                <div class="summary-card expense-card">
                    <div class="card-title">إجمالي المصروفات</div>
                    <div class="card-amount expense">${totalExpense.toLocaleString()} دينار أردني</div>
                </div>
                <div class="summary-card balance-card">
                    <div class="card-title">الرصيد الصافي</div>
                    <div class="card-amount balance">${balance.toLocaleString()} دينار أردني</div>
                </div>
            </div>
        </div>

        <div class="info-section">
            <div class="info-grid">
                <div class="info-item">
                    <div class="info-label">تاريخ التقرير</div>
                    <div class="info-value">${currentDate}</div>
                </div>
                <div class="info-item">
                    <div class="info-label">وقت التقرير</div>
                    <div class="info-value">${currentTime}</div>
                </div>
                <div class="info-item">
                    <div class="info-label">إجمالي المعاملات</div>
                    <div class="info-value">${transactions ? transactions.length : 0}</div>
                </div>
            </div>
        </div>

        <div class="table-section">
            <div class="section-title">تفاصيل المعاملات المالية</div>
            <table>
                <thead>
                    <tr>
                        <th>نوع المعاملة</th>
                        <th>المبلغ</th>
                        <th>الوصف</th>
                        <th>التصنيف</th>
                        <th>التاريخ</th>
                    </tr>
                </thead>
                <tbody>
                    ${transactionsHTML}
                </tbody>
            </table>
        </div>

        <div class="footer">
            <p>تم إنشاؤه بواسطة نظام إدارة الأموال</p>
            <p>شركة الشفق للزراعة الحديثة</p>
            <p>"نحو زراعة مستدامة ومستقبل أخضر"</p>
        </div>
    </div>
</body>
</html>`;
  } catch (error) {
    console.error('❌ خطأ في إنشاء HTML المالي للمعاينة:', error);
    throw error;
  }
};

export default {
  createSimpleInventoryPDF,
  createSimpleFinancialPDF,
  createInventoryHTML,
  createFinancialHTML,
  createInventoryHTMLPreview,
  createFinancialHTMLPreview
};
