import React, { useState } from 'react';
import { Users, Plus, Search, DollarSign, CheckCircle, Clock, XCircle, CreditCard } from 'lucide-react';
import { formatDate } from '../utils/database';

interface Advance {
  id: string;
  employeeId: string;
  employeeName: string;
  amount: number;
  date: string;
  reason: string;
  status: 'pending' | 'approved' | 'rejected' | 'paid';
  notes: string;
}

interface AdvancesViewProps {
  advances: Advance[];
  employees: any[];
  onAdvanceUpdate: (advanceId: string, updatedAdvance: Advance) => void;
  onAdvanceAdd: (advance: Omit<Advance, 'id'>) => void;
  onAdvanceDelete: (advanceId: string) => void;
}

const AdvancesView: React.FC<AdvancesViewProps> = ({ 
  advances, 
  employees,
  onAdvanceUpdate, 
  onAdvanceAdd, 
  onAdvanceDelete 
}) => {
  const [showAddModal, setShowAddModal] = useState(false);
  const [editingAdvance, setEditingAdvance] = useState<Advance | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<'all' | 'pending' | 'approved' | 'paid' | 'rejected'>('all');

  const filteredAdvances = advances.filter(advance =>
    (statusFilter === 'all' || advance.status === statusFilter) &&
    (searchTerm === '' || 
     advance.employeeName.toLowerCase().includes(searchTerm.toLowerCase()) ||
     advance.reason.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    const formData = new FormData(e.currentTarget);
    
    const advanceData = {
      employeeId: formData.get('employeeId') as string,
      employeeName: formData.get('employeeName') as string,
      amount: parseFloat(formData.get('amount') as string),
      date: formData.get('date') as string,
      reason: formData.get('reason') as string,
      status: 'pending' as 'pending' | 'approved' | 'rejected' | 'paid',
      notes: formData.get('notes') as string
    };

    if (editingAdvance) {
      onAdvanceUpdate(editingAdvance.id, { ...editingAdvance, ...advanceData });
      setEditingAdvance(null);
    } else {
      onAdvanceAdd(advanceData);
      setShowAddModal(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      case 'approved': return 'bg-green-100 text-green-800';
      case 'rejected': return 'bg-red-100 text-red-800';
      case 'paid': return 'bg-blue-100 text-blue-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'pending': return 'قيد الانتظار';
      case 'approved': return 'موافق عليه';
      case 'rejected': return 'مرفوض';
      case 'paid': return 'مدفوع';
      default: return 'غير محدد';
    }
  };

  const totalAdvances = advances.reduce((sum, advance) => sum + advance.amount, 0);
  const pendingAdvances = advances.filter(advance => advance.status === 'pending');
  const approvedAdvances = advances.filter(advance => advance.status === 'approved');
  const paidAdvances = advances.filter(advance => advance.status === 'paid');

  const formatCurrency = (amount: number) => new Intl.NumberFormat('ar-JO', { style: 'currency', currency: 'JOD' }).format(amount);

  return (
    <div className="min-h-screen w-full bg-gradient-to-br from-yellow-50 via-amber-50 to-emerald-50 py-8 px-2 md:px-8">
      {/* Header مع التدرج */}
      <div className="bg-gradient-to-r from-yellow-500 to-amber-500 text-white p-6 shadow-lg rounded-2xl mb-4">
        <div className="max-w-7xl mx-auto">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <div className="bg-white/20 p-3 rounded-2xl flex items-center justify-center backdrop-blur-sm">
                <DollarSign className="h-8 w-8 text-white" />
              </div>
              <div>
                <h1 className="text-3xl font-bold drop-shadow">إدارة السلف</h1>
                <p className="text-yellow-100 mt-1">سلف الموظفين والمدفوعات</p>
              </div>
            </div>
            <button
              onClick={() => setShowAddModal(true)}
              className="bg-white text-yellow-600 hover:bg-yellow-50 px-6 py-3 rounded-xl font-semibold flex items-center gap-2 transition-colors shadow-md hover:shadow-lg border-2 border-yellow-400 hover:border-yellow-500 transform hover:scale-105"
            >
              <Plus size={20} />
              <span>إضافة سلفة</span>
            </button>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto p-0 md:p-6">
        {/* شريط التنقل */}
        <div className="bg-white rounded-2xl shadow-md p-4 mb-6 border border-yellow-100 flex flex-wrap gap-3 justify-center">
          <button 
            onClick={() => setStatusFilter('all')}
            className={`px-6 py-3 rounded-xl font-semibold flex items-center gap-2 shadow transition-colors ${
              statusFilter === 'all' 
                ? 'bg-yellow-600 text-white hover:bg-yellow-700' 
                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
            }`}
          > 
            <DollarSign size={20} /> 
            <span>إدارة السلف</span> 
          </button>
          <button 
            onClick={() => setStatusFilter('paid')}
            className={`px-6 py-3 rounded-xl font-semibold flex items-center gap-2 shadow transition-colors ${
              statusFilter === 'paid' 
                ? 'bg-blue-600 text-white hover:bg-blue-700' 
                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
            }`}
          > 
            <CreditCard size={20} /> 
            <span>المدفوعات</span> 
          </button>
          <button 
            onClick={() => setStatusFilter('approved')}
            className={`px-6 py-3 rounded-xl font-semibold flex items-center gap-2 shadow transition-colors ${
              statusFilter === 'approved' 
                ? 'bg-green-600 text-white hover:bg-green-700' 
                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
            }`}
          > 
            <CheckCircle size={20} /> 
            <span>الموافق عليها</span> 
          </button>
        </div>

        {/* بطاقات الإحصائيات */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <div onClick={() => setStatusFilter('all')} className={`cursor-pointer bg-white rounded-2xl shadow-2xl p-6 border-2 border-yellow-100 transition-all duration-200 ${statusFilter === 'all' ? 'ring-4 ring-yellow-300' : ''}`}>
            <div className="flex items-center justify-between">
              <div>
                <p className="text-yellow-700 text-sm font-bold mb-2">إجمالي السلف</p>
                <p className="text-3xl font-bold text-yellow-600">{formatCurrency(totalAdvances)}</p>
              </div>
              <div className="bg-yellow-100 p-3 rounded-xl">
                <DollarSign className="h-8 w-8 text-yellow-600" />
              </div>
            </div>
          </div>



          <div onClick={() => setStatusFilter('approved')} className={`cursor-pointer bg-white rounded-2xl shadow-2xl p-6 border-2 border-green-100 transition-all duration-200 ${statusFilter === 'approved' ? 'ring-4 ring-green-300' : ''}`}>
            <div className="flex items-center justify-between">
              <div>
                <p className="text-green-700 text-sm font-bold mb-2">موافق عليه</p>
                <p className="text-3xl font-bold text-green-600">{approvedAdvances.length}</p>
              </div>
              <div className="bg-green-100 p-3 rounded-xl">
                <CheckCircle className="h-8 w-8 text-green-600" />
              </div>
            </div>
          </div>

          <div onClick={() => setStatusFilter('paid')} className={`cursor-pointer bg-white rounded-2xl shadow-2xl p-6 border-2 border-blue-100 transition-all duration-200 ${statusFilter === 'paid' ? 'ring-4 ring-blue-300' : ''}`}>
            <div className="flex items-center justify-between">
              <div>
                <p className="text-blue-700 text-sm font-bold mb-2">مدفوع</p>
                <p className="text-3xl font-bold text-blue-600">{paidAdvances.length}</p>
              </div>
              <div className="bg-blue-100 p-3 rounded-xl">
                <CreditCard className="h-8 w-8 text-blue-600" />
              </div>
            </div>
          </div>
          <div onClick={() => setStatusFilter('rejected')} className={`cursor-pointer bg-white rounded-2xl shadow-2xl p-6 border-2 border-red-100 transition-all duration-200 ${statusFilter === 'rejected' ? 'ring-4 ring-red-300' : ''}`}>
            <div className="flex items-center justify-between">
              <div>
                <p className="text-red-700 text-sm font-bold mb-2">مرفوض</p>
                <p className="text-3xl font-bold text-red-600">{advances.filter(advance => advance.status === 'rejected').length}</p>
              </div>
              <div className="bg-red-100 p-3 rounded-xl">
                <XCircle className="h-8 w-8 text-red-600" />
              </div>
            </div>
          </div>
        </div>

        {/* شريط البحث */}
        <div className="bg-white rounded-2xl shadow-md p-4 mb-6 border border-yellow-100 flex items-center justify-between">
          <div className="relative flex-1 max-w-md">
            <input
              type="text"
              placeholder="البحث في السلف..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-yellow-500 focus:border-transparent"
            />
            <Search className="absolute left-3 top-3 h-5 w-5 text-gray-400" />
          </div>
          <button
            onClick={() => setShowAddModal(true)}
            className="bg-gradient-to-r from-yellow-400 to-yellow-600 text-white px-6 py-3 rounded-xl font-semibold flex items-center gap-2 shadow-md hover:shadow-lg border-2 border-yellow-400 hover:border-yellow-500 transform hover:scale-105 transition-all"
          >
            <Plus size={20} />
            <span>إضافة سلفة جديدة</span>
          </button>
        </div>

        {/* جدول السلف */}
        <div className="bg-white rounded-2xl shadow-xl border border-yellow-100 overflow-hidden">
          <div className="px-6 py-4 border-b border-yellow-200 bg-gradient-to-r from-yellow-50 to-amber-50">
            <h3 className="text-lg font-bold text-yellow-700 drop-shadow">قائمة السلف</h3>
          </div>

          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-4 text-right text-sm font-bold text-yellow-700">الموظف</th>
                  <th className="px-6 py-4 text-right text-sm font-bold text-yellow-700">المبلغ</th>
                  <th className="px-6 py-4 text-right text-sm font-bold text-yellow-700">التاريخ</th>
                  <th className="px-6 py-4 text-right text-sm font-bold text-yellow-700">السبب</th>
                  <th className="px-6 py-4 text-right text-sm font-bold text-yellow-700">الحالة</th>
                  <th className="px-6 py-4 text-right text-sm font-bold text-yellow-700">الإجراءات</th>
                </tr>
              </thead>
              <tbody className="divide-y divide-yellow-50">
                {filteredAdvances.map((advance) => (
                  <tr key={advance.id} className="hover:bg-yellow-50 transition-colors">
                    <td className="px-6 py-4">
                      <div className="flex items-center">
                        <div className="w-10 h-10 bg-yellow-100 rounded-full flex items-center justify-center">
                          <span className="text-yellow-600 font-semibold">
                            {advance.employeeName.charAt(0)}
                          </span>
                        </div>
                        <div className="mr-4">
                          <div className="text-sm font-bold text-yellow-800">{advance.employeeName}</div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 text-sm font-bold text-yellow-800">{formatCurrency(advance.amount)}</td>
                    <td className="px-6 py-4 text-sm text-yellow-800">
                      {formatDate(advance.date)}
                    </td>
                    <td className="px-6 py-4 text-sm text-yellow-800">
                      <div className="max-w-xs truncate" title={advance.reason}>
                        {advance.reason}
                      </div>
                    </td>
                    <td className="px-6 py-4">
                      <span className={`px-3 py-1 rounded-full text-xs font-bold ${getStatusColor(advance.status)}`}> 
                        {getStatusText(advance.status)}
                      </span>
                    </td>
                    <td className="px-6 py-4">
                      <div className="flex items-center gap-2">
                        <button
                          onClick={() => setEditingAdvance(advance)}
                          className="bg-yellow-100 text-yellow-700 px-3 py-1 rounded-xl text-xs font-bold hover:bg-yellow-200 transition-colors shadow"
                        >
                          تعديل
                        </button>
                        <button
                          onClick={() => onAdvanceDelete(advance.id)}
                          className="bg-red-100 text-red-700 px-3 py-1 rounded-xl text-xs font-bold hover:bg-red-200 transition-colors shadow"
                        >
                          حذف
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>

        {/* نافذة إضافة/تعديل سلفة */}
        {(showAddModal || editingAdvance) && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
            <div className="bg-white rounded-2xl shadow-2xl max-w-md w-full border-2 border-yellow-200">
              <div className="bg-gradient-to-r from-yellow-500 to-amber-500 text-white p-6 rounded-t-2xl">
                <h2 className="text-xl font-bold">
                  {editingAdvance ? 'تعديل سلفة' : 'إضافة سلفة جديدة'}
                </h2>
              </div>
              
              <form onSubmit={handleSubmit} className="p-6 space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">الموظف</label>
                  <select
                    name="employeeId"
                    defaultValue={editingAdvance?.employeeId || ''}
                    required
                    onChange={(e) => {
                      const employee = employees.find(emp => emp.id === e.target.value);
                      if (employee) {
                        const employeeNameInput = document.querySelector('input[name="employeeName"]') as HTMLInputElement;
                        if (employeeNameInput) {
                          employeeNameInput.value = employee.name;
                        }
                      }
                    }}
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-yellow-500 focus:border-transparent"
                  >
                    <option value="">اختر الموظف</option>
                    {employees.map((employee) => (
                      <option key={employee.id} value={employee.id}>
                        {employee.name}
                      </option>
                    ))}
                  </select>
                  <input
                    type="hidden"
                    name="employeeName"
                    defaultValue={editingAdvance?.employeeName}
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">المبلغ</label>
                  <input
                    type="number"
                    name="amount"
                    defaultValue={editingAdvance?.amount}
                    required
                    min="0"
                    step="0.01"
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-yellow-500 focus:border-transparent"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">التاريخ</label>
                  <input
                    type="date"
                    name="date"
                    defaultValue={editingAdvance?.date}
                    required
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-yellow-500 focus:border-transparent"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">السبب</label>
                  <textarea
                    name="reason"
                    defaultValue={editingAdvance?.reason}
                    required
                    rows={3}
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-yellow-500 focus:border-transparent"
                    placeholder="أدخل سبب السلفة"
                  />
                </div>
                

                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">ملاحظات</label>
                  <textarea
                    name="notes"
                    defaultValue={editingAdvance?.notes}
                    rows={3}
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-yellow-500 focus:border-transparent"
                  />
                </div>
                
                <div className="flex gap-3 pt-4">
                  <button
                    type="submit"
                    className="flex-1 bg-gradient-to-r from-yellow-400 to-yellow-600 text-white py-3 px-4 rounded-xl font-bold hover:from-yellow-500 hover:to-yellow-700 transition-all shadow"
                  >
                    {editingAdvance ? 'تحديث' : 'إضافة'}
                  </button>
                  <button
                    type="button"
                    onClick={() => {
                      setShowAddModal(false);
                      setEditingAdvance(null);
                    }}
                    className="flex-1 bg-gray-200 text-gray-700 py-3 px-4 rounded-xl font-bold hover:bg-gray-300 transition-all shadow"
                  >
                    إلغاء
                  </button>
                </div>
              </form>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default AdvancesView;