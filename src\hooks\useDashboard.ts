import { useState, useEffect, useCallback } from 'react';
import { enhancedDB } from '../utils/enhancedDatabase';
import { DashboardStats } from '../types';
import { useToast } from './useToast';

export const useDashboard = () => {
  const [stats, setStats] = useState<DashboardStats | null>(null);
  const [loading, setLoading] = useState(true);
  const { showError } = useToast();

  const loadStats = useCallback(async () => {
    try {
      setLoading(true);
      
      // جلب البيانات بشكل متوازي لتحسين الأداء
      const [
        zones,
        employees, 
        sales,
        inventory,
        advances,
        sprayings,
        fertilizations
      ] = await Promise.all([
        enhancedDB.getZones(),
        enhancedDB.getEmployees(),
        enhancedDB.getSales(),
        enhancedDB.getInventoryItems(),
        enhancedDB.getAdvances(),
        enhancedDB.getSprayings(),
        enhancedDB.getFertilizations()
      ]);

      // حساب الإحصائيات
      const currentMonth = new Date().getMonth();
      const currentYear = new Date().getFullYear();
      
      const monthlySales = sales
        .filter(sale => {
          const saleDate = new Date(sale.date);
          return saleDate.getMonth() === currentMonth && saleDate.getFullYear() === currentYear;
        })
        .reduce((sum, sale) => sum + sale.amount, 0);

      const totalSales = sales.reduce((sum, sale) => sum + sale.amount, 0);

      const lowStockItems = inventory.filter(item => 
        item.quantity <= (item.minQuantity || 0)
      ).length;

      const dashboardStats: DashboardStats = {
        totalZones: zones.length,
        activeZones: zones.filter(z => z.status === 'active').length,
        harvestingZones: zones.filter(z => z.status === 'harvesting').length,
        
        // حساب محابس الري
        totalIrrigationValves: zones.reduce((sum, zone) => sum + (zone.valves?.length || 0), 0),
        activeValves: zones.reduce((sum, zone) => 
          sum + (zone.valves?.filter(valve => valve.status === 'active').length || 0), 0
        ),
        
        // إحصائيات الري (سيتم تحسينها لاحقاً)
        todayIrrigations: 0, // TODO: حساب عمليات الري اليوم
        pendingIrrigations: zones.filter(z => z.irrigationStatus === 'pending').length,
        
        // إحصائيات الموظفين
        totalEmployees: employees.length,
        activeEmployees: employees.filter(e => e.status === 'active').length,
        
        // إحصائيات المبيعات
        totalSales,
        monthlySales,
        
        // إحصائيات المخزون والسلف
        lowStockItems,
        pendingAdvances: advances.filter(a => a.status === 'pending').length
      };

      setStats(dashboardStats);
    } catch (error) {
      showError('خطأ في تحميل الإحصائيات', 'تعذر تحميل إحصائيات لوحة التحكم');
      console.error('Error loading dashboard stats:', error);
    } finally {
      setLoading(false);
    }
  }, [showError]);

  const refreshStats = useCallback(() => {
    loadStats();
  }, [loadStats]);

  // تحديث الإحصائيات كل 5 دقائق
  useEffect(() => {
    loadStats();
    
    const interval = setInterval(loadStats, 5 * 60 * 1000);
    return () => clearInterval(interval);
  }, [loadStats]);

  return {
    stats,
    loading,
    refreshStats
  };
};

// Hook للحصول على تقارير سريعة
export const useQuickReports = () => {
  const { showError } = useToast();

  const getZoneReport = useCallback(async () => {
    try {
      const zones = await enhancedDB.getZones();
      return {
        total: zones.length,
        active: zones.filter(z => z.status === 'active').length,
        harvesting: zones.filter(z => z.status === 'harvesting').length,
        inactive: zones.filter(z => z.status === 'inactive').length,
        iceberg: zones.filter(z => z.lettuceType === 'iceberg').length,
        romaine: zones.filter(z => z.lettuceType === 'romaine').length
      };
    } catch (error) {
      showError('خطأ في تقرير المناطق', 'تعذر إنشاء تقرير المناطق');
      return null;
    }
  }, [showError]);

  const getEmployeeReport = useCallback(async () => {
    try {
      const employees = await enhancedDB.getEmployees();
      return {
        total: employees.length,
        active: employees.filter(e => e.status === 'active').length,
        inactive: employees.filter(e => e.status === 'inactive').length,
        onVacation: employees.filter(e => e.status === 'vacation').length,
        totalSalary: employees
          .filter(e => e.status === 'active')
          .reduce((sum, e) => sum + e.salary, 0)
      };
    } catch (error) {
      showError('خطأ في تقرير الموظفين', 'تعذر إنشاء تقرير الموظفين');
      return null;
    }
  }, [showError]);

  const getSalesReport = useCallback(async (startDate?: string, endDate?: string) => {
    try {
      let sales = await enhancedDB.getSales();
      
      // فلترة حسب التاريخ إذا تم توفيره
      if (startDate || endDate) {
        sales = sales.filter(sale => {
          const saleDate = new Date(sale.date);
          const start = startDate ? new Date(startDate) : new Date('1900-01-01');
          const end = endDate ? new Date(endDate) : new Date();
          return saleDate >= start && saleDate <= end;
        });
      }

      return {
        totalSales: sales.length,
        totalAmount: sales.reduce((sum, sale) => sum + sale.amount, 0),
        icebergSales: sales.filter(s => s.itemType === 'iceberg').length,
        romaineSales: sales.filter(s => s.itemType === 'romaine').length,
        packagingSales: sales.filter(s => s.itemType === 'packaging').length,
        averageSale: sales.length > 0 ? 
          sales.reduce((sum, sale) => sum + sale.amount, 0) / sales.length : 0
      };
    } catch (error) {
      showError('خطأ في تقرير المبيعات', 'تعذر إنشاء تقرير المبيعات');
      return null;
    }
  }, [showError]);

  const getInventoryReport = useCallback(async () => {
    try {
      const inventory = await enhancedDB.getInventoryItems();
      const lowStockItems = await enhancedDB.getLowStockItems();
      
      const currentDate = new Date();
      const expiringItems = inventory.filter(item => {
        if (!item.expiryDate) return false;
        const expiryDate = new Date(item.expiryDate);
        const daysUntilExpiry = Math.ceil((expiryDate.getTime() - currentDate.getTime()) / (1000 * 60 * 60 * 24));
        return daysUntilExpiry <= 30 && daysUntilExpiry >= 0;
      });

      return {
        totalItems: inventory.length,
        lowStockItems: lowStockItems.length,
        expiringItems: expiringItems.length,
        categories: {
          pesticides: inventory.filter(i => i.category === 'pesticides').length,
          fertilizers: inventory.filter(i => i.category === 'fertilizers').length,
          seeds: inventory.filter(i => i.category === 'seeds').length,
          packaging: inventory.filter(i => i.category === 'packaging').length,
          equipment: inventory.filter(i => i.category === 'equipment').length
        }
      };
    } catch (error) {
      showError('خطأ في تقرير المخزون', 'تعذر إنشاء تقرير المخزون');
      return null;
    }
  }, [showError]);

  return {
    getZoneReport,
    getEmployeeReport,
    getSalesReport,
    getInventoryReport
  };
};