import React, { useState, useEffect } from 'react';
import { Calculator, Delete, RotateCcw, ArrowRight, Home } from 'lucide-react';

interface CalculatorViewProps {
  setCurrentView?: (view: string) => void;
}

const CalculatorView: React.FC<CalculatorViewProps> = ({ setCurrentView }) => {
  const [display, setDisplay] = useState<string>('0');
  const [previousValue, setPreviousValue] = useState<number | null>(null);
  const [operation, setOperation] = useState<string | null>(null);
  const [waitingForOperand, setWaitingForOperand] = useState<boolean>(false);
  const [pressedKey, setPressedKey] = useState<string | null>(null);

  const inputNumber = (num: string) => {
    if (waitingForOperand) {
      setDisplay(num);
      setWaitingForOperand(false);
    } else {
      setDisplay(display === '0' ? num : display + num);
    }
  };

  const inputDecimal = () => {
    if (waitingForOperand) {
      setDisplay('0.');
      setWaitingForOperand(false);
    } else if (display.indexOf('.') === -1) {
      setDisplay(display + '.');
    }
  };

  const clear = () => {
    setDisplay('0');
    setPreviousValue(null);
    setOperation(null);
    setWaitingForOperand(false);
  };

  const performOperation = (nextOperation: string) => {
    const inputValue = parseFloat(display);

    if (previousValue === null) {
      setPreviousValue(inputValue);
    } else if (operation) {
      const currentValue = previousValue || 0;
      const newValue = calculate(currentValue, inputValue, operation);

      setDisplay(String(newValue));
      setPreviousValue(newValue);
    }

    setWaitingForOperand(true);
    setOperation(nextOperation);
  };

  const calculate = (firstValue: number, secondValue: number, operation: string): number => {
    switch (operation) {
      case '+':
        return firstValue + secondValue;
      case '-':
        return firstValue - secondValue;
      case '×':
        return firstValue * secondValue;
      case '÷':
        return firstValue / secondValue;
      case '=':
        return secondValue;
      default:
        return secondValue;
    }
  };

  const handleEquals = () => {
    const inputValue = parseFloat(display);

    if (previousValue !== null && operation) {
      const newValue = calculate(previousValue, inputValue, operation);
      setDisplay(String(newValue));
      setPreviousValue(null);
      setOperation(null);
      setWaitingForOperand(true);
    }
  };

  const deleteLastDigit = () => {
    if (display.length > 1) {
      setDisplay(display.slice(0, -1));
    } else {
      setDisplay('0');
    }
  };

  // دالة للتعامل مع إدخال الكيبورد
  const handleKeyPress = (event: KeyboardEvent) => {
    const key = event.key;

    // منع السلوك الافتراضي للمفاتيح
    event.preventDefault();

    // إضافة تأثير بصري
    setPressedKey(key);
    setTimeout(() => setPressedKey(null), 150);

    // الأرقام
    if (/[0-9]/.test(key)) {
      inputNumber(key);
    }
    // العمليات الحسابية
    else if (key === '+') {
      performOperation('+');
    }
    else if (key === '-') {
      performOperation('-');
    }
    else if (key === '*' || key === 'x' || key === 'X') {
      performOperation('×');
    }
    else if (key === '/' || key === '÷') {
      performOperation('÷');
    }
    // العلامة العشرية
    else if (key === '.' || key === ',') {
      inputDecimal();
    }
    // يساوي
    else if (key === '=' || key === 'Enter') {
      handleEquals();
    }
    // مسح
    else if (key === 'Escape' || key === 'c' || key === 'C') {
      clear();
    }
    // حذف آخر رقم
    else if (key === 'Backspace' || key === 'Delete') {
      deleteLastDigit();
    }
  };

  // إضافة مستمع الأحداث للكيبورد
  useEffect(() => {
    window.addEventListener('keydown', handleKeyPress);

    // تنظيف المستمع عند إلغاء تحميل المكون
    return () => {
      window.removeEventListener('keydown', handleKeyPress);
    };
  }, [display, operation, previousValue, waitingForOperand]);

  const Button: React.FC<{
    onClick: () => void;
    className?: string;
    children: React.ReactNode;
    keyboardKey?: string;
  }> = ({ onClick, className = '', children, keyboardKey }) => {
    const isPressed = keyboardKey && (pressedKey === keyboardKey ||
      (keyboardKey === '×' && (pressedKey === '*' || pressedKey === 'x' || pressedKey === 'X')) ||
      (keyboardKey === '÷' && (pressedKey === '/' || pressedKey === '÷')) ||
      (keyboardKey === '=' && (pressedKey === '=' || pressedKey === 'Enter')) ||
      (keyboardKey === 'clear' && (pressedKey === 'Escape' || pressedKey === 'c' || pressedKey === 'C')) ||
      (keyboardKey === 'delete' && (pressedKey === 'Backspace' || pressedKey === 'Delete')) ||
      (keyboardKey === '.' && (pressedKey === '.' || pressedKey === ',')));

    return (
      <button
        onClick={onClick}
        className={`h-20 rounded-2xl font-bold text-xl transition-all duration-200 transform hover:scale-105 active:scale-95 shadow-lg hover:shadow-2xl border border-gray-600 ${
          isPressed ? 'scale-95 brightness-125' : ''
        } ${className}`}
      >
        {children}
      </button>
    );
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 p-6">
      <div className="max-w-lg mx-auto">
        {/* Header */}
        <div className="bg-gradient-to-r from-gray-800 to-gray-900 rounded-3xl shadow-2xl p-6 mb-6 border border-gray-700">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center gap-4">
              <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-2xl flex items-center justify-center shadow-lg">
                <Calculator size={24} className="text-white" />
              </div>
              <div>
                <h1 className="text-2xl font-bold bg-gradient-to-br from-white to-blue-200 bg-clip-text text-transparent font-arabic drop-shadow">الآلة الحاسبة</h1>
                <p className="text-gray-300 text-sm">حاسبة احترافية للعمليات الحسابية</p>
                <p className="text-blue-300 text-xs mt-1">⌨️ يمكن الإدخال من الكيبورد</p>
              </div>
            </div>
            {setCurrentView && (
              <button
                onClick={() => setCurrentView('home')}
                className="bg-gradient-to-r from-green-500 to-green-600 text-white p-3 rounded-xl hover:from-green-600 hover:to-green-700 transition-all duration-300 flex items-center gap-2 shadow-lg hover:shadow-xl transform hover:scale-105"
                title="العودة للرئيسية"
              >
                <Home size={20} />
                <span className="hidden sm:inline">الرئيسية</span>
              </button>
            )}
          </div>
        </div>

        {/* Calculator */}
        <div className="bg-gradient-to-br from-gray-800 to-gray-900 rounded-3xl shadow-2xl p-8 border border-gray-700">
          {/* Display */}
          <div className="bg-gradient-to-r from-black to-gray-900 rounded-2xl p-8 mb-8 border border-gray-600 shadow-inner">
            <div className="text-right">
              <div className="text-green-400 text-5xl font-mono font-bold break-all min-h-[60px] flex items-center justify-end">
                {display}
              </div>
              {operation && previousValue !== null && (
                <div className="text-gray-400 text-lg mt-3 font-mono">
                  {previousValue} {operation}
                </div>
              )}
            </div>
          </div>

          {/* Buttons Grid */}
          <div className="grid grid-cols-4 gap-4">
            {/* Row 1 */}
            <Button
              onClick={clear}
              className="bg-gradient-to-r from-red-600 to-red-700 text-white hover:from-red-700 hover:to-red-800 shadow-red-500/25"
              keyboardKey="clear"
            >
              <RotateCcw size={24} />
            </Button>
            <Button
              onClick={deleteLastDigit}
              className="bg-gradient-to-r from-orange-600 to-orange-700 text-white hover:from-orange-700 hover:to-orange-800 shadow-orange-500/25"
              keyboardKey="delete"
            >
              <Delete size={24} />
            </Button>
            <Button
              onClick={() => performOperation('÷')}
              className="bg-gradient-to-r from-blue-600 to-blue-700 text-white hover:from-blue-700 hover:to-blue-800 shadow-blue-500/25"
              keyboardKey="÷"
            >
              ÷
            </Button>
            <Button
              onClick={() => performOperation('×')}
              className="bg-gradient-to-r from-blue-600 to-blue-700 text-white hover:from-blue-700 hover:to-blue-800 shadow-blue-500/25"
              keyboardKey="×"
            >
              ×
            </Button>

            {/* Row 2 */}
            <Button
              onClick={() => inputNumber('7')}
              className="bg-gradient-to-r from-gray-600 to-gray-700 text-white hover:from-gray-700 hover:to-gray-800 shadow-gray-500/25"
              keyboardKey="7"
            >
              7
            </Button>
            <Button
              onClick={() => inputNumber('8')}
              className="bg-gradient-to-r from-gray-600 to-gray-700 text-white hover:from-gray-700 hover:to-gray-800 shadow-gray-500/25"
              keyboardKey="8"
            >
              8
            </Button>
            <Button
              onClick={() => inputNumber('9')}
              className="bg-gradient-to-r from-gray-600 to-gray-700 text-white hover:from-gray-700 hover:to-gray-800 shadow-gray-500/25"
              keyboardKey="9"
            >
              9
            </Button>
            <Button
              onClick={() => performOperation('-')}
              className="bg-gradient-to-r from-blue-600 to-blue-700 text-white hover:from-blue-700 hover:to-blue-800 shadow-blue-500/25"
              keyboardKey="-"
            >
              -
            </Button>

            {/* Row 3 */}
            <Button
              onClick={() => inputNumber('4')}
              className="bg-gradient-to-r from-gray-600 to-gray-700 text-white hover:from-gray-700 hover:to-gray-800 shadow-gray-500/25"
              keyboardKey="4"
            >
              4
            </Button>
            <Button
              onClick={() => inputNumber('5')}
              className="bg-gradient-to-r from-gray-600 to-gray-700 text-white hover:from-gray-700 hover:to-gray-800 shadow-gray-500/25"
              keyboardKey="5"
            >
              5
            </Button>
            <Button
              onClick={() => inputNumber('6')}
              className="bg-gradient-to-r from-gray-600 to-gray-700 text-white hover:from-gray-700 hover:to-gray-800 shadow-gray-500/25"
              keyboardKey="6"
            >
              6
            </Button>
            <Button
              onClick={() => performOperation('+')}
              className="bg-gradient-to-r from-blue-600 to-blue-700 text-white hover:from-blue-700 hover:to-blue-800 shadow-blue-500/25"
              keyboardKey="+"
            >
              +
            </Button>

            {/* Row 4 */}
            <Button
              onClick={() => inputNumber('1')}
              className="bg-gradient-to-r from-gray-600 to-gray-700 text-white hover:from-gray-700 hover:to-gray-800 shadow-gray-500/25"
              keyboardKey="1"
            >
              1
            </Button>
            <Button
              onClick={() => inputNumber('2')}
              className="bg-gradient-to-r from-gray-600 to-gray-700 text-white hover:from-gray-700 hover:to-gray-800 shadow-gray-500/25"
              keyboardKey="2"
            >
              2
            </Button>
            <Button
              onClick={() => inputNumber('3')}
              className="bg-gradient-to-r from-gray-600 to-gray-700 text-white hover:from-gray-700 hover:to-gray-800 shadow-gray-500/25"
              keyboardKey="3"
            >
              3
            </Button>
            <Button
              onClick={handleEquals}
              className="bg-gradient-to-r from-green-600 to-green-700 text-white hover:from-green-700 hover:to-green-800 row-span-2 shadow-green-500/25"
              keyboardKey="="
            >
              =
            </Button>

            {/* Row 5 */}
            <Button
              onClick={() => inputNumber('0')}
              className="bg-gradient-to-r from-gray-600 to-gray-700 text-white hover:from-gray-700 hover:to-gray-800 col-span-2 shadow-gray-500/25"
              keyboardKey="0"
            >
              0
            </Button>
            <Button
              onClick={inputDecimal}
              className="bg-gradient-to-r from-gray-600 to-gray-700 text-white hover:from-gray-700 hover:to-gray-800 shadow-gray-500/25"
              keyboardKey="."
            >
              .
            </Button>
          </div>

          {/* Keyboard Shortcuts Help */}
          <div className="mt-6 bg-gradient-to-r from-gray-700 to-gray-800 rounded-2xl p-4 border border-gray-600">
            <h3 className="text-white font-bold text-sm mb-3 text-center">اختصارات الكيبورد</h3>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-3 text-xs">
              <div className="bg-gray-600 rounded-lg p-2 text-center">
                <div className="text-blue-300 font-bold">0-9</div>
                <div className="text-gray-300">الأرقام</div>
              </div>
              <div className="bg-gray-600 rounded-lg p-2 text-center">
                <div className="text-blue-300 font-bold">+ - * /</div>
                <div className="text-gray-300">العمليات</div>
              </div>
              <div className="bg-gray-600 rounded-lg p-2 text-center">
                <div className="text-blue-300 font-bold">Enter =</div>
                <div className="text-gray-300">يساوي</div>
              </div>
              <div className="bg-gray-600 rounded-lg p-2 text-center">
                <div className="text-blue-300 font-bold">Esc</div>
                <div className="text-gray-300">مسح</div>
              </div>
              <div className="bg-gray-600 rounded-lg p-2 text-center">
                <div className="text-blue-300 font-bold">Backspace</div>
                <div className="text-gray-300">حذف</div>
              </div>
              <div className="bg-gray-600 rounded-lg p-2 text-center">
                <div className="text-blue-300 font-bold">. ,</div>
                <div className="text-gray-300">فاصلة</div>
              </div>
              <div className="bg-gray-600 rounded-lg p-2 text-center">
                <div className="text-blue-300 font-bold">x X</div>
                <div className="text-gray-300">ضرب</div>
              </div>
              <div className="bg-gray-600 rounded-lg p-2 text-center">
                <div className="text-blue-300 font-bold">C c</div>
                <div className="text-gray-300">مسح</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CalculatorView;
