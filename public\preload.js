const { contextBridge, ipc<PERSON>enderer } = require('electron');

contextBridge.exposeInMainWorld('electronAPI', {
  // معلومات التطبيق
  isElectron: true,
  getAppVersion: () => ipcRenderer.invoke('get-app-version'),
  getUserDataPath: () => ipcRenderer.invoke('get-user-data-path'),

  // وظائف قاعدة البيانات
  getCategories: () => ipcRenderer.invoke('get-categories'),
  addCategory: (name, description) => ipcRenderer.invoke('add-category', name, description),

  getInventory: () => ipcRenderer.invoke('get-inventory'),
  addInventoryItem: (categoryId, name, pallets, boxes, totalBoxes, location, date, notes) =>
    ipcRenderer.invoke('add-inventory-item', categoryId, name, pallets, boxes, totalBoxes, location, date, notes),
  updateInventory: (id, pallets, boxes, totalBoxes, notes) =>
    ipcRenderer.invoke('update-inventory', id, pallets, boxes, totalBoxes, notes),
  updateInventoryWithDate: (id, pallets, boxes, totalBoxes, notes, date) =>
    ipcRenderer.invoke('update-inventory-with-date', id, pallets, boxes, totalBoxes, notes, date),
  deleteInventoryItem: (id) => ipcRenderer.invoke('delete-inventory-item', id),

  getMovements: () => ipcRenderer.invoke('get-movements'),
  addMovement: (inventoryId, type, quantity, notes) =>
    ipcRenderer.invoke('add-movement', inventoryId, type, quantity, notes),
  clearMovements: () => ipcRenderer.invoke('clear-movements'),

  // وظائف مساعدة
  findItemIdByName: (name) => ipcRenderer.invoke('find-item-id-by-name', name)
});
