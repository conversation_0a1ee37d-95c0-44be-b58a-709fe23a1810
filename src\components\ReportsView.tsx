import React, { useState } from 'react';
import {
  <PERSON><PERSON>ex<PERSON>,
  Bar<PERSON>hart3,
  Download,
  Calendar,
  Leaf,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  TrendingUp,
  Truck,
  Package,
  DollarSign,
  Store,
  Filter,
  Printer,
  Eye,
  X,
  Save,
  Users,
  Plus
} from 'lucide-react';
import Modal from './Modal';
import { createPDFFromHTML, createInventoryReportHTML, createFinancialReportHTML } from '../utils/arabicPDF';
import { createSimpleInventoryPDF, createSimpleFinancialPDF, createInventoryHTML, createFinancialHTML, createInventoryHTMLPreview, createFinancialHTMLPreview } from '../utils/simplePDF';

interface ReportsViewProps {
  zones: any[];
  employees: any[];
  suppliers: any[];
  advances: any[];
  sales: any[];
  sprayingRecords: any[];
  fertilizationRecords: any[];
  inventoryItems: any[];
}

const ReportsView: React.FC<ReportsViewProps> = ({
  zones,
  employees,
  suppliers,
  advances,
  sales,
  sprayingRecords,
  fertilizationRecords,
  inventoryItems
}) => {
  const [activeTab, setActiveTab] = useState('overview');
  const [isExportModalOpen, setIsExportModalOpen] = useState(false);
  const [selectedReportType, setSelectedReportType] = useState<string>('');

  const formatCurrency = (amount: number) => new Intl.NumberFormat('ar-JO', { style: 'currency', currency: 'JOD' }).format(amount);

  const handleExportReport = async () => {
    if (selectedReportType === 'financial') {
      await createFinancialHTML([...sales, ...advances]);
    } else if (selectedReportType === 'operations') {
      alert('تصدير تقرير العمليات غير مفعل بعد.');
    } else if (selectedReportType === 'inventory') {
      await createInventoryHTML(inventoryItems);
    }
    setIsExportModalOpen(false);
  };

  // حساب الإحصائيات
  const totalZones = zones.length;
  const activeZones = zones.filter(zone => zone.status === 'active').length;
  const totalEmployees = employees.length;
  const activeEmployees = employees.filter(emp => emp.status === 'active').length;
  const totalSuppliers = suppliers.length;
  const activeSuppliers = suppliers.filter(sup => sup.status === 'active').length;
  const totalAdvances = advances.reduce((sum, adv) => sum + adv.amount, 0);
  const pendingAdvances = advances.filter(adv => adv.status === 'pending').length;
  const totalSales = sales.reduce((sum, sale) => sum + sale.totalAmount, 0);
  const completedSales = sales.filter(sale => sale.status === 'completed').length;
  const totalSpraying = sprayingRecords.length;
  const totalFertilization = fertilizationRecords.length;
  const totalInventory = inventoryItems.reduce((sum, item) => sum + (item.quantity || 0), 0);

  const tabs = [
    { id: 'overview', name: 'نظرة عامة', icon: <BarChart3 className="inline w-5 h-5 mr-2" /> },
    { id: 'zones', name: 'المراحل الزراعية', icon: <Package className="inline w-5 h-5 mr-2" /> },
    { id: 'employees', name: 'الموظفين', icon: <Users className="inline w-5 h-5 mr-2" /> },
    { id: 'financial', name: 'المالية', icon: <DollarSign className="inline w-5 h-5 mr-2" /> },
    { id: 'operations', name: 'العمليات', icon: <Truck className="inline w-5 h-5 mr-2" /> }
  ];

  const renderOverview = () => (
    <div className="space-y-6">
      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-gradient-to-br from-blue-500 to-blue-600 text-white rounded-xl p-6 shadow-xl">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-xl font-bold mb-2">المراحل الزراعية</h3>
              <p className="text-4xl md:text-5xl font-extrabold mb-1">{totalZones}</p>
              <p className="text-blue-100 text-lg font-medium">{activeZones} نشط</p>
            </div>
            <div className="text-5xl">🌱</div>
          </div>
        </div>

        <div className="bg-gradient-to-br from-green-500 to-green-600 text-white rounded-xl p-6 shadow-xl">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-xl font-bold mb-2">الموظفين</h3>
              <p className="text-4xl md:text-5xl font-extrabold mb-1">{totalEmployees}</p>
              <p className="text-green-100 text-lg font-medium">{activeEmployees} نشط</p>
            </div>
            <div className="text-5xl">👥</div>
          </div>
        </div>

        <div className="bg-gradient-to-br from-purple-500 to-purple-600 text-white rounded-xl p-6 shadow-xl">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-xl font-bold mb-2">المبيعات</h3>
              <p className="text-4xl md:text-5xl font-extrabold mb-1">{totalSales.toLocaleString()}</p>
              <p className="text-purple-100 text-lg font-medium">{completedSales} مكتمل</p>
            </div>
            <div className="text-5xl">💰</div>
          </div>
        </div>

        <div className="bg-gradient-to-br from-orange-500 to-orange-600 text-white rounded-xl p-6 shadow-xl">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-xl font-bold mb-2">السلف</h3>
              <p className="text-4xl md:text-5xl font-extrabold mb-1">{totalAdvances.toLocaleString()}</p>
              <p className="text-orange-100 text-lg font-medium">{pendingAdvances} قيد الانتظار</p>
            </div>
            <div className="text-5xl">💳</div>
          </div>
        </div>
      </div>

      {/* Recent Activity */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="bg-white rounded-xl shadow-lg p-6">
          <h3 className="text-2xl font-bold text-gray-800 mb-6">النشاط الأخير</h3>
          <div className="space-y-4">
            <div className="flex items-center space-x-4 space-x-reverse">
              <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center">
                <span className="text-green-600 text-xl">🌱</span>
              </div>
              <div>
                <p className="text-lg font-bold text-gray-900">تم إضافة مرحلة جديدة</p>
                <p className="text-sm text-gray-500 font-medium">منذ ساعتين</p>
              </div>
            </div>
            <div className="flex items-center space-x-4 space-x-reverse">
              <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
                <span className="text-blue-600 text-xl">💰</span>
              </div>
              <div>
                <p className="text-lg font-bold text-gray-900">مبيعات جديدة</p>
                <p className="text-sm text-gray-500 font-medium">منذ 3 ساعات</p>
              </div>
            </div>
            <div className="flex items-center space-x-4 space-x-reverse">
              <div className="w-12 h-12 bg-yellow-100 rounded-full flex items-center justify-center">
                <span className="text-yellow-600 text-xl">👥</span>
              </div>
              <div>
                <p className="text-lg font-bold text-gray-900">تم إضافة موظف جديد</p>
                <p className="text-sm text-gray-500 font-medium">منذ 5 ساعات</p>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-lg p-6">
          <h3 className="text-2xl font-bold text-gray-800 mb-6">إحصائيات سريعة</h3>
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <span className="text-lg font-semibold text-gray-600">إجمالي الموردين</span>
              <span className="text-xl font-bold text-gray-900">{totalSuppliers}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-lg font-semibold text-gray-600">عمليات الرش</span>
              <span className="text-xl font-bold text-gray-900">{totalSpraying}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-lg font-semibold text-gray-600">عمليات التسميد</span>
              <span className="text-xl font-bold text-gray-900">{totalFertilization}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-lg font-semibold text-gray-600">المخزون الإجمالي</span>
              <span className="text-xl font-bold text-gray-900">{totalInventory}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  const renderZonesReport = () => (
    <div className="space-y-6">
      <div className="bg-white rounded-xl shadow-lg p-6">
        <h3 className="text-3xl font-bold text-gray-800 mb-6">تقرير المراحل الزراعية</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          <div className="bg-green-50 p-6 rounded-xl shadow-lg">
            <h4 className="text-xl font-bold text-green-800 mb-2">المراحل النشطة</h4>
            <p className="text-4xl font-extrabold text-green-600">{activeZones}</p>
          </div>
          <div className="bg-gray-50 p-6 rounded-xl shadow-lg">
            <h4 className="text-xl font-bold text-gray-800 mb-2">المراحل غير النشطة</h4>
            <p className="text-4xl font-extrabold text-gray-600">{totalZones - activeZones}</p>
          </div>
          <div className="bg-blue-50 p-6 rounded-xl shadow-lg">
            <h4 className="text-xl font-bold text-blue-800 mb-2">إجمالي المراحل</h4>
            <p className="text-4xl font-extrabold text-blue-600">{totalZones}</p>
          </div>
        </div>
        
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-4 text-right text-lg font-bold text-gray-700">المرحلة</th>
                <th className="px-6 py-4 text-right text-lg font-bold text-gray-700">نوع المحصول</th>
                <th className="px-6 py-4 text-right text-lg font-bold text-gray-700">المساحة</th>
                <th className="px-6 py-4 text-right text-lg font-bold text-gray-700">الحالة</th>
                <th className="px-6 py-4 text-right text-lg font-bold text-gray-700">المحابس النشطة</th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-200">
              {zones.slice(0, 10).map((zone) => (
                <tr key={zone.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 text-lg font-semibold text-gray-900">{zone.name}</td>
                  <td className="px-6 py-4 text-lg font-semibold text-gray-900">{zone.cropType}</td>
                  <td className="px-6 py-4 text-lg font-semibold text-gray-900">{zone.area} متر²</td>
                  <td className="px-6 py-4 text-lg">
                    <span className={`px-3 py-2 text-sm font-bold rounded-full ${
                      zone.status === 'active' ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'
                    }`}>
                      {zone.status === 'active' ? 'نشط' : 'غير نشط'}
                    </span>
                  </td>
                  <td className="px-6 py-4 text-lg font-semibold text-gray-900">
                    {zone.valves?.filter((v: any) => v.isActive).length || 0}/{zone.valves?.length || 0}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );

  const renderEmployeesReport = () => (
    <div className="space-y-6">
      <div className="bg-white rounded-xl shadow-lg p-6">
        <h3 className="text-lg font-semibold text-gray-800 mb-4">تقرير الموظفين</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
          <div className="bg-green-50 p-4 rounded-lg">
            <h4 className="font-semibold text-green-800">الموظفين النشطين</h4>
            <p className="text-2xl font-bold text-green-600">{activeEmployees}</p>
          </div>
          <div className="bg-gray-50 p-4 rounded-lg">
            <h4 className="font-semibold text-gray-800">الموظفين غير النشطين</h4>
            <p className="text-2xl font-bold text-gray-600">{totalEmployees - activeEmployees}</p>
          </div>
          <div className="bg-blue-50 p-4 rounded-lg">
            <h4 className="font-semibold text-blue-800">إجمالي الموظفين</h4>
            <p className="text-2xl font-bold text-blue-600">{totalEmployees}</p>
          </div>
        </div>
        
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-4 py-2 text-right text-sm font-medium text-gray-500">الموظف</th>
                <th className="px-4 py-2 text-right text-sm font-medium text-gray-500">المنصب</th>
                <th className="px-4 py-2 text-right text-sm font-medium text-gray-500">الراتب</th>
                <th className="px-4 py-2 text-right text-sm font-medium text-gray-500">الحالة</th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-200">
              {employees.slice(0, 10).map((employee) => (
                <tr key={employee.id}>
                  <td className="px-4 py-2 text-sm text-gray-900">{employee.name}</td>
                  <td className="px-4 py-2 text-sm text-gray-900">{employee.position}</td>
                  <td className="px-4 py-2 text-sm text-gray-900">{formatCurrency(employee.salary)}</td>
                  <td className="px-4 py-2 text-sm">
                    <span className={`px-2 py-1 text-xs rounded-full ${
                      employee.status === 'active' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                    }`}>
                      {employee.status === 'active' ? 'نشط' : 'غير نشط'}
                    </span>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );

  const renderFinancialReport = () => (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="bg-white rounded-xl shadow-lg p-6">
          <h3 className="text-2xl font-bold text-gray-800 mb-6">تقرير المبيعات</h3>
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600">إجمالي المبيعات</span>
              <span className="text-lg font-bold text-green-600">{formatCurrency(totalSales)}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600">المبيعات المكتملة</span>
              <span className="text-sm font-semibold text-gray-900">{completedSales}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600">المبيعات المعلقة</span>
              <span className="text-sm font-semibold text-gray-900">{sales.filter(s => s.status === 'pending').length}</span>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-lg p-6">
          <h3 className="text-2xl font-bold text-gray-800 mb-6">تقرير السلف</h3>
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600">إجمالي السلف</span>
              <span className="text-lg font-bold text-orange-600">{formatCurrency(totalAdvances)}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600">قيد الانتظار</span>
              <span className="text-sm font-semibold text-gray-900">{pendingAdvances}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600">المدفوعة</span>
              <span className="text-sm font-semibold text-gray-900">{advances.filter(a => a.status === 'paid').length}</span>
            </div>
          </div>
        </div>
      </div>

      <div className="bg-white rounded-xl shadow-lg p-6">
        <h3 className="text-2xl font-bold text-gray-800 mb-6">آخر المعاملات المالية</h3>
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-4 text-right text-lg font-bold text-gray-700">النوع</th>
                <th className="px-6 py-4 text-right text-lg font-bold text-gray-700">المبلغ</th>
                <th className="px-6 py-4 text-right text-lg font-bold text-gray-700">التاريخ</th>
                <th className="px-6 py-4 text-right text-lg font-bold text-gray-700">الحالة</th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-200">
              {[...sales, ...advances].slice(0, 10).map((item, index) => (
                <tr key={index} className="hover:bg-gray-50">
                  <td className="px-6 py-4 text-lg font-semibold text-gray-900">
                    {item.customerName ? 'مبيعات' : 'سلفة'}
                  </td>
                  <td className="px-6 py-4 text-lg font-bold text-gray-900">
                    {formatCurrency(item.totalAmount || item.amount)}
                  </td>
                  <td className="px-6 py-4 text-lg font-semibold text-gray-900">
                    {new Date(item.date).toLocaleDateString('ar-SA')}
                  </td>
                  <td className="px-6 py-4 text-lg">
                    <span className={`px-3 py-2 text-sm font-bold rounded-full ${
                      item.status === 'completed' || item.status === 'paid' 
                        ? 'bg-green-100 text-green-800' 
                        : item.status === 'pending'
                        ? 'bg-yellow-100 text-yellow-800'
                        : 'bg-red-100 text-red-800'
                    }`}>
                      {item.status === 'completed' ? 'مكتمل' : 
                       item.status === 'paid' ? 'مدفوع' :
                       item.status === 'pending' ? 'قيد الانتظار' : 'ملغي'}
                    </span>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );

  const renderOperationsReport = () => (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="bg-white rounded-xl shadow-lg p-6">
          <h3 className="text-2xl font-bold text-gray-800 mb-6">عمليات الرش</h3>
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600">إجمالي العمليات</span>
              <span className="text-lg font-bold text-blue-600">{totalSpraying}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600">هذا الشهر</span>
              <span className="text-sm font-semibold text-gray-900">
                {sprayingRecords.filter(r => {
                  const recordDate = new Date(r.date);
                  const now = new Date();
                  return recordDate.getMonth() === now.getMonth() && 
                         recordDate.getFullYear() === now.getFullYear();
                }).length}
              </span>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-lg p-6">
          <h3 className="text-2xl font-bold text-gray-800 mb-6">عمليات التسميد</h3>
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600">إجمالي العمليات</span>
              <span className="text-lg font-bold text-green-600">{totalFertilization}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600">هذا الشهر</span>
              <span className="text-sm font-semibold text-gray-900">
                {fertilizationRecords.filter(r => {
                  const recordDate = new Date(r.date);
                  const now = new Date();
                  return recordDate.getMonth() === now.getMonth() && 
                         recordDate.getFullYear() === now.getFullYear();
                }).length}
              </span>
            </div>
          </div>
        </div>
      </div>

      <div className="bg-white rounded-xl shadow-lg p-6">
        <h3 className="text-2xl font-bold text-gray-800 mb-6">المخزون</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="bg-blue-50 p-6 rounded-xl shadow-lg">
            <h4 className="text-xl font-bold text-blue-800 mb-2">إجمالي المخزون</h4>
            <p className="text-4xl font-extrabold text-blue-600">{totalInventory}</p>
          </div>
          <div className="bg-green-50 p-6 rounded-xl shadow-lg">
            <h4 className="text-xl font-bold text-green-800 mb-2">المواد المتوفرة</h4>
            <p className="text-4xl font-extrabold text-green-600">
              {inventoryItems.filter(item => (item.quantity || 0) > 0).length}
            </p>
          </div>
          <div className="bg-red-50 p-6 rounded-xl shadow-lg">
            <h4 className="text-xl font-bold text-red-800 mb-2">المواد النافدة</h4>
            <p className="text-4xl font-extrabold text-red-600">
              {inventoryItems.filter(item => (item.quantity || 0) <= 0).length}
            </p>
          </div>
        </div>
      </div>
    </div>
  );

  const renderContent = () => {
    switch (activeTab) {
      case 'overview':
        return renderOverview();
      case 'zones':
        return renderZonesReport();
      case 'employees':
        return renderEmployeesReport();
      case 'financial':
        return renderFinancialReport();
      case 'operations':
        return renderOperationsReport();
      default:
        return renderOverview();
    }
  };

  return (
    <div className="min-h-screen w-full bg-gradient-to-br from-blue-50 via-green-50 to-indigo-100 py-8 px-2 md:px-8 space-y-6">
      {/* Header مع التدرج */}
      <div className="bg-gradient-to-r from-indigo-700 via-blue-600 to-green-600 text-white p-6 shadow-2xl rounded-3xl mb-8">
        <div className="max-w-7xl mx-auto flex items-center justify-between">
          <div className="flex items-center gap-4">
            <div className="bg-white/30 p-3 rounded-2xl shadow-lg border-2 border-indigo-300 flex items-center justify-center">
              <BarChart3 className="h-10 w-10 text-indigo-700" />
            </div>
            <div>
              <h1 className="text-4xl md:text-5xl font-extrabold text-white drop-shadow-lg tracking-wide">التقارير والإحصائيات</h1>
              <p className="text-indigo-100 mt-2 text-lg font-medium">نظرة شاملة على أداء المزرعة</p>
            </div>
          </div>
          <button className="bg-white text-blue-600 hover:bg-blue-50 px-8 py-4 rounded-xl font-bold text-lg flex items-center gap-3 transition-all shadow-lg hover:shadow-xl border-2 border-blue-400 hover:border-blue-500 transform hover:scale-105"
            onClick={() => setIsExportModalOpen(true)}>
            <Download size={24} />
            <span>تصدير تقرير</span>
          </button>
        </div>
      </div>

      <div className="max-w-7xl mx-auto p-0 md:p-6">
        {/* شريط التبويبات */}
        <div className="bg-white rounded-2xl shadow-md mb-6 border border-blue-100">
          <div className="flex flex-wrap border-b border-blue-100">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`px-8 py-5 text-lg font-bold transition-all flex items-center gap-3 rounded-t-2xl border-b-4 ${
                  activeTab === tab.id
                    ? 'text-blue-700 border-blue-600 bg-blue-50 shadow-lg'
                    : 'text-gray-500 border-transparent hover:text-blue-600 hover:bg-blue-50'
                }`}
              >
                {tab.icon}
                {tab.name}
              </button>
            ))}
          </div>
        </div>

        {/* محتوى التقرير */}
        {renderContent()}
      </div>

      <Modal isOpen={isExportModalOpen} onClose={() => setIsExportModalOpen(false)} title="اختر نوع التقرير للتصدير">
        <div className="space-y-6">
          <button onClick={() => { setSelectedReportType('financial'); handleExportReport(); }}
            className="w-full bg-gradient-to-r from-green-500 to-blue-500 text-white py-4 rounded-xl font-bold text-xl shadow-lg hover:from-green-600 hover:to-blue-600 transition-all transform hover:scale-105">تقرير مالي</button>
          <button onClick={() => { setSelectedReportType('operations'); handleExportReport(); }}
            className="w-full bg-gradient-to-r from-orange-500 to-yellow-400 text-white py-4 rounded-xl font-bold text-xl shadow-lg hover:from-orange-600 hover:to-yellow-500 transition-all transform hover:scale-105">تقرير العمليات</button>
          <button onClick={() => { setSelectedReportType('inventory'); handleExportReport(); }}
            className="w-full bg-gradient-to-r from-blue-600 to-indigo-500 text-white py-4 rounded-xl font-bold text-xl shadow-lg hover:from-blue-700 hover:to-indigo-600 transition-all transform hover:scale-105">تقرير المخزون</button>
        </div>
      </Modal>
    </div>
  );
};

export default ReportsView;