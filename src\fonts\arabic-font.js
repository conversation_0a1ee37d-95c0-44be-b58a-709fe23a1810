// خطوط عربية مضمنة لاستخدامها مع jsPDF
import { jsPDF } from 'jspdf';

// دالة لإضافة الخط العربي إلى jsPDF
export const addArabicFont = (pdf) => {
  // إضافة خط Cairo العربي
  pdf.addFont('https://fonts.gstatic.com/s/cairo/v28/SLXgc1nY6HkvalIhTp2mxdt0UX8geg.woff2', 'Cairo', 'normal');
  pdf.addFont('https://fonts.gstatic.com/s/cairo/v28/SLXfc1nY6HkvalIhTp2mxdt0UX8geg.woff2', 'Cairo', 'bold');

  // تعيين الخط الافتراضي
  pdf.setFont('Cairo');
};

// دالة لمعالجة النص العربي
export const processArabicText = (text) => {
  // إذا كان النص يحتوي على أحرف عربية
  if (/[\u0600-\u06FF]/.test(text)) {
    // عكس النص للعرض الصحيح
    return text.split('').reverse().join('');
  }
  return text;
};

// دالة لتحسين عرض النص العربي في PDF
export const formatArabicTextForPDF = (text) => {
  if (!text) return '';

  // تنظيف النص
  let cleanText = text.toString().trim();

  // إذا كان النص يحتوي على أحرف عربية
  if (/[\u0600-\u06FF]/.test(cleanText)) {
    // معالجة الأرقام العربية
    cleanText = cleanText.replace(/[٠-٩]/g, (match) => {
      const arabicDigits = '٠١٢٣٤٥٦٧٨٩';
      const englishDigits = '0123456789';
      return englishDigits[arabicDigits.indexOf(match)];
    });

    // معالجة التشكيل
    cleanText = cleanText.replace(/[\u064B-\u0652]/g, '');

    return cleanText;
  }

  return cleanText;
};

// خط Cairo مضمن (base64 مبسط)
export const cairoFontBase64 = {
  normal: 'data:font/truetype;charset=utf-8;base64,', // سيتم إضافة البيانات الفعلية
  bold: 'data:font/truetype;charset=utf-8;base64,'   // سيتم إضافة البيانات الفعلية
};
