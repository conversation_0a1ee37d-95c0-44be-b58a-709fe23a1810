import React, { useState, useEffect } from 'react';
import { Leaf, Plus, Edit, Calendar, Droplets, Trash2, AlertTriangle } from 'lucide-react';
import { Zone } from '../types';
import { formatDate } from '../utils/database';
import { enhancedDB } from '../utils/enhancedDatabase';
import Modal from './Modal';

interface ZonesViewProps {
  zones: Zone[];
  onZoneUpdate: (zoneId: number | string, updatedZone: Zone) => void;
  onZoneDelete?: (zoneId: number | string) => void;
  onZoneAdd?: (zone: Omit<Zone, 'id'>) => void;
}

const ZonesView: React.FC<ZonesViewProps> = ({ zones, onZoneUpdate, onZoneDelete, onZoneAdd }) => {
  const [isLoading, setIsLoading] = useState(false);
  const [selectedZone, setSelectedZone] = useState<Zone | null>(null);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [zoneToDelete, setZoneToDelete] = useState<Zone | null>(null);
  
  // حالة الفلاتر
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [lettuceTypeFilter, setLettuceTypeFilter] = useState<string>('all');

  // فلترة المراحل
  const filteredZones = zones.filter(zone => {
    const statusMatch = statusFilter === 'all' || zone.status === statusFilter;
    const lettuceTypeMatch = lettuceTypeFilter === 'all' || zone.lettuceType === lettuceTypeFilter;
    return statusMatch && lettuceTypeMatch;
  });

  // إعادة تعيين الفلاتر
  const resetFilters = () => {
    setStatusFilter('all');
    setLettuceTypeFilter('all');
  };


  const handleEditZone = (zone: Zone) => {
    setSelectedZone(zone);
    setIsEditModalOpen(true);
  };

  const handleAddZone = () => {
    setSelectedZone(null);
    setIsAddModalOpen(true);
  };

  const handleDeleteZone = (zone: Zone) => {
    setZoneToDelete(zone);
    setIsDeleteModalOpen(true);
  };

  const handleSaveZone = async (zoneData: Omit<Zone, 'id'> | Zone) => {
    try {
      if ('id' in zoneData && zoneData.id) {
        // تحديث مرحلة موجودة
        await onZoneUpdate?.(zoneData.id, zoneData as Zone);
        setIsEditModalOpen(false);
        setSelectedZone(null);
      } else {
        // إضافة مرحلة جديدة
        await onZoneAdd?.(zoneData as Omit<Zone, 'id'>);
        setIsAddModalOpen(false);
      }
    } catch (error) {
      console.error('خطأ في حفظ المرحلة:', error);
      alert('حدث خطأ أثناء حفظ المرحلة');
    }
  };

  const confirmDeleteZone = async () => {
    if (!zoneToDelete) return;

    try {
      // إخبار المكون الأب بالحذف
      await onZoneDelete?.(zoneToDelete.id);
      
      setIsDeleteModalOpen(false);
      setZoneToDelete(null);
    } catch (error) {
      console.error('خطأ في حذف المرحلة:', error);
      alert('حدث خطأ أثناء حذف المرحلة');
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'status-active';
      case 'harvesting':
        return 'status-warning';
      case 'preparing':
        return 'status-inactive';
      default:
        return 'status-inactive';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'active':
        return 'نشطة';
      case 'harvesting':
        return 'جاهزة للحصاد';
      case 'preparing':
        return 'قيد التحضير';
      default:
        return 'غير نشطة';
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-emerald-600"></div>
      </div>
    );
  }

  return (
    <div className="min-h-screen w-full bg-gradient-to-br from-green-50 via-emerald-50 to-teal-50 py-8 px-2 md:px-8">
      {/* Header */}
      <div className="bg-gradient-to-r from-emerald-600 via-green-600 to-lime-600 text-white p-8 shadow-2xl rounded-2xl mb-6 relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-r from-emerald-500/20 to-lime-500/20"></div>
        <div className="absolute top-0 left-0 w-full h-full opacity-30 bg-gradient-to-br from-white/10 to-transparent"></div>
        <div className="container mx-auto relative z-10">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-6">
              <div className="bg-white/25 p-4 rounded-2xl shadow-xl border border-white/40 backdrop-blur-sm">
                <Leaf size={32} className="text-white drop-shadow-lg" />
              </div>
              <div>
                <h1 className="text-4xl font-bold font-arabic tracking-wide drop-shadow-lg mb-2">إدارة المراحل الزراعية</h1>
                <p className="text-emerald-100 text-lg font-medium tracking-wider">Advanced Farm Stages Management</p>
              </div>
            </div>
            <div className="text-lg bg-white/25 px-6 py-3 rounded-2xl backdrop-blur-sm border border-white/30 shadow-lg">
              <div className="flex items-center gap-3">
                <Calendar className="h-5 w-5" />
                <span className="font-arabic font-medium">{formatDate(new Date())}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <div className="bg-gradient-to-br from-emerald-50 to-green-100 rounded-3xl p-6 shadow-xl hover:shadow-2xl transition-all duration-300 border-2 border-emerald-200 hover:border-emerald-300 transform hover:scale-105">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-emerald-700 text-sm font-bold mb-2">المراحل النشطة</h3>
              <p className="text-3xl font-bold text-emerald-600">{zones.filter(z => z.status === 'active').length}</p>
              <p className="text-emerald-500 text-xs mt-1">قيد الزراعة</p>
            </div>
            <div className="w-12 h-12 bg-gradient-to-br from-emerald-500 to-green-600 rounded-2xl flex items-center justify-center shadow-lg">
              <Leaf size={24} className="text-white" />
            </div>
          </div>
        </div>

        <div className="bg-gradient-to-br from-yellow-50 to-amber-100 rounded-3xl p-6 shadow-xl hover:shadow-2xl transition-all duration-300 border-2 border-yellow-200 hover:border-yellow-300 transform hover:scale-105">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-yellow-700 text-sm font-bold mb-2">جاهزة للحصاد</h3>
              <p className="text-3xl font-bold text-yellow-600">{zones.filter(z => z.status === 'harvesting').length}</p>
              <p className="text-yellow-500 text-xs mt-1">مستعد للجني</p>
            </div>
            <div className="w-12 h-12 bg-gradient-to-br from-yellow-500 to-amber-600 rounded-2xl flex items-center justify-center shadow-lg">
              <Calendar size={24} className="text-white" />
            </div>
          </div>
        </div>

        <div className="bg-gradient-to-br from-blue-50 to-cyan-100 rounded-3xl p-6 shadow-xl hover:shadow-2xl transition-all duration-300 border-2 border-blue-200 hover:border-blue-300 transform hover:scale-105">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-blue-700 text-sm font-bold mb-2">تحتاج ري</h3>
              <p className="text-3xl font-bold text-blue-600">{zones.filter(z => z.irrigationStatus === 'pending').length}</p>
              <p className="text-blue-500 text-xs mt-1">في انتظار الري</p>
            </div>
            <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-cyan-600 rounded-2xl flex items-center justify-center shadow-lg">
              <Droplets size={24} className="text-white" />
            </div>
          </div>
        </div>

        <div className="bg-gradient-to-br from-gray-50 to-slate-100 rounded-3xl p-6 shadow-xl hover:shadow-2xl transition-all duration-300 border-2 border-gray-200 hover:border-gray-300 transform hover:scale-105">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-gray-700 text-sm font-bold mb-2">غير نشطة</h3>
              <p className="text-3xl font-bold text-gray-600">{zones.filter(z => z.status === 'inactive').length}</p>
              <p className="text-gray-500 text-xs mt-1">في انتظار التفعيل</p>
            </div>
            <div className="w-12 h-12 bg-gradient-to-br from-gray-500 to-slate-600 rounded-2xl flex items-center justify-center shadow-lg">
              <Leaf size={24} className="text-white" />
            </div>
          </div>
        </div>
      </div>

      {/* Filters and Actions */}
      <div className="bg-gradient-to-br from-white to-gray-50 rounded-3xl shadow-xl p-6 mb-8 border-2 border-emerald-100">
        <div className="flex flex-col lg:flex-row gap-6 items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 bg-gradient-to-br from-emerald-500 to-green-600 rounded-xl flex items-center justify-center shadow-lg">
              <Leaf size={20} className="text-white" />
            </div>
            <span className="font-arabic font-bold text-emerald-700 text-lg">قائمة المراحل:</span>
          </div>
          
          <div className="flex flex-wrap gap-4 items-center">
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className="px-4 py-3 border-2 border-emerald-200 rounded-xl focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 bg-white shadow-lg hover:shadow-xl transition-all duration-200 font-arabic font-medium"
            >
              <option value="all">🌱 جميع المراحل</option>
              <option value="active">🌱 نشطة</option>
              <option value="harvesting">✂️ جاهزة للحصاد</option>
              <option value="inactive">⚪ غير نشطة</option>
              <option value="preparing">🔵 قيد التحضير</option>
            </select>

            <select
              value={lettuceTypeFilter}
              onChange={(e) => setLettuceTypeFilter(e.target.value)}
              className="px-4 py-3 border-2 border-blue-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white shadow-lg hover:shadow-xl transition-all duration-200 font-arabic font-medium"
            >
              <option value="all">🥬 جميع الأنواع</option>
              <option value="iceberg">🥬 خس أيسبيرغ</option>
              <option value="romaine">🥬 خس رومين</option>
            </select>

            <button
              onClick={resetFilters}
              className="px-6 py-3 bg-gradient-to-r from-emerald-500 to-green-600 text-white rounded-xl hover:from-emerald-600 hover:to-green-700 transition-all duration-300 flex items-center gap-3 shadow-lg hover:shadow-xl transform hover:scale-105 font-arabic font-bold"
            >
              <span>🔄 إعادة تعيين</span>
            </button>

            <div className="h-8 w-px bg-gray-300"></div>

            <button
              onClick={handleAddZone}
              className="px-6 py-3 bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-xl hover:from-blue-600 hover:to-blue-700 transition-all duration-300 flex items-center gap-3 shadow-lg hover:shadow-xl transform hover:scale-105 font-arabic font-bold"
            >
              <Plus size={20} />
              <span>إضافة مرحلة جديدة</span>
            </button>
          </div>
        </div>
      </div>

      {/* جدول المراحل */}
      <div className="bg-white rounded-2xl shadow-xl overflow-hidden border-2 border-emerald-100">
        <div className="p-6 bg-gradient-to-r from-emerald-50 to-green-50 border-b border-emerald-200">
          <div className="flex items-center justify-between">
            <h3 className="text-xl font-bold text-emerald-700 font-arabic">قائمة المراحل</h3>
            <div className="flex items-center gap-4">
              <span className="text-sm text-emerald-600 font-arabic">
                عرض {filteredZones.length} من {zones.length} مرحلة
              </span>
              {(statusFilter !== 'all' || lettuceTypeFilter !== 'all') && (
                <span className="px-3 py-1 bg-emerald-100 text-emerald-700 rounded-full text-xs font-bold font-arabic">
                  🔍 مفلتر
                </span>
              )}
            </div>
          </div>
        </div>
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-emerald-100">
            <thead className="bg-gradient-to-r from-emerald-100 to-green-100">
              <tr>
                <th className="px-6 py-4 text-right text-sm font-bold text-emerald-700 font-arabic">المرحلة</th>
                <th className="px-6 py-4 text-right text-sm font-bold text-emerald-700 font-arabic">الحالة</th>
                <th className="px-6 py-4 text-right text-sm font-bold text-emerald-700 font-arabic">نوع الخس</th>
                <th className="px-6 py-4 text-right text-sm font-bold text-emerald-700 font-arabic">تاريخ الزراعة</th>
                <th className="px-6 py-4 text-right text-sm font-bold text-emerald-700 font-arabic">تاريخ الحصاد</th>
                <th className="px-6 py-4 text-right text-sm font-bold text-emerald-700 font-arabic">حالة الري</th>
                <th className="px-6 py-4 text-right text-sm font-bold text-emerald-700 font-arabic">المحابس</th>
                <th className="px-6 py-4 text-right text-sm font-bold text-emerald-700 font-arabic">الإجراءات</th>
              </tr>
            </thead>
            <tbody className="divide-y divide-emerald-50">
              {filteredZones.length === 0 ? (
                <tr>
                  <td colSpan={8} className="px-6 py-12 text-center">
                    <div className="flex flex-col items-center gap-4">
                      <div className="text-6xl">🔍</div>
                      <div className="text-gray-500 font-arabic">
                        <p className="text-lg font-bold mb-2">لا توجد مراحل تطابق الفلاتر المحددة</p>
                        <p className="text-sm">جرب تغيير الفلاتر أو إعادة تعيينها</p>
                      </div>
                      <button
                        onClick={resetFilters}
                        className="px-4 py-2 bg-emerald-500 text-white rounded-lg hover:bg-emerald-600 transition-colors font-arabic"
                      >
                        🔄 إعادة تعيين الفلاتر
                      </button>
                    </div>
                  </td>
                </tr>
              ) : (
                filteredZones.map((zone) => (
                  <tr key={zone.id} className="hover:bg-gradient-to-r hover:from-emerald-50 hover:to-green-50 transition-all duration-200">
                  <td className="px-6 py-4 font-bold text-emerald-800 font-arabic">{zone.name}</td>
                  <td className="px-6 py-4">
                    <span className={`px-3 py-2 rounded-full text-xs font-bold text-white shadow-lg ${
                      zone.status === 'active' ? 'bg-gradient-to-r from-emerald-500 to-green-600' :
                      zone.status === 'harvesting' ? 'bg-gradient-to-r from-yellow-500 to-amber-600' :
                      zone.status === 'preparing' ? 'bg-gradient-to-r from-blue-500 to-cyan-600' :
                      'bg-gradient-to-r from-gray-500 to-slate-600'
                    }`}>
                      {getStatusText(zone.status)}
                    </span>
                  </td>
                  <td className="px-6 py-4">
                    <span className={`px-3 py-2 rounded-full text-xs font-bold shadow-lg ${
                      zone.lettuceType === 'iceberg' 
                        ? 'bg-gradient-to-r from-blue-100 to-cyan-100 text-blue-800 border border-blue-200' 
                        : 'bg-gradient-to-r from-green-100 to-emerald-100 text-green-800 border border-green-200'
                    }`}>
                      {zone.lettuceType === 'iceberg' ? '🥬 أيسبيرغ' : '🥬 رومين'}
                    </span>
                  </td>
                  <td className="px-6 py-4 text-emerald-700 font-arabic">
                    {zone.plantingDate ? formatDate(zone.plantingDate) : '-'}
                  </td>
                  <td className="px-6 py-4 text-emerald-700 font-arabic">
                    {zone.harvestDate ? formatDate(zone.harvestDate) : '-'}
                  </td>
                  <td className="px-6 py-4">
                    <span className={`px-3 py-2 rounded-full text-xs font-bold shadow-lg ${
                      zone.irrigationStatus === 'completed' 
                        ? 'bg-gradient-to-r from-green-100 to-emerald-100 text-green-800 border border-green-200'
                        : zone.irrigationStatus === 'scheduled'
                        ? 'bg-gradient-to-r from-blue-100 to-cyan-100 text-blue-800 border border-blue-200'
                        : 'bg-gradient-to-r from-gray-100 to-slate-100 text-gray-800 border border-gray-200'
                    }`}>
                      {zone.irrigationStatus === 'completed' ? '✅ مكتمل' :
                       zone.irrigationStatus === 'scheduled' ? '📅 مخطط' : '⏳ معلق'}
                    </span>
                  </td>
                  <td className="px-6 py-4">
                    <div className="flex items-center gap-2">
                      <div className="flex items-center gap-1">
                        <span className="text-cyan-600 font-bold text-lg">{zone.valves?.length || 0}</span>
                        <span className="text-cyan-500 text-sm">🚰</span>
                      </div>
                      <div className="flex gap-1">
                        {zone.valves?.slice(0, 3).map((valve, index) => (
                          <div
                            key={valve.id}
                            className={`w-2 h-2 rounded-full ${valve.status === 'active' ? 'bg-green-500' : 'bg-gray-400'}`}
                            title={`محبس ${valve.valveNumber} - ${valve.status === 'active' ? 'نشط' : 'غير نشط'}`}
                          />
                        ))}
                        {(zone.valves?.length || 0) > 3 && (
                          <span className="text-xs text-gray-500">+{(zone.valves?.length || 0) - 3}</span>
                        )}
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4">
                    <div className="flex items-center gap-2">
                      <button
                        onClick={() => handleEditZone(zone)}
                        className="text-emerald-600 hover:text-emerald-800 p-2 hover:bg-emerald-50 rounded-xl transition-all duration-200 shadow-lg hover:shadow-xl transform hover:scale-105"
                        title="تعديل المرحلة"
                      >
                        <Edit size={18} />
                      </button>
                      <button
                        onClick={() => handleDeleteZone(zone)}
                        className="text-red-600 hover:text-red-800 p-2 hover:bg-red-50 rounded-xl transition-all duration-200 shadow-lg hover:shadow-xl transform hover:scale-105"
                        title="حذف المرحلة"
                      >
                        <Trash2 size={18} />
                      </button>
                    </div>
                  </td>
                </tr>
                ))
              )}
            </tbody>
          </table>
        </div>
      </div>

      {/* نافذة تعديل المرحلة */}
      <Modal
        isOpen={isEditModalOpen}
        onClose={() => {
          setIsEditModalOpen(false);
          setSelectedZone(null);
        }}
        title="تعديل المرحلة"
      >
        {selectedZone && (
          <div className="space-y-6">
            <div className="bg-gradient-to-r from-emerald-50 to-green-50 p-4 rounded-xl border border-emerald-200">
              <h3 className="text-lg font-bold text-emerald-700 font-arabic mb-2">معلومات المرحلة</h3>
              <p className="text-emerald-600 text-sm">تعديل بيانات المرحلة الزراعية</p>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-bold text-emerald-700 mb-2 font-arabic">اسم المرحلة</label>
                <input
                  type="text"
                  value={selectedZone.name}
                  onChange={(e) => setSelectedZone({...selectedZone, name: e.target.value})}
                  className="w-full px-4 py-3 border-2 border-emerald-200 rounded-xl focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 bg-white shadow-lg hover:shadow-xl transition-all duration-200 font-arabic"
                />
              </div>
              <div>
                <label className="block text-sm font-bold text-emerald-700 mb-2 font-arabic">الحالة</label>
                <select
                  value={selectedZone.status}
                  onChange={(e) => setSelectedZone({...selectedZone, status: e.target.value as any})}
                  className="w-full px-4 py-3 border-2 border-emerald-200 rounded-xl focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 bg-white shadow-lg hover:shadow-xl transition-all duration-200 font-arabic"
                >
                  <option value="inactive">⚪ غير نشطة</option>
                  <option value="preparing">🔵 قيد التحضير</option>
                  <option value="active">🟢 نشطة</option>
                  <option value="harvesting">🟡 جاهزة للحصاد</option>
                </select>
              </div>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-bold text-emerald-700 mb-2 font-arabic">نوع الخس</label>
                <select
                  value={selectedZone.lettuceType}
                  onChange={(e) => setSelectedZone({...selectedZone, lettuceType: e.target.value as any})}
                  className="w-full px-4 py-3 border-2 border-emerald-200 rounded-xl focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 bg-white shadow-lg hover:shadow-xl transition-all duration-200 font-arabic"
                >
                  <option value="iceberg">🥬 أيسبيرغ</option>
                  <option value="romaine">🥬 رومين</option>
                </select>
              </div>
              <div>
                <label className="block text-sm font-bold text-emerald-700 mb-2 font-arabic">حالة الري</label>
                <select
                  value={selectedZone.irrigationStatus}
                  onChange={(e) => setSelectedZone({...selectedZone, irrigationStatus: e.target.value as any})}
                  className="w-full px-4 py-3 border-2 border-emerald-200 rounded-xl focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 bg-white shadow-lg hover:shadow-xl transition-all duration-200 font-arabic"
                >
                  <option value="pending">⏳ معلق</option>
                  <option value="scheduled">📅 مخطط</option>
                  <option value="completed">✅ مكتمل</option>
                </select>
              </div>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-bold text-emerald-700 mb-2 font-arabic">تاريخ الزراعة</label>
                <input
                  type="date"
                  value={selectedZone.plantingDate}
                  onChange={(e) => setSelectedZone({...selectedZone, plantingDate: e.target.value})}
                  className="w-full px-4 py-3 border-2 border-emerald-200 rounded-xl focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 bg-white shadow-lg hover:shadow-xl transition-all duration-200 font-arabic"
                />
              </div>
              <div>
                <label className="block text-sm font-bold text-emerald-700 mb-2 font-arabic">تاريخ الحصاد</label>
                <input
                  type="date"
                  value={selectedZone.harvestDate || ''}
                  onChange={(e) => setSelectedZone({...selectedZone, harvestDate: e.target.value})}
                  className="w-full px-4 py-3 border-2 border-emerald-200 rounded-xl focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 bg-white shadow-lg hover:shadow-xl transition-all duration-200 font-arabic"
                />
              </div>
            </div>
            
            <div>
              <label className="block text-sm font-bold text-emerald-700 mb-2 font-arabic">ملاحظات</label>
              <textarea
                value={selectedZone.notes}
                onChange={(e) => setSelectedZone({...selectedZone, notes: e.target.value})}
                className="w-full px-4 py-3 border-2 border-emerald-200 rounded-xl focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 bg-white shadow-lg hover:shadow-xl transition-all duration-200 font-arabic"
                rows={3}
                placeholder="أدخل ملاحظات حول المرحلة..."
              />
            </div>

            {/* قسم إدارة المحابس */}
            <div className="bg-gradient-to-r from-cyan-50 to-blue-50 p-4 rounded-xl border border-cyan-200">
              <h4 className="text-lg font-bold text-cyan-700 font-arabic mb-3">⚙️ إدارة المحابس</h4>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                <div>
                  <label className="block text-sm font-bold text-cyan-700 mb-2 font-arabic">عدد المحابس الحالي</label>
                  <div className="bg-white p-3 rounded-xl border-2 border-cyan-200">
                    <span className="text-lg font-bold text-cyan-600">{selectedZone.valves?.length || 0} محبس</span>
                  </div>
                </div>
                <div>
                  <label className="block text-sm font-bold text-cyan-700 mb-2 font-arabic">تعديل العدد</label>
                  <select
                    value={selectedZone.valves?.length || 2}
                    onChange={(e) => {
                      const newCount = parseInt(e.target.value);
                      const currentValves = selectedZone.valves || [];
                      let newValves = [...currentValves];
                      
                      if (newCount > currentValves.length) {
                        // إضافة محابس جديدة
                        for (let i = currentValves.length + 1; i <= newCount; i++) {
                          newValves.push({
                            id: Date.now() + i,
                            zoneId: selectedZone.id,
                            valveNumber: i,
                            lettuceType: selectedZone.lettuceType,
                            status: 'inactive' as const,
                            lastIrrigation: '',
                            nextIrrigation: ''
                          });
                        }
                      } else if (newCount < currentValves.length) {
                        // حذف المحابس الزائدة
                        newValves = currentValves.slice(0, newCount);
                      }
                      
                      setSelectedZone({...selectedZone, valves: newValves});
                    }}
                    className="w-full px-4 py-3 border-2 border-cyan-200 rounded-xl focus:ring-2 focus:ring-cyan-500 focus:border-cyan-500 bg-white shadow-lg hover:shadow-xl transition-all duration-200 font-arabic"
                  >
                    <option value={1}>🚰 محبس واحد</option>
                    <option value={2}>🚰🚰 محبسان</option>
                    <option value={3}>🚰🚰🚰 ثلاثة محابس</option>
                    <option value={4}>🚰🚰🚰🚰 أربعة محابس</option>
                    <option value={5}>🚰🚰🚰🚰🚰 خمسة محابس</option>
                    <option value={6}>🚰🚰🚰🚰🚰🚰 ستة محابس</option>
                  </select>
                </div>
              </div>

              {/* عرض المحابس الحالية */}
              {selectedZone.valves && selectedZone.valves.length > 0 && (
                <div>
                  <h5 className="text-md font-bold text-cyan-700 font-arabic mb-2">المحابس الحالية:</h5>
                  <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-2">
                    {selectedZone.valves.map((valve, index) => (
                      <div key={valve.id} className="bg-white p-2 rounded-lg border border-cyan-200 text-center">
                        <div className={`w-6 h-6 rounded-full mx-auto mb-1 ${valve.status === 'active' ? 'bg-green-500' : 'bg-gray-400'}`}></div>
                        <span className="text-xs text-cyan-600 font-arabic">محبس {valve.valveNumber}</span>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
            
            <div className="flex gap-4 pt-6">
              <button
                onClick={() => handleSaveZone(selectedZone)}
                className="flex-1 bg-gradient-to-r from-emerald-500 to-green-600 text-white py-4 px-6 rounded-xl font-bold hover:from-emerald-600 hover:to-green-700 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:scale-105 font-arabic"
              >
                💾 حفظ التغييرات
              </button>
              <button
                onClick={() => {
                  setIsEditModalOpen(false);
                  setSelectedZone(null);
                }}
                className="flex-1 bg-gradient-to-r from-gray-200 to-gray-300 text-gray-700 py-4 px-6 rounded-xl font-bold hover:from-gray-300 hover:to-gray-400 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:scale-105 font-arabic"
              >
                ❌ إلغاء
              </button>
            </div>
          </div>
        )}
      </Modal>

      {/* نافذة إضافة مرحلة جديدة */}
      <Modal
        isOpen={isAddModalOpen}
        onClose={() => setIsAddModalOpen(false)}
        title="إضافة مرحلة زراعية جديدة"
      >
        <ZoneForm
          onSave={handleSaveZone}
          onCancel={() => setIsAddModalOpen(false)}
          zones={zones}
        />
      </Modal>

      {/* نافذة تأكيد الحذف */}
      <Modal
        isOpen={isDeleteModalOpen}
        onClose={() => {
          setIsDeleteModalOpen(false);
          setZoneToDelete(null);
        }}
        title="تأكيد حذف المرحلة"
      >
        {zoneToDelete && (
          <div className="space-y-6">
            <div className="bg-gradient-to-r from-red-50 to-pink-50 p-6 rounded-xl border-2 border-red-200">
              <div className="flex items-center gap-4">
                <div className="w-12 h-12 bg-gradient-to-br from-red-500 to-pink-600 rounded-2xl flex items-center justify-center shadow-lg">
                  <AlertTriangle size={24} className="text-white" />
                </div>
                <div>
                  <h3 className="text-xl font-bold text-red-700 font-arabic mb-1">تحذير!</h3>
                  <p className="text-red-600 font-arabic">هذا الإجراء لا يمكن التراجع عنه</p>
                </div>
              </div>
            </div>

            <div className="bg-white p-6 rounded-xl border border-gray-200 shadow-lg">
              <h4 className="text-lg font-bold text-gray-800 font-arabic mb-4">سيتم حذف المرحلة التالية:</h4>
              <div className="bg-gray-50 p-4 rounded-xl space-y-2">
                <div className="flex justify-between">
                  <span className="font-bold text-gray-700 font-arabic">اسم المرحلة:</span>
                  <span className="text-gray-600 font-arabic">{zoneToDelete.name}</span>
                </div>
                <div className="flex justify-between">
                  <span className="font-bold text-gray-700 font-arabic">نوع الخس:</span>
                  <span className="text-gray-600 font-arabic">
                    {zoneToDelete.lettuceType === 'iceberg' ? '🥬 أيسبيرغ' : '🥬 رومين'}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="font-bold text-gray-700 font-arabic">الحالة:</span>
                  <span className="text-gray-600 font-arabic">{getStatusText(zoneToDelete.status)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="font-bold text-gray-700 font-arabic">عدد المحابس:</span>
                  <span className="text-gray-600">{zoneToDelete.valves?.length || 0}</span>
                </div>
              </div>
            </div>

            <div className="bg-yellow-50 p-4 rounded-xl border border-yellow-200">
              <p className="text-yellow-800 font-arabic text-sm">
                <strong>ملاحظة:</strong> سيتم أيضاً حذف جميع البيانات المرتبطة بهذه المرحلة مثل عمليات الرش والتسميد ومحابس الري.
              </p>
            </div>

            <div className="flex gap-4 pt-4">
              <button
                onClick={confirmDeleteZone}
                className="flex-1 bg-gradient-to-r from-red-500 to-pink-600 text-white py-4 px-6 rounded-xl font-bold hover:from-red-600 hover:to-pink-700 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:scale-105 font-arabic"
              >
                🗑️ نعم، احذف المرحلة
              </button>
              <button
                onClick={() => {
                  setIsDeleteModalOpen(false);
                  setZoneToDelete(null);
                }}
                className="flex-1 bg-gradient-to-r from-gray-500 to-gray-600 text-white py-4 px-6 rounded-xl font-bold hover:from-gray-600 hover:to-gray-700 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:scale-105 font-arabic"
              >
                ❌ إلغاء
              </button>
            </div>
          </div>
        )}
      </Modal>
    </div>
  );
};

// مكون نموذج إضافة مرحلة جديدة
const ZoneForm: React.FC<{
  onSave: (zone: Omit<Zone, 'id'>) => void;
  onCancel: () => void;
  zones?: Zone[];
}> = ({ onSave, onCancel, zones = [] }) => {
  const [formData, setFormData] = useState<Omit<Zone, 'id'>>({
    name: '',
    status: 'inactive',
    plantingDate: '',
    harvestDate: '',
    lettuceType: 'iceberg',
    irrigationStatus: 'pending',
    lastIrrigation: '',
    nextIrrigation: '',
    notes: '',
    valves: []
  });

  const [valveCount, setValveCount] = useState(2); // عدد المحابس الافتراضي
  const [showNameModal, setShowNameModal] = useState(false); // نافذة اختيار الاسم

  // تحديث المحابس تلقائياً عند تحميل النموذج
  useEffect(() => {
    if (formData.valves.length === 0) {
      setFormData(prev => ({
        ...prev,
        valves: generateValves(valveCount)
      }));
    }
  }, []);

  // قائمة أسماء المراحل من 1 إلى 24
  const getStageNames = () => {
    const existingNames = zones?.map(z => z.name) || [];
    const stageNames = [];
    
    const arabicNumbers = [
      'الأولى', 'الثانية', 'الثالثة', 'الرابعة', 'الخامسة', 'السادسة',
      'السابعة', 'الثامنة', 'التاسعة', 'العاشرة', 'الحادية عشرة', 'الثانية عشرة',
      'الثالثة عشرة', 'الرابعة عشرة', 'الخامسة عشرة', 'السادسة عشرة',
      'السابعة عشرة', 'الثامنة عشرة', 'التاسعة عشرة', 'العشرون',
      'الحادية والعشرون', 'الثانية والعشرون', 'الثالثة والعشرون', 'الرابعة والعشرون'
    ];
    
    for (let i = 0; i < 24; i++) {
      const stageName = `المرحلة ${arabicNumbers[i]}`;
      stageNames.push({
        name: stageName,
        isUsed: existingNames.includes(stageName)
      });
    }
    
    return stageNames;
  };

  // دالة اختيار الاسم
  const handleNameSelect = (selectedName: string) => {
    setFormData({...formData, name: selectedName});
    setShowNameModal(false);
  };

  // دالة لإنشاء المحابس تلقائياً
  const generateValves = (count: number) => {
    const valves = [];
    for (let i = 1; i <= count; i++) {
      valves.push({
        id: Date.now() + i,
        zoneId: 0, // سيتم تحديثه لاحقاً
        valveNumber: i,
        lettuceType: formData.lettuceType,
        status: 'inactive' as const,
        lastIrrigation: '',
        nextIrrigation: ''
      });
    }
    return valves;
  };

  // تحديث المحابس عند تغيير العدد أو نوع الخس
  const handleValveCountChange = (count: number) => {
    setValveCount(count);
    setFormData({
      ...formData,
      valves: generateValves(count)
    });
  };

  // تحديث نوع الخس في المحابس عند تغييره
  const handleLettuceTypeChange = (lettuceType: 'iceberg' | 'romaine') => {
    setFormData({
      ...formData,
      lettuceType,
      valves: formData.valves.map(valve => ({
        ...valve,
        lettuceType
      }))
    });
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!formData.name.trim()) {
      alert('يرجى إدخال اسم المرحلة');
      return;
    }
    
    // إنشاء المحابس إذا لم تكن موجودة
    const finalFormData = {
      ...formData,
      valves: formData.valves.length > 0 ? formData.valves : generateValves(valveCount)
    };
    
    onSave(finalFormData);
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <div className="bg-gradient-to-r from-blue-50 to-indigo-50 p-4 rounded-xl border border-blue-200">
        <h3 className="text-lg font-bold text-blue-700 font-arabic mb-2">معلومات المرحلة الجديدة</h3>
        <p className="text-blue-600 text-sm">إدخال بيانات المرحلة الزراعية</p>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-bold text-blue-700 mb-2 font-arabic">اسم المرحلة *</label>
          <button
            type="button"
            onClick={() => setShowNameModal(true)}
            className="w-full px-4 py-3 border-2 border-blue-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white shadow-lg hover:shadow-xl transition-all duration-200 font-arabic text-right flex items-center justify-between group"
          >
            <span className={formData.name ? 'text-gray-800' : 'text-gray-400'}>
              {formData.name || 'اختر اسم المرحلة...'}
            </span>
            <div className="flex items-center gap-2">
              <span className="text-blue-500 group-hover:text-blue-600 transition-colors">📝</span>
              <span className="text-xs text-blue-500 group-hover:text-blue-600 transition-colors">اختر</span>
            </div>
          </button>
        </div>
        <div>
          <label className="block text-sm font-bold text-blue-700 mb-2 font-arabic">الحالة</label>
          <select
            value={formData.status}
            onChange={(e) => setFormData({...formData, status: e.target.value as any})}
            className="w-full px-4 py-3 border-2 border-blue-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white shadow-lg hover:shadow-xl transition-all duration-200 font-arabic"
          >
            <option value="inactive">⚪ غير نشطة</option>
            <option value="preparing">🔵 قيد التحضير</option>
            <option value="active">🟢 نشطة</option>
            <option value="harvesting">🟡 جاهزة للحصاد</option>
          </select>
        </div>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-bold text-blue-700 mb-2 font-arabic">نوع الخس</label>
          <select
            value={formData.lettuceType}
            onChange={(e) => handleLettuceTypeChange(e.target.value as 'iceberg' | 'romaine')}
            className="w-full px-4 py-3 border-2 border-blue-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white shadow-lg hover:shadow-xl transition-all duration-200 font-arabic"
          >
            <option value="iceberg">🥬 أيسبيرغ</option>
            <option value="romaine">🥬 رومين</option>
          </select>
        </div>
        <div>
          <label className="block text-sm font-bold text-blue-700 mb-2 font-arabic">حالة الري</label>
          <select
            value={formData.irrigationStatus}
            onChange={(e) => setFormData({...formData, irrigationStatus: e.target.value as any})}
            className="w-full px-4 py-3 border-2 border-blue-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white shadow-lg hover:shadow-xl transition-all duration-200 font-arabic"
          >
            <option value="pending">⏳ معلق</option>
            <option value="scheduled">📅 مخطط</option>
            <option value="completed">✅ مكتمل</option>
          </select>
        </div>
      </div>

      {/* قسم المحابس */}
      <div className="bg-gradient-to-r from-cyan-50 to-blue-50 p-4 rounded-xl border border-cyan-200">
        <h4 className="text-lg font-bold text-cyan-700 font-arabic mb-3">⚙️ إعدادات المحابس</h4>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-bold text-cyan-700 mb-2 font-arabic">عدد المحابس</label>
            <select
              value={valveCount}
              onChange={(e) => handleValveCountChange(parseInt(e.target.value))}
              className="w-full px-4 py-3 border-2 border-cyan-200 rounded-xl focus:ring-2 focus:ring-cyan-500 focus:border-cyan-500 bg-white shadow-lg hover:shadow-xl transition-all duration-200 font-arabic"
            >
              <option value={1}>🚰 محبس واحد</option>
              <option value={2}>🚰🚰 محبسان</option>
              <option value={3}>🚰🚰🚰 ثلاثة محابس</option>
              <option value={4}>🚰🚰🚰🚰 أربعة محابس</option>
              <option value={5}>🚰🚰🚰🚰🚰 خمسة محابس</option>
              <option value={6}>🚰🚰🚰🚰🚰🚰 ستة محابس</option>
            </select>
          </div>
          <div className="flex items-end">
            <div className="bg-white p-3 rounded-xl border-2 border-cyan-200 w-full">
              <p className="text-sm text-cyan-600 font-arabic mb-2">
                <span className="font-bold">المحابس المُنشأة:</span> {valveCount} محبس
              </p>
              <div className="flex gap-1 mb-2">
                {Array.from({length: valveCount}, (_, i) => (
                  <div
                    key={i}
                    className="w-4 h-4 rounded-full bg-gray-400 border-2 border-white shadow-sm"
                    title={`محبس ${i + 1} - غير نشط`}
                  />
                ))}
              </div>
              <p className="text-xs text-cyan-500">
                سيتم إنشاء {valveCount} محبس تلقائياً لهذه المرحلة
              </p>
            </div>
          </div>
        </div>
        
        {/* خيارات متقدمة */}
        <div className="mt-4 p-3 bg-cyan-25 rounded-lg border border-cyan-100">
          <details className="group">
            <summary className="cursor-pointer text-sm font-bold text-cyan-700 font-arabic hover:text-cyan-800 transition-colors">
              ⚙️ خيارات متقدمة
            </summary>
            <div className="mt-3 space-y-2">
              <button
                type="button"
                onClick={() => {
                  const newValves = generateValves(valveCount);
                  // تعيين نصف المحابس كنشطة للمعاينة
                  const activeCount = Math.ceil(valveCount / 2);
                  for (let i = 0; i < activeCount; i++) {
                    newValves[i].status = 'active';
                  }
                  setFormData({...formData, valves: newValves});
                }}
                className="text-xs bg-cyan-100 hover:bg-cyan-200 text-cyan-700 px-3 py-1 rounded-lg transition-colors font-arabic"
              >
                🔄 إعادة إنشاء المحابس
              </button>
              <p className="text-xs text-cyan-600 font-arabic">
                💡 نصيحة: يمكنك تعديل حالة المحابس لاحقاً من خريطة المزرعة
              </p>
            </div>
          </details>
        </div>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-bold text-blue-700 mb-2 font-arabic">تاريخ الزراعة</label>
          <input
            type="date"
            value={formData.plantingDate}
            onChange={(e) => setFormData({...formData, plantingDate: e.target.value})}
            className="w-full px-4 py-3 border-2 border-blue-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white shadow-lg hover:shadow-xl transition-all duration-200 font-arabic"
          />
        </div>
        <div>
          <label className="block text-sm font-bold text-blue-700 mb-2 font-arabic">تاريخ الحصاد المتوقع</label>
          <input
            type="date"
            value={formData.harvestDate}
            onChange={(e) => setFormData({...formData, harvestDate: e.target.value})}
            className="w-full px-4 py-3 border-2 border-blue-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white shadow-lg hover:shadow-xl transition-all duration-200 font-arabic"
          />
        </div>
      </div>
      
      <div>
        <label className="block text-sm font-bold text-blue-700 mb-2 font-arabic">ملاحظات</label>
        <textarea
          value={formData.notes}
          onChange={(e) => setFormData({...formData, notes: e.target.value})}
          className="w-full px-4 py-3 border-2 border-blue-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white shadow-lg hover:shadow-xl transition-all duration-200 font-arabic"
          rows={3}
          placeholder="أدخل ملاحظات حول المرحلة الجديدة..."
        />
      </div>
      
      <div className="flex gap-4 pt-6">
        <button
          type="submit"
          className="flex-1 bg-gradient-to-r from-blue-500 to-indigo-600 text-white py-4 px-6 rounded-xl font-bold hover:from-blue-600 hover:to-indigo-700 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:scale-105 font-arabic"
        >
          ✅ إضافة المرحلة
        </button>
        <button
          type="button"
          onClick={onCancel}
          className="flex-1 bg-gradient-to-r from-gray-200 to-gray-300 text-gray-700 py-4 px-6 rounded-xl font-bold hover:from-gray-300 hover:to-gray-400 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:scale-105 font-arabic"
        >
          ❌ إلغاء
        </button>
      </div>

      {/* نافذة اختيار اسم المرحلة */}
      {showNameModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-2xl shadow-2xl max-w-4xl w-full max-h-[80vh] overflow-hidden">
            {/* رأس النافذة */}
            <div className="bg-gradient-to-r from-blue-500 to-indigo-600 text-white p-6">
              <div className="flex items-center justify-between">
                <h3 className="text-xl font-bold font-arabic">📝 اختيار اسم المرحلة</h3>
                <button
                  onClick={() => setShowNameModal(false)}
                  className="text-white hover:text-gray-200 transition-colors p-1 rounded-full hover:bg-white/20"
                >
                  <span className="text-2xl">×</span>
                </button>
              </div>
              <p className="text-blue-100 text-sm mt-2 font-arabic">اختر من المراحل المتاحة (1-24)</p>
            </div>

            {/* محتوى النافذة */}
            <div className="p-6 max-h-[60vh] overflow-y-auto">
              <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3">
                {getStageNames().map((stage, index) => (
                  <button
                    key={index}
                    onClick={() => !stage.isUsed && handleNameSelect(stage.name)}
                    disabled={stage.isUsed}
                    className={`p-4 text-center border-2 rounded-xl transition-all duration-200 font-arabic ${
                      stage.isUsed
                        ? 'border-gray-200 bg-gray-100 text-gray-400 cursor-not-allowed'
                        : 'border-gray-200 hover:border-blue-300 hover:bg-blue-50 shadow-sm hover:shadow-md group'
                    }`}
                  >
                    <div className="flex flex-col items-center gap-2">
                      <span className={`text-2xl ${stage.isUsed ? '🔒' : '📁'}`}>
                        {stage.isUsed ? '🔒' : '📁'}
                      </span>
                      <span className={`font-medium text-sm ${
                        stage.isUsed ? 'text-gray-400' : 'text-gray-700 group-hover:text-blue-700'
                      }`}>
                        {stage.name}
                      </span>
                      {stage.isUsed && (
                        <span className="text-xs text-red-500 font-arabic">مستخدمة</span>
                      )}
                    </div>
                  </button>
                ))}
              </div>

              {getStageNames().filter(s => !s.isUsed).length === 0 && (
                <div className="text-center py-8 text-gray-500">
                  <div className="text-4xl mb-2">🔒</div>
                  <p className="font-arabic">جميع المراحل مستخدمة (1-24)</p>
                </div>
              )}
            </div>

            {/* أسفل النافذة */}
            <div className="bg-gray-50 px-6 py-4 flex justify-between items-center">
              <div className="text-sm text-gray-600 font-arabic">
                المتاح: {getStageNames().filter(s => !s.isUsed).length} من 24 مرحلة
              </div>
              <button
                onClick={() => setShowNameModal(false)}
                className="px-6 py-2 bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300 transition-colors font-arabic"
              >
                إلغاء
              </button>
            </div>
          </div>
        </div>
      )}
    </form>
  );
};

export default ZonesView; 