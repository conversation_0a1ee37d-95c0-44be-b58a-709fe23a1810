import { useState, useEffect, useCallback } from 'react';
import { enhancedDB } from '../utils/enhancedDatabase';
import { useToast } from './useToast';
import {
  Zone,
  Employee,
  Supplier,
  Advance,
  LocalSale,
  InventoryItem,
  Spraying,
  Fertilization
} from '../types';

export const useZones = () => {
  const [zones, setZones] = useState<Zone[]>([]);
  const [loading, setLoading] = useState(true);
  const { showError, showSuccess } = useToast();

  const loadZones = useCallback(async () => {
    try {
      console.log('🔄 تحميل المراحل من قاعدة البيانات...');
      setLoading(true);
      const data = await enhancedDB.getZones();
      console.log('📊 تم تحميل', data.length, 'مرحلة');
      setZones(data);
    } catch (error) {
      showError('خطأ في تحميل المناطق', 'تعذر تحميل بيانات المناطق الزراعية');
      console.error('Error loading zones:', error);
    } finally {
      setLoading(false);
    }
  }, [showError]);

  const addZone = useCallback(async (zone: Omit<Zone, 'id'>) => {
    try {
      const id = await enhancedDB.addZone(zone);
      const newZone = { ...zone, id } as Zone;
      setZones(prev => [...prev, newZone]);
      showSuccess('تم إضافة المنطقة', 'تم إضافة المنطقة الزراعية بنجاح');
      return id;
    } catch (error) {
      showError('خطأ في إضافة المنطقة', 'تعذر إضافة المنطقة الزراعية');
      throw error;
    }
  }, [showSuccess, showError]);

  const updateZone = useCallback(async (zoneIdOrZone: number | string | Zone, updatedZone?: Zone) => {
    try {
      let zoneToUpdate: Zone;
      
      // إذا تم تمرير معرف المرحلة والمرحلة المحدثة (من FarmMapView)
      if (updatedZone && typeof zoneIdOrZone !== 'object') {
        zoneToUpdate = updatedZone;
        console.log('🔄 تحديث المرحلة من خريطة المزرعة:', zoneToUpdate.id, zoneToUpdate.status);
      }
      // إذا تم تمرير المرحلة مباشرة (من ZonesView)
      else if (typeof zoneIdOrZone === 'object') {
        zoneToUpdate = zoneIdOrZone;
        console.log('🔄 تحديث المرحلة من إدارة المراحل:', zoneToUpdate.id, zoneToUpdate.status);
      }
      else {
        throw new Error('بيانات غير صحيحة لتحديث المرحلة');
      }

      await enhancedDB.updateZone(zoneToUpdate);
      setZones(prev => prev.map(z => z.id === zoneToUpdate.id ? zoneToUpdate : z));
      showSuccess('تم تحديث المنطقة', 'تم تحديث بيانات المنطقة بنجاح');
    } catch (error) {
      console.error('❌ خطأ في تحديث المرحلة:', error);
      showError('خطأ في تحديث المنطقة', 'تعذر تحديث بيانات المنطقة');
      throw error;
    }
  }, [showSuccess, showError]);

  const deleteZone = useCallback(async (id: number | string) => {
    try {
      const numericId = typeof id === 'string' ? parseInt(id) : id;
      console.log('🗑️ حذف المرحلة:', numericId);
      await enhancedDB.deleteZone(numericId);
      console.log('✅ تم حذف المرحلة من قاعدة البيانات');
      setZones(prev => {
        const filtered = prev.filter(z => z.id !== numericId);
        console.log('📊 المراحل قبل الحذف:', prev.length, 'بعد الحذف:', filtered.length);
        return filtered;
      });
      showSuccess('تم حذف المنطقة', 'تم حذف المنطقة الزراعية بنجاح');
    } catch (error) {
      console.error('❌ خطأ في حذف المرحلة:', error);
      showError('خطأ في حذف المنطقة', 'تعذر حذف المنطقة الزراعية');
      throw error;
    }
  }, [showSuccess, showError]);

  useEffect(() => {
    loadZones();
  }, [loadZones]);

  return {
    zones,
    loading,
    addZone,
    updateZone,
    deleteZone,
    refreshZones: loadZones
  };
};

export const useEmployees = () => {
  const [employees, setEmployees] = useState<Employee[]>([]);
  const [loading, setLoading] = useState(true);
  const { showError, showSuccess } = useToast();

  const loadEmployees = useCallback(async () => {
    try {
      setLoading(true);
      const data = await enhancedDB.getEmployees();
      setEmployees(data);
    } catch (error) {
      showError('خطأ في تحميل الموظفين', 'تعذر تحميل بيانات الموظفين');
      console.error('Error loading employees:', error);
    } finally {
      setLoading(false);
    }
  }, [showError]);

  const addEmployee = useCallback(async (employee: Omit<Employee, 'id' | 'createdAt'>) => {
    try {
      const id = await enhancedDB.addEmployee(employee);
      const newEmployee = { ...employee, id, createdAt: new Date().toISOString() } as Employee;
      setEmployees(prev => [...prev, newEmployee]);
      showSuccess('تم إضافة الموظف', 'تم إضافة الموظف بنجاح');
      return id;
    } catch (error) {
      showError('خطأ في إضافة الموظف', 'تعذر إضافة الموظف');
      throw error;
    }
  }, [showSuccess, showError]);

  const updateEmployee = useCallback(async (employee: Employee) => {
    try {
      await enhancedDB.updateEmployee(employee);
      setEmployees(prev => prev.map(e => e.id === employee.id ? employee : e));
      showSuccess('تم تحديث الموظف', 'تم تحديث بيانات الموظف بنجاح');
    } catch (error) {
      showError('خطأ في تحديث الموظف', 'تعذر تحديث بيانات الموظف');
      throw error;
    }
  }, [showSuccess, showError]);

  const deleteEmployee = useCallback(async (id: number) => {
    try {
      await enhancedDB.deleteEmployee(id);
      setEmployees(prev => prev.filter(e => e.id !== id));
      showSuccess('تم حذف الموظف', 'تم حذف الموظف بنجاح');
    } catch (error) {
      showError('خطأ في حذف الموظف', 'تعذر حذف الموظف');
      throw error;
    }
  }, [showSuccess, showError]);

  useEffect(() => {
    loadEmployees();
  }, [loadEmployees]);

  return {
    employees,
    loading,
    addEmployee,
    updateEmployee,
    deleteEmployee,
    refreshEmployees: loadEmployees
  };
};

export const useSuppliers = () => {
  const [suppliers, setSuppliers] = useState<Supplier[]>([]);
  const [loading, setLoading] = useState(true);
  const { showError, showSuccess } = useToast();

  const loadSuppliers = useCallback(async () => {
    try {
      setLoading(true);
      const data = await enhancedDB.getSuppliers();
      setSuppliers(data);
    } catch (error) {
      showError('خطأ في تحميل الموردين', 'تعذر تحميل بيانات الموردين');
      console.error('Error loading suppliers:', error);
    } finally {
      setLoading(false);
    }
  }, [showError]);

  const addSupplier = useCallback(async (supplier: Omit<Supplier, 'id' | 'createdAt'>) => {
    try {
      const id = await enhancedDB.addSupplier(supplier);
      const newSupplier = { ...supplier, id, createdAt: new Date().toISOString() } as Supplier;
      setSuppliers(prev => [...prev, newSupplier]);
      showSuccess('تم إضافة المورد', 'تم إضافة المورد بنجاح');
      return id;
    } catch (error) {
      showError('خطأ في إضافة المورد', 'تعذر إضافة المورد');
      throw error;
    }
  }, [showSuccess, showError]);

  const updateSupplier = useCallback(async (supplier: Supplier) => {
    try {
      await enhancedDB.updateSupplier(supplier);
      setSuppliers(prev => prev.map(s => s.id === supplier.id ? supplier : s));
      showSuccess('تم تحديث المورد', 'تم تحديث بيانات المورد بنجاح');
    } catch (error) {
      showError('خطأ في تحديث المورد', 'تعذر تحديث بيانات المورد');
      throw error;
    }
  }, [showSuccess, showError]);

  const deleteSupplier = useCallback(async (id: number) => {
    try {
      await enhancedDB.deleteSupplier(id);
      setSuppliers(prev => prev.filter(s => s.id !== id));
      showSuccess('تم حذف المورد', 'تم حذف المورد بنجاح');
    } catch (error) {
      showError('خطأ في حذف المورد', 'تعذر حذف المورد');
      throw error;
    }
  }, [showSuccess, showError]);

  useEffect(() => {
    loadSuppliers();
  }, [loadSuppliers]);

  return {
    suppliers,
    loading,
    addSupplier,
    updateSupplier,
    deleteSupplier,
    refreshSuppliers: loadSuppliers
  };
};

export const useAdvances = () => {
  const [advances, setAdvances] = useState<Advance[]>([]);
  const [loading, setLoading] = useState(true);
  const { showError, showSuccess } = useToast();

  const loadAdvances = useCallback(async () => {
    try {
      setLoading(true);
      const data = await enhancedDB.getAdvances();
      setAdvances(data);
    } catch (error) {
      showError('خطأ في تحميل السلف', 'تعذر تحميل بيانات السلف');
      console.error('Error loading advances:', error);
    } finally {
      setLoading(false);
    }
  }, [showError]);

  const addAdvance = useCallback(async (advance: Omit<Advance, 'id' | 'createdAt'>) => {
    try {
      const id = await enhancedDB.addAdvance(advance);
      const newAdvance = { ...advance, id, createdAt: new Date().toISOString() } as Advance;
      setAdvances(prev => [...prev, newAdvance]);
      showSuccess('تم إضافة السلفة', 'تم إضافة السلفة بنجاح');
      return id;
    } catch (error) {
      showError('خطأ في إضافة السلفة', 'تعذر إضافة السلفة');
      throw error;
    }
  }, [showSuccess, showError]);

  const updateAdvance = useCallback(async (advance: Advance) => {
    try {
      await enhancedDB.updateAdvance(advance);
      setAdvances(prev => prev.map(a => a.id === advance.id ? advance : a));
      showSuccess('تم تحديث السلفة', 'تم تحديث بيانات السلفة بنجاح');
    } catch (error) {
      showError('خطأ في تحديث السلفة', 'تعذر تحديث بيانات السلفة');
      throw error;
    }
  }, [showSuccess, showError]);

  const deleteAdvance = useCallback(async (id: number) => {
    try {
      await enhancedDB.deleteAdvance(id);
      setAdvances(prev => prev.filter(a => a.id !== id));
      showSuccess('تم حذف السلفة', 'تم حذف السلفة بنجاح');
    } catch (error) {
      showError('خطأ في حذف السلفة', 'تعذر حذف السلفة');
      throw error;
    }
  }, [showSuccess, showError]);

  useEffect(() => {
    loadAdvances();
  }, [loadAdvances]);

  return {
    advances,
    loading,
    addAdvance,
    updateAdvance,
    deleteAdvance,
    refreshAdvances: loadAdvances
  };
};

export const useSales = () => {
  const [sales, setSales] = useState<LocalSale[]>([]);
  const [loading, setLoading] = useState(true);
  const { showError, showSuccess } = useToast();

  const loadSales = useCallback(async () => {
    try {
      setLoading(true);
      const data = await enhancedDB.getSales();
      setSales(data);
    } catch (error) {
      showError('خطأ في تحميل المبيعات', 'تعذر تحميل بيانات المبيعات');
      console.error('Error loading sales:', error);
    } finally {
      setLoading(false);
    }
  }, [showError]);

  const addSale = useCallback(async (sale: Omit<LocalSale, 'id' | 'createdAt'>) => {
    try {
      const id = await enhancedDB.addSale(sale);
      const newSale = { ...sale, id, createdAt: new Date().toISOString() } as LocalSale;
      setSales(prev => [...prev, newSale]);
      showSuccess('تم إضافة المبيعة', 'تم إضافة المبيعة بنجاح');
      return id;
    } catch (error) {
      showError('خطأ في إضافة المبيعة', 'تعذر إضافة المبيعة');
      throw error;
    }
  }, [showSuccess, showError]);

  const updateSale = useCallback(async (sale: LocalSale) => {
    try {
      await enhancedDB.updateSale(sale);
      setSales(prev => prev.map(s => s.id === sale.id ? sale : s));
      showSuccess('تم تحديث المبيعة', 'تم تحديث بيانات المبيعة بنجاح');
    } catch (error) {
      showError('خطأ في تحديث المبيعة', 'تعذر تحديث بيانات المبيعة');
      throw error;
    }
  }, [showSuccess, showError]);

  const deleteSale = useCallback(async (id: number) => {
    try {
      await enhancedDB.deleteSale(id);
      setSales(prev => prev.filter(s => s.id !== id));
      showSuccess('تم حذف المبيعة', 'تم حذف المبيعة بنجاح');
    } catch (error) {
      showError('خطأ في حذف المبيعة', 'تعذر حذف المبيعة');
      throw error;
    }
  }, [showSuccess, showError]);

  useEffect(() => {
    loadSales();
  }, [loadSales]);

  return {
    sales,
    loading,
    addSale,
    updateSale,
    deleteSale,
    refreshSales: loadSales
  };
};

export const useInventory = () => {
  const [inventory, setInventory] = useState<InventoryItem[]>([]);
  const [loading, setLoading] = useState(true);
  const { showError, showSuccess } = useToast();

  const loadInventory = useCallback(async () => {
    try {
      setLoading(true);
      const data = await enhancedDB.getInventoryItems();
      setInventory(data);
    } catch (error) {
      showError('خطأ في تحميل المخزون', 'تعذر تحميل بيانات المخزون');
      console.error('Error loading inventory:', error);
    } finally {
      setLoading(false);
    }
  }, [showError]);

  const addInventoryItem = useCallback(async (item: Omit<InventoryItem, 'id'>) => {
    try {
      const id = await enhancedDB.addInventoryItem(item);
      const newItem = { ...item, id } as InventoryItem;
      setInventory(prev => [...prev, newItem]);
      showSuccess('تم إضافة المادة', 'تم إضافة المادة للمخزون بنجاح');
      return id;
    } catch (error) {
      showError('خطأ في إضافة المادة', 'تعذر إضافة المادة للمخزون');
      throw error;
    }
  }, [showSuccess, showError]);

  const updateInventoryItem = useCallback(async (item: InventoryItem) => {
    try {
      await enhancedDB.updateInventoryItem(item);
      setInventory(prev => prev.map(i => i.id === item.id ? item : i));
      showSuccess('تم تحديث المادة', 'تم تحديث بيانات المادة بنجاح');
    } catch (error) {
      showError('خطأ في تحديث المادة', 'تعذر تحديث بيانات المادة');
      throw error;
    }
  }, [showSuccess, showError]);

  const deleteInventoryItem = useCallback(async (id: number) => {
    try {
      await enhancedDB.deleteInventoryItem(id);
      setInventory(prev => prev.filter(i => i.id !== id));
      showSuccess('تم حذف المادة', 'تم حذف المادة من المخزون بنجاح');
    } catch (error) {
      showError('خطأ في حذف المادة', 'تعذر حذف المادة من المخزون');
      throw error;
    }
  }, [showSuccess, showError]);

  useEffect(() => {
    loadInventory();
  }, [loadInventory]);

  return {
    inventory,
    loading,
    addInventoryItem,
    updateInventoryItem,
    deleteInventoryItem,
    refreshInventory: loadInventory
  };
};

export const useSprayings = () => {
  const [sprayings, setSprayings] = useState<Spraying[]>([]);
  const [loading, setLoading] = useState(true);
  const { showError, showSuccess } = useToast();

  const loadSprayings = useCallback(async () => {
    try {
      setLoading(true);
      const data = await enhancedDB.getSprayings();
      setSprayings(data);
    } catch (error) {
      showError('خطأ في تحميل عمليات الرش', 'تعذر تحميل بيانات عمليات الرش');
      console.error('Error loading sprayings:', error);
    } finally {
      setLoading(false);
    }
  }, [showError]);

  const addSpraying = useCallback(async (spraying: Omit<Spraying, 'id' | 'createdAt'>) => {
    try {
      const id = await enhancedDB.addSpraying(spraying);
      const newSpraying = { ...spraying, id, createdAt: new Date().toISOString() } as Spraying;
      setSprayings(prev => [...prev, newSpraying]);
      showSuccess('تم إضافة عملية الرش', 'تم إضافة عملية الرش بنجاح');
      return id;
    } catch (error) {
      showError('خطأ في إضافة عملية الرش', 'تعذر إضافة عملية الرش');
      throw error;
    }
  }, [showSuccess, showError]);

  useEffect(() => {
    loadSprayings();
  }, [loadSprayings]);

  return {
    sprayings,
    loading,
    addSpraying,
    refreshSprayings: loadSprayings
  };
};

export const useFertilizations = () => {
  const [fertilizations, setFertilizations] = useState<Fertilization[]>([]);
  const [loading, setLoading] = useState(true);
  const { showError, showSuccess } = useToast();

  const loadFertilizations = useCallback(async () => {
    try {
      setLoading(true);
      const data = await enhancedDB.getFertilizations();
      setFertilizations(data);
    } catch (error) {
      showError('خطأ في تحميل عمليات التسميد', 'تعذر تحميل بيانات عمليات التسميد');
      console.error('Error loading fertilizations:', error);
    } finally {
      setLoading(false);
    }
  }, [showError]);

  const addFertilization = useCallback(async (fertilization: Omit<Fertilization, 'id' | 'createdAt'>) => {
    try {
      const id = await enhancedDB.addFertilization(fertilization);
      const newFertilization = { ...fertilization, id, createdAt: new Date().toISOString() } as Fertilization;
      setFertilizations(prev => [...prev, newFertilization]);
      showSuccess('تم إضافة عملية التسميد', 'تم إضافة عملية التسميد بنجاح');
      return id;
    } catch (error) {
      showError('خطأ في إضافة عملية التسميد', 'تعذر إضافة عملية التسميد');
      throw error;
    }
  }, [showSuccess, showError]);

  useEffect(() => {
    loadFertilizations();
  }, [loadFertilizations]);

  return {
    fertilizations,
    loading,
    addFertilization,
    refreshFertilizations: loadFertilizations
  };
};