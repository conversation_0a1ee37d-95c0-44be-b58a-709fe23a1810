#!/usr/bin/env node

/**
 * فحص سلامة النظام والتحقق من التحسينات
 * شركة الشفق للزراعة الحديثة
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// ألوان للطباعة
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
};

// رسائل التحقق
const messages = {
  header: '🌱 فحص سلامة نظام إدارة مزرعة الخس',
  company: 'شركة الشفق للزراعة الحديثة',
  separator: '='.repeat(50),
  success: '✅',
  error: '❌',
  warning: '⚠️',
  info: 'ℹ️'
};

console.log(`${colors.bold}${colors.green}${messages.header}${colors.reset}`);
console.log(`${colors.cyan}${messages.company}${colors.reset}`);
console.log(`${colors.blue}${messages.separator}${colors.reset}\n`);

let checksCount = 0;
let passedChecks = 0;
let warnings = 0;

function checkFile(filePath, description) {
  checksCount++;
  const fullPath = path.join(__dirname, filePath);
  
  if (fs.existsSync(fullPath)) {
    console.log(`${messages.success} ${colors.green}${description}${colors.reset}`);
    passedChecks++;
    return true;
  } else {
    console.log(`${messages.error} ${colors.red}${description} - مفقود${colors.reset}`);
    return false;
  }
}

function checkContent(filePath, searchText, description) {
  checksCount++;
  const fullPath = path.join(__dirname, filePath);
  
  try {
    const content = fs.readFileSync(fullPath, 'utf8');
    if (content.includes(searchText)) {
      console.log(`${messages.success} ${colors.green}${description}${colors.reset}`);
      passedChecks++;
      return true;
    } else {
      console.log(`${messages.warning} ${colors.yellow}${description} - غير موجود${colors.reset}`);
      warnings++;
      return false;
    }
  } catch (error) {
    console.log(`${messages.error} ${colors.red}${description} - خطأ في القراءة${colors.reset}`);
    return false;
  }
}

// 1. فحص الملفات الأساسية
console.log(`${colors.bold}1. فحص الملفات الأساسية:${colors.reset}`);
checkFile('package.json', 'ملف package.json موجود');
checkFile('src/App.tsx', 'ملف App.tsx الرئيسي موجود');
checkFile('src/main.tsx', 'ملف main.tsx موجود');
checkFile('src/types/index.ts', 'ملف الأنواع موجود');
console.log();

// 2. فحص Custom Hooks
console.log(`${colors.bold}2. فحص Custom Hooks:${colors.reset}`);
checkFile('src/hooks/useToast.ts', 'Hook نظام التوست');
checkFile('src/hooks/useDatabase.ts', 'Hook إدارة قاعدة البيانات');
checkFile('src/hooks/useSearch.ts', 'Hook البحث والفلترة');
checkFile('src/hooks/useBackup.ts', 'Hook النسخ الاحتياطي');
checkFile('src/hooks/useDashboard.ts', 'Hook لوحة التحكم');
console.log();

// 3. فحص مكونات UI الجديدة
console.log(`${colors.bold}3. فحص مكونات UI الجديدة:${colors.reset}`);
checkFile('src/components/Toast.tsx', 'مكون الإشعارات');
checkFile('src/components/SearchAndFilter.tsx', 'مكون البحث والفلترة');
checkFile('src/components/Pagination.tsx', 'مكون التصفح');
checkFile('src/components/LoadingSpinner.tsx', 'مكون التحميل');
console.log();

// 4. فحص قاعدة البيانات المحسنة
console.log(`${colors.bold}4. فحص قاعدة البيانات المحسنة:${colors.reset}`);
checkFile('src/utils/enhancedDatabase.ts', 'قاعدة البيانات المحسنة');
checkContent('src/utils/enhancedDatabase.ts', 'IndexedDB', 'دعم IndexedDB');
checkContent('src/utils/enhancedDatabase.ts', 'exportAllData', 'وظيفة التصدير');
console.log();

// 5. فحص تحديثات App.tsx
console.log(`${colors.bold}5. فحص تحديثات الملف الرئيسي:${colors.reset}`);
checkContent('src/App.tsx', 'useToast', 'استخدام Hook التوست');
checkContent('src/App.tsx', 'useDashboard', 'استخدام Hook لوحة التحكم');
checkContent('src/App.tsx', 'ToastContainer', 'عرض مكون الإشعارات');
console.log();

// 6. فحص تحديثات main.tsx
console.log(`${colors.bold}6. فحص تهيئة النظام:${colors.reset}`);
checkContent('src/main.tsx', 'enhancedDB.init', 'تهيئة قاعدة البيانات');
checkContent('src/main.tsx', 'initializeApp', 'وظيفة التهيئة');
console.log();

// 7. فحص ملفات التوثيق
console.log(`${colors.bold}7. فحص ملفات التوثيق:${colors.reset}`);
checkFile('README-IMPROVEMENTS.md', 'دليل التحسينات');
checkFile('test-improvements.html', 'صفحة اختبار التحسينات');
checkFile('quick-start.bat', 'ملف التشغيل السريع');
console.log();

// 8. فحص الأنواع الجديدة
console.log(`${colors.bold}8. فحص الأنواع الجديدة:${colors.reset}`);
checkContent('src/types/index.ts', 'interface Toast', 'نوع Toast');
checkContent('src/types/index.ts', 'interface SearchOptions', 'نوع SearchOptions');
checkContent('src/types/index.ts', 'interface BackupInfo', 'نوع BackupInfo');
console.log();

// 9. فحص التبعيات
console.log(`${colors.bold}9. فحص التبعيات:${colors.reset}`);
const packageJsonPath = path.join(__dirname, 'package.json');
if (fs.existsSync(packageJsonPath)) {
  const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
  
  const requiredDeps = ['react', 'react-dom', 'typescript'];
  requiredDeps.forEach(dep => {
    if (packageJson.dependencies && packageJson.dependencies[dep]) {
      console.log(`${messages.success} ${colors.green}${dep} مثبت${colors.reset}`);
      passedChecks++;
    } else {
      console.log(`${messages.error} ${colors.red}${dep} غير مثبت${colors.reset}`);
    }
    checksCount++;
  });
} else {
  console.log(`${messages.error} ${colors.red}ملف package.json غير موجود${colors.reset}`);
}
console.log();

// النتائج النهائية
console.log(`${colors.bold}${colors.blue}النتائج النهائية:${colors.reset}`);
console.log(`${colors.blue}${messages.separator}${colors.reset}`);

const successRate = Math.round((passedChecks / checksCount) * 100);
const statusColor = successRate >= 90 ? colors.green : 
                   successRate >= 70 ? colors.yellow : colors.red;

console.log(`إجمالي الفحوصات: ${colors.cyan}${checksCount}${colors.reset}`);
console.log(`الفحوصات الناجحة: ${colors.green}${passedChecks}${colors.reset}`);
console.log(`التحذيرات: ${colors.yellow}${warnings}${colors.reset}`);
console.log(`الفحوصات الفاشلة: ${colors.red}${checksCount - passedChecks - warnings}${colors.reset}`);
console.log(`معدل النجاح: ${statusColor}${successRate}%${colors.reset}`);

console.log();

if (successRate >= 90) {
  console.log(`${messages.success} ${colors.bold}${colors.green}النظام جاهز للاستخدام!${colors.reset}`);
  console.log(`${colors.green}جميع التحسينات تم تطبيقها بنجاح${colors.reset}`);
} else if (successRate >= 70) {
  console.log(`${messages.warning} ${colors.bold}${colors.yellow}النظام يحتاج بعض التحسينات${colors.reset}`);
  console.log(`${colors.yellow}راجع التحذيرات أعلاه${colors.reset}`);
} else {
  console.log(`${messages.error} ${colors.bold}${colors.red}النظام يحتاج إصلاحات${colors.reset}`);
  console.log(`${colors.red}يرجى إصلاح الأخطاء المذكورة أعلاه${colors.reset}`);
}

console.log();
console.log(`${colors.bold}${colors.cyan}للبدء في استخدام النظام:${colors.reset}`);
console.log(`${colors.cyan}1. قم بتشغيل: npm install${colors.reset}`);
console.log(`${colors.cyan}2. قم بتشغيل: npm run dev${colors.reset}`);
console.log(`${colors.cyan}3. أو استخدم: quick-start.bat${colors.reset}`);

console.log();
console.log(`${colors.bold}${colors.green}🌱 شركة الشفق للزراعة الحديثة${colors.reset}`);
console.log(`${colors.green}نحو مستقبل زراعي أكثر ذكاءً وكفاءة${colors.reset}`);

// إنهاء مع رمز الإخراج المناسب
process.exit(successRate >= 90 ? 0 : 1);