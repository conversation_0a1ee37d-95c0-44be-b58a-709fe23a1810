import React, { useState, useEffect } from 'react';
import {
  Plus,
  Minus,
  FileText,
  Package,
  Warehouse,
  Edit,
  Archive,
  Leaf,
  TrendingUp,
  Trash2,
  Save,
  X,
  Printer,
  Download
} from 'lucide-react';
import Modal from './Modal';

interface InventoryItem {
  id: number;
  name: string;
  quantity: number;
  date: string;
  notes?: string;
}

interface Movement {
  id: number;
  type: 'in' | 'out';
  itemName: string;
  quantity: number;
  date: string;
  time: string;
  notes?: string;
}

interface WarehouseViewProps {
  setCurrentView: (view: string) => void;
  navigationItems: any[];
  currentView: string;
}

const WarehouseView: React.FC<WarehouseViewProps> = ({
  setCurrentView,
  navigationItems,
  currentView
}) => {
  // الأصناف المحددة للمستودع
  const warehouseItems = [
    'صندوق حقل',
    'شبر',
    'صندوق بلاستيك اسود قياس 40*60',
    'طبليات خشب',
    'لزيق',
    'أكياس',
    'زوايا',
    'كليبس',
    'طبليات بلاستيك',
    'ورق رومين',
    'جاسوس',
    'خيط'
  ];

  // حالات البيانات
  const [inventory, setInventory] = useState<InventoryItem[]>([]);
  const [movements, setMovements] = useState<Movement[]>([]);
  const [availableItems, setAvailableItems] = useState<{id: number, name: string, unit: string, description: string}[]>([]);

  // حالات النوافذ المنبثقة
  const [isAddModalOpen, setIsAddModalOpen] = useState<boolean>(false);
  const [isRemoveModalOpen, setIsRemoveModalOpen] = useState<boolean>(false);
  const [isMovementsModalOpen, setIsMovementsModalOpen] = useState<boolean>(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState<boolean>(false);
  const [isItemManagementModalOpen, setIsItemManagementModalOpen] = useState<boolean>(false);
  const [isManageItemsModalOpen, setIsManageItemsModalOpen] = useState<boolean>(false);

  // حالات البيانات الجديدة
  const [newItem, setNewItem] = useState<{name: string, quantity: number, date: string, notes: string}>({
    name: '', quantity: 0, date: new Date().toISOString().split('T')[0], notes: ''
  });
  const [newItemName, setNewItemName] = useState<string>('');
  const [removeItem, setRemoveItem] = useState<{itemId: string, quantity: number, date: string, notes: string}>({
    itemId: '', quantity: 0, date: new Date().toISOString().split('T')[0], notes: ''
  });
  const [editingItem, setEditingItem] = useState<InventoryItem | null>(null);
  const [newItemData, setNewItemData] = useState<{name: string, unit: string, description: string}>({
    name: '', unit: 'حبة', description: ''
  });
  const [newUnit, setNewUnit] = useState<string>('');

  // الإحصائيات
  const totalItems = inventory.length;
  const totalQuantity = inventory.reduce((sum, item) => sum + item.quantity, 0);

  // تحميل البيانات من localStorage
  useEffect(() => {
    const loadData = () => {
      try {
        // تحميل المخزون
        const savedInventory = localStorage.getItem('warehouseInventory');
        if (savedInventory) {
          setInventory(JSON.parse(savedInventory));
        }

        // تحميل الحركات
        const savedMovements = localStorage.getItem('warehouseMovements');
        if (savedMovements) {
          setMovements(JSON.parse(savedMovements));
        }

        // تحميل الأصناف المتاحة أو إنشاؤها
        const savedItems = localStorage.getItem('warehouseAvailableItems');
        if (savedItems) {
          setAvailableItems(JSON.parse(savedItems));
        } else {
          // إنشاء الأصناف الافتراضية
          const defaultItems = warehouseItems.map((item, index) => ({
            id: index + 1,
            name: item,
            unit: 'حبة',
            description: ''
          }));
          setAvailableItems(defaultItems);
          localStorage.setItem('warehouseAvailableItems', JSON.stringify(defaultItems));
        }
      } catch (error) {
        console.error('خطأ في تحميل بيانات المستودع:', error);
      }
    };

    loadData();
  }, []);

  // حفظ البيانات في localStorage
  useEffect(() => {
    localStorage.setItem('warehouseInventory', JSON.stringify(inventory));
  }, [inventory]);

  useEffect(() => {
    localStorage.setItem('warehouseMovements', JSON.stringify(movements));
  }, [movements]);

  useEffect(() => {
    localStorage.setItem('warehouseAvailableItems', JSON.stringify(availableItems));
  }, [availableItems]);

  // دوال التنسيق
  const formatDate = (date: Date) => {
    const day = date.getDate().toString().padStart(2, '0');
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const year = date.getFullYear();
    return `${day}/${month}/${year}`;
  };

  const formatDateForDisplay = (dateStr: string) => {
    if (!dateStr) return '-';
    if (dateStr.includes('/') && dateStr.split('/').length === 3) {
      const parts = dateStr.split('/');
      if (parts[0].length <= 2 && parts[1].length <= 2 && parts[2].length === 4) {
        return dateStr;
      }
    }
    try {
      const date = new Date(dateStr);
      if (!isNaN(date.getTime())) {
        return formatDate(date);
      }
    } catch (error) {
      console.error('خطأ في تنسيق التاريخ:', error);
    }
    return dateStr;
  };

  const formatTimeForDisplay = (timeStr: string) => {
    if (!timeStr) return '';
    return timeStr.slice(0, 5);
  };

  const formatQuantity = (quantity: number) => {
    if (quantity === 0) return '0';
    return `${quantity}`;
  };

  const convertDateForInput = (dateStr: string) => {
    if (!dateStr) return '';
    try {
      if (dateStr.includes('/')) {
        const [day, month, year] = dateStr.split('/');
        return `${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}`;
      }
      return dateStr;
    } catch (error) {
      return '';
    }
  };

  const getInventoryNotes = (itemName: string) => {
    const item = inventory.find(item => item.name === itemName);
    return item?.notes || '';
  };

  // دوال المعالجة
  const handleAddItem = async () => {
    try {
      let actualItemName = newItem.name;
      if (newItem.name === "صنف جديد") {
        if (!newItemName.trim()) {
          alert('يرجى إدخال اسم الصنف الجديد');
          return;
        }
        actualItemName = newItemName.trim();
      }

      if (!actualItemName) {
        alert('يرجى اختيار أو إدخال اسم الصنف');
        return;
      }

      const existingItemIndex = inventory.findIndex(item => item.name === actualItemName);

      if (existingItemIndex !== -1) {
        const updatedInventory = [...inventory];
        updatedInventory[existingItemIndex] = {
          ...updatedInventory[existingItemIndex],
          quantity: updatedInventory[existingItemIndex].quantity + newItem.quantity,
          date: formatDate(new Date()),
          notes: newItem.notes || updatedInventory[existingItemIndex].notes
        };
        setInventory(updatedInventory);
      } else {
        const newInventoryItem: InventoryItem = {
          id: Date.now(),
          name: actualItemName,
          quantity: newItem.quantity,
          date: formatDate(new Date()),
          notes: newItem.notes
        };
        setInventory([...inventory, newInventoryItem]);
      }

      // إضافة حركة
      const movement: Movement = {
        id: Date.now(),
        type: 'in',
        itemName: actualItemName,
        quantity: newItem.quantity,
        date: formatDate(new Date()),
        time: new Date().toTimeString().slice(0, 8),
        notes: newItem.notes
      };
      setMovements([movement, ...movements]);

      // إعادة تعيين النموذج
      setNewItem({ name: '', quantity: 0, date: new Date().toISOString().split('T')[0], notes: '' });
      setNewItemName('');
      setIsAddModalOpen(false);
      alert('تم إضافة المخزون بنجاح!');
    } catch (error) {
      console.error('خطأ في إضافة المخزون:', error);
      alert('حدث خطأ أثناء إضافة المخزون');
    }
  };

  const handleRemoveItem = () => {
    try {
      if (!removeItem.itemId) {
        alert('يرجى اختيار صنف');
        return;
      }

      if (removeItem.quantity <= 0) {
        alert('يرجى إدخال كمية صحيحة للإخراج');
        return;
      }

      const itemIndex = inventory.findIndex(item => item.id.toString() === removeItem.itemId);
      if (itemIndex === -1) {
        alert('الصنف غير موجود');
        return;
      }

      const item = inventory[itemIndex];
      if (item.quantity < removeItem.quantity) {
        alert('الكمية المطلوبة أكبر من المتوفر في المخزون');
        return;
      }

      const updatedInventory = [...inventory];
      const newQuantity = item.quantity - removeItem.quantity;

      if (newQuantity === 0) {
        updatedInventory.splice(itemIndex, 1);
      } else {
        updatedInventory[itemIndex] = {
          ...item,
          quantity: newQuantity
        };
      }

      setInventory(updatedInventory);

      // إضافة حركة إخراج
      const movement: Movement = {
        id: Date.now(),
        type: 'out',
        itemName: item.name,
        quantity: removeItem.quantity,
        date: formatDate(new Date()),
        time: new Date().toTimeString().slice(0, 8),
        notes: removeItem.notes
      };
      setMovements([movement, ...movements]);

      setRemoveItem({ itemId: '', quantity: 0, date: new Date().toISOString().split('T')[0], notes: '' });
      setIsRemoveModalOpen(false);
      alert('تم إخراج المخزون بنجاح!');
    } catch (error) {
      console.error('خطأ في إخراج المخزون:', error);
      alert('حدث خطأ أثناء إخراج المخزون');
    }
  };

  const handleEditItem = (item: InventoryItem) => {
    setEditingItem(item);
    setIsEditModalOpen(true);
  };

  const handleSaveEdit = () => {
    if (!editingItem) return;

    try {
      const updatedInventory = inventory.map(item =>
        item.id === editingItem.id ? editingItem : item
      );
      setInventory(updatedInventory);
      setIsEditModalOpen(false);
      setEditingItem(null);
      alert('تم تحديث البيانات بنجاح!');
    } catch (error) {
      console.error('خطأ في تحديث البيانات:', error);
      alert('حدث خطأ أثناء تحديث البيانات');
    }
  };

  const handleDeleteItem = (id: number) => {
    if (window.confirm('هل أنت متأكد من حذف هذا الصنف؟')) {
      setInventory(inventory.filter(item => item.id !== id));
      alert('تم حذف الصنف بنجاح!');
    }
  };

  const handleAddNewItem = () => {
    if (!newItemData.name.trim()) {
      alert('يرجى إدخال اسم الصنف');
      return;
    }

    let actualUnit = newItemData.unit;
    if (newItemData.unit === "وحدة جديدة") {
      if (!newUnit.trim()) {
        alert('يرجى إدخال اسم الوحدة الجديدة');
        return;
      }
      actualUnit = newUnit.trim();
    }

    const newItem = {
      id: Date.now(),
      name: newItemData.name.trim(),
      unit: actualUnit,
      description: newItemData.description
    };

    setAvailableItems([...availableItems, newItem]);
    setNewItemData({ name: '', unit: 'حبة', description: '' });
    setNewUnit('');
    setIsItemManagementModalOpen(false);
    alert('تم إضافة الصنف الجديد بنجاح!');
  };

  const handleDeleteAvailableItem = (itemId: number) => {
    const itemToDelete = availableItems.find(item => item.id === itemId);
    if (!itemToDelete) return;

    // التحقق من وجود الصنف في المخزون
    const isInInventory = inventory.some(invItem => invItem.name === itemToDelete.name);

    if (isInInventory) {
      alert(`لا يمكن حذف الصنف "${itemToDelete.name}" لأنه موجود في المخزون الحالي.\nيرجى إخراج جميع الكميات أولاً.`);
      return;
    }

    if (window.confirm(`هل أنت متأكد من حذف الصنف "${itemToDelete.name}"؟\nلا يمكن التراجع عن هذا الإجراء.`)) {
      const updatedItems = availableItems.filter(item => item.id !== itemId);
      setAvailableItems(updatedItems);
      alert('تم حذف الصنف بنجاح!');
    }
  };

  const handlePrint = () => {
    window.print();
  };

  const handleExportReport = () => {
    try {
      const today = new Date();
      const formattedDate = today.toLocaleDateString('ar-SA', {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      });

      const totalIn = movements.filter(m => m.type === 'in').reduce((sum, m) => sum + m.quantity, 0);
      const totalOut = movements.filter(m => m.type === 'out').reduce((sum, m) => sum + m.quantity, 0);
      const totalItems = inventory.length;
      const totalQuantity = inventory.reduce((sum, item) => sum + item.quantity, 0);

      const htmlContent = `
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تقرير المستودع - شركة الشفق للزراعة الحديثة</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background: white;
            direction: rtl;
            text-align: right;
        }

        .container {
            max-width: 210mm;
            margin: 0 auto;
            padding: 20mm;
            background: white;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 3px solid #059669;
            padding-bottom: 20px;
        }

        .company-name {
            font-size: 28px;
            font-weight: bold;
            color: #059669;
            margin-bottom: 10px;
        }

        .company-name-en {
            font-size: 16px;
            color: #6b7280;
            margin-bottom: 20px;
        }

        .report-title {
            font-size: 24px;
            font-weight: bold;
            color: #1f2937;
            margin-bottom: 10px;
        }

        .report-date {
            font-size: 14px;
            color: #6b7280;
        }

        .summary-box {
            background: #f0f9ff;
            border: 1px solid #0ea5e9;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }

        .summary-title {
            font-size: 18px;
            font-weight: bold;
            color: #0c4a6e;
            margin-bottom: 15px;
        }

        .summary-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }

        .summary-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px;
            background: white;
            border-radius: 6px;
            border: 1px solid #e0f2fe;
        }

        .summary-label {
            font-weight: 500;
            color: #374151;
        }

        .summary-value {
            font-weight: bold;
            color: #059669;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            font-size: 12px;
        }

        th, td {
            border: 1px solid #d1d5db;
            padding: 8px;
            text-align: right;
        }

        th {
            background-color: #f3f4f6;
            font-weight: bold;
            color: #374151;
        }

        tr:nth-child(even) {
            background-color: #f9fafb;
        }

        .section-title {
            font-size: 18px;
            font-weight: bold;
            color: #059669;
            margin: 30px 0 15px 0;
            border-bottom: 2px solid #059669;
            padding-bottom: 5px;
        }

        .footer {
            margin-top: 50px;
            text-align: center;
            font-size: 12px;
            color: #6b7280;
            border-top: 1px solid #e5e7eb;
            padding-top: 20px;
        }

        @media print {
            body {
                margin: 0;
                padding: 0;
            }

            .container {
                max-width: none;
                margin: 0;
                padding: 15mm;
            }

            table {
                font-size: 10px;
            }

            th, td {
                padding: 6px;
            }
        }

        @page {
            size: A4;
            margin: 15mm;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="company-name">شركة الشفق للزراعة الحديثة</div>
            <div class="company-name-en">Al-Shafaq Modern Agriculture Company</div>
            <div class="report-title">تقرير المستودع</div>
            <div class="report-date">${formattedDate}</div>
        </div>

        <div class="summary-box">
            <div class="summary-title">ملخص المستودع</div>
            <div class="summary-grid">
                <div class="summary-item">
                    <span class="summary-label">إجمالي الأصناف:</span>
                    <span class="summary-value">${totalItems}</span>
                </div>
                <div class="summary-item">
                    <span class="summary-label">إجمالي الكمية:</span>
                    <span class="summary-value">${totalQuantity}</span>
                </div>
                <div class="summary-item">
                    <span class="summary-label">إجمالي الإدخال:</span>
                    <span class="summary-value">${totalIn}</span>
                </div>
                <div class="summary-item">
                    <span class="summary-label">إجمالي الإخراج:</span>
                    <span class="summary-value">${totalOut}</span>
                </div>
            </div>
        </div>

        <div class="section-title">المخزون الحالي</div>
        <table>
            <thead>
                <tr>
                    <th>#</th>
                    <th>الصنف</th>
                    <th>الوحدة</th>
                    <th>الكمية</th>
                    <th>آخر تحديث</th>
                </tr>
            </thead>
            <tbody>
                ${inventory.length === 0 ?
                  '<tr><td colspan="5" style="text-align: center; color: #6b7280; padding: 20px;">لا يوجد أصناف في المخزون</td></tr>' :
                  inventory.map((item, index) => `
                    <tr>
                        <td>${index + 1}</td>
                        <td>${item.name}</td>
                        <td>${item.unit}</td>
                        <td>${item.quantity}</td>
                        <td>${item.lastUpdated}</td>
                    </tr>
                  `).join('')
                }
            </tbody>
        </table>

        <div class="section-title">آخر الحركات</div>
        <table>
            <thead>
                <tr>
                    <th>#</th>
                    <th>النوع</th>
                    <th>الصنف</th>
                    <th>الكمية</th>
                    <th>التاريخ</th>
                    <th>الملاحظات</th>
                </tr>
            </thead>
            <tbody>
                ${movements.length === 0 ?
                  '<tr><td colspan="6" style="text-align: center; color: #6b7280; padding: 20px;">لا توجد حركات مسجلة</td></tr>' :
                  movements.slice(-10).reverse().map((movement, index) => `
                    <tr>
                        <td>${index + 1}</td>
                        <td style="color: ${movement.type === 'in' ? '#059669' : '#dc2626'};">
                            ${movement.type === 'in' ? 'إدخال' : 'إخراج'}
                        </td>
                        <td>${movement.itemName}</td>
                        <td>${movement.quantity}</td>
                        <td>${movement.date}</td>
                        <td>${movement.notes || '-'}</td>
                    </tr>
                  `).join('')
                }
            </tbody>
        </table>

        <div class="footer">
            <p>تم إنشاء هذا التقرير بواسطة نظام إدارة المخزون - شركة الشفق للزراعة الحديثة</p>
            <p>تاريخ الطباعة: ${formattedDate}</p>
        </div>
    </div>
</body>
</html>`;

      const blob = new Blob([htmlContent], { type: 'text/html;charset=utf-8' });
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `تقرير-المستودع-${formatDate(new Date()).replace(/\//g, '-')}.html`;
      link.click();
      URL.revokeObjectURL(url);
      alert('تم تصدير التقرير بنجاح!');
    } catch (error) {
      console.error('خطأ في تصدير التقرير:', error);
      alert('حدث خطأ أثناء تصدير التقرير');
    }
  };

  // تاريخ اليوم بالميلادي
  const today = new Date();
  const weekdays = ['الأحد', 'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت'];
  const months = ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو', 'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'];
  const formattedDate = `${weekdays[today.getDay()]}، ${today.getDate()} ${months[today.getMonth()]} ${today.getFullYear()}`;

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-50 via-blue-50 to-green-100 p-6 space-y-6">
      {/* Header */}
      <div className="bg-gradient-to-r from-indigo-700 via-purple-600 to-green-600 text-white p-6 shadow-2xl rounded-b-3xl">
        <div className="container mx-auto">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <div className="w-12 h-12 bg-white/30 rounded-2xl flex items-center justify-center backdrop-blur-md shadow-lg border-2 border-indigo-300">
                <Warehouse size={24} className="text-white" />
              </div>
              <div>
                <h1 className="text-2xl font-bold font-arabic tracking-wide text-white drop-shadow-lg">إدارة المستودع</h1>
                <p className="text-indigo-100 text-sm font-medium tracking-wider">Warehouse Management</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Navigation */}
      <div className="bg-gradient-to-r from-purple-500 to-indigo-500 p-4 shadow-lg">
        <div className="container mx-auto">
          <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 xl:grid-cols-9 gap-3">
            {navigationItems.map((item) => {
              const IconComponent = item.icon;
              return (
                <button
                  key={item.id}
                  onClick={() => setCurrentView(item.id)}
                  className={`p-4 rounded-2xl transition-all duration-300 transform hover:scale-105 hover:shadow-lg ${
                    currentView === item.id
                      ? 'bg-white text-purple-600 shadow-xl border-2 border-purple-500'
                      : 'bg-white text-purple-600 hover:bg-gray-50 shadow-md'
                  }`}
                >
                  <IconComponent size={24} className="mx-auto mb-2 text-purple-600" />
                  <span className="text-sm font-bold block font-arabic text-purple-600">{item.name}</span>
                </button>
              );
            })}
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="container mx-auto p-6">
        {/* Page Header */}
        <div className="flex items-center justify-between mb-8">
          <div className="flex items-center gap-4">
            <div className="w-12 h-12 bg-gradient-to-br from-purple-500 to-indigo-600 rounded-2xl flex items-center justify-center shadow-lg">
              <Warehouse size={24} className="text-white" />
            </div>
            <div>
              <h2 className="text-3xl font-bold font-arabic text-gray-800 tracking-wide">إدارة المستودع</h2>
              <p className="text-gray-600 text-sm mt-1">{formattedDate}</p>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex gap-3 flex-wrap">
            <button
              onClick={() => setIsAddModalOpen(true)}
              className="bg-gradient-to-r from-purple-500 to-purple-600 text-white px-6 py-3 rounded-xl hover:from-purple-600 hover:to-purple-700 transition-all duration-300 flex items-center gap-2 shadow-lg hover:shadow-xl transform hover:scale-105 font-arabic font-medium tracking-wide"
            >
              <Plus size={20} />
              إضافة مخزون
            </button>
            <button
              onClick={() => setIsRemoveModalOpen(true)}
              className="bg-gradient-to-r from-red-500 to-red-600 text-white px-6 py-3 rounded-xl hover:from-red-600 hover:to-red-700 transition-all duration-300 flex items-center gap-2 shadow-lg hover:shadow-xl transform hover:scale-105 font-arabic font-medium tracking-wide"
            >
              <Minus size={20} />
              إخراج مخزون
            </button>
            <button
              onClick={() => setIsMovementsModalOpen(true)}
              className="bg-gradient-to-r from-blue-500 to-blue-600 text-white px-6 py-3 rounded-xl hover:from-blue-600 hover:to-blue-700 transition-all duration-300 flex items-center gap-2 shadow-lg hover:shadow-xl transform hover:scale-105 font-arabic font-medium tracking-wide"
            >
              <FileText size={20} />
              سجل الحركات
            </button>
            <button
              onClick={() => setIsManageItemsModalOpen(true)}
              className="bg-gradient-to-r from-green-500 to-green-600 text-white px-6 py-3 rounded-xl hover:from-green-600 hover:to-green-700 transition-all duration-300 flex items-center gap-2 shadow-lg hover:shadow-xl transform hover:scale-105 font-arabic font-medium tracking-wide"
            >
              <Package size={20} />
              إدارة الأصناف
            </button>
          </div>
        </div>

        {/* Statistics Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          <div className="bg-white rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 border border-purple-100">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-gray-600 text-sm font-medium mb-2 font-arabic tracking-wide">إجمالي الأصناف</h3>
                <p className="text-3xl font-bold text-purple-600 font-arabic">{totalItems}</p>
              </div>
              <div className="w-12 h-12 bg-gradient-to-br from-purple-500 to-purple-600 rounded-2xl flex items-center justify-center">
                <Package size={24} className="text-white" />
              </div>
            </div>
          </div>

          <div className="bg-white rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 border border-blue-100">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-gray-600 text-sm font-medium mb-2 font-arabic tracking-wide">إجمالي الكمية</h3>
                <p className="text-3xl font-bold text-blue-600 font-arabic">{totalQuantity}</p>
              </div>
              <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-2xl flex items-center justify-center">
                <Archive size={24} className="text-white" />
              </div>
            </div>
          </div>

          <div className="bg-white rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 border border-indigo-100">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-gray-600 text-sm font-medium mb-2 font-arabic tracking-wide">متوسط الكمية</h3>
                <p className="text-2xl font-bold text-indigo-600 font-arabic">{totalItems > 0 ? Math.round(totalQuantity / totalItems) : 0}</p>
              </div>
              <div className="w-12 h-12 bg-gradient-to-br from-indigo-500 to-indigo-600 rounded-2xl flex items-center justify-center">
                <TrendingUp size={24} className="text-white" />
              </div>
            </div>
          </div>
        </div>

        {/* Inventory Table */}
        <div className="bg-white rounded-2xl shadow-xl overflow-hidden border border-purple-100">
          <div className="bg-gradient-to-r from-purple-500 to-indigo-600 text-white p-6">
            <h3 className="text-xl font-bold font-arabic tracking-wide">جدول المستودع</h3>
          </div>

          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-purple-50">
                <tr>
                  <th className="px-6 py-4 text-right text-sm font-semibold text-purple-700 font-arabic tracking-wide">نوع الصنف</th>
                  <th className="px-6 py-4 text-right text-sm font-semibold text-purple-700 font-arabic tracking-wide">الكمية</th>
                  <th className="px-6 py-4 text-right text-sm font-semibold text-purple-700 font-arabic tracking-wide">التاريخ</th>
                  <th className="px-6 py-4 text-right text-sm font-semibold text-purple-700 font-arabic tracking-wide">الملاحظات</th>
                  <th className="px-6 py-4 text-right text-sm font-semibold text-purple-700 font-arabic tracking-wide">الكمية</th>
                  <th className="px-6 py-4 text-right text-sm font-semibold text-purple-700 font-arabic tracking-wide">الإجراءات</th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-200">
                {inventory.map((item) => (
                  <tr key={item.id} className="hover:bg-purple-50 transition-colors duration-200">
                    <td className="px-6 py-4 text-sm font-medium text-gray-900 font-arabic">{item.name}</td>
                    <td className="px-6 py-4 text-sm text-gray-700 font-arabic">{item.quantity}</td>
                    <td className="px-6 py-4 text-sm text-gray-700 font-arabic">{formatDateForDisplay(item.date)}</td>
                    <td className="px-6 py-4 text-sm text-gray-600 max-w-xs truncate font-arabic" title={item.notes}>
                      {item.notes || '-'}
                    </td>
                    <td className="px-6 py-4 text-sm font-semibold text-purple-600 font-arabic">{item.quantity}</td>
                    <td className="px-6 py-4 text-sm">
                      <div className="flex gap-2">
                        <button
                          onClick={() => handleEditItem(item)}
                          className="text-purple-600 hover:text-purple-800 p-2 hover:bg-purple-50 rounded-lg transition-all duration-200"
                        >
                          <Edit size={16} />
                        </button>
                        <button
                          onClick={() => handleDeleteItem(item.id)}
                          className="text-red-600 hover:text-red-800 p-2 hover:bg-red-50 rounded-lg transition-all duration-200"
                        >
                          <Trash2 size={16} />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </div>

      {/* Add Item Modal */}
      <Modal isOpen={isAddModalOpen} onClose={() => {
        setIsAddModalOpen(false);
        setNewItem({ name: '', quantity: 0, date: new Date().toISOString().split('T')[0], notes: '' });
        setNewItemName('');
      }} title="إضافة مخزون جديد">
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">اسم الصنف</label>
            <select
              value={newItem.name}
              onChange={(e) => {
                console.log('تم اختيار صنف:', e.target.value);
                if (e.target.value === "صنف جديد") {
                  setIsItemManagementModalOpen(true);
                  return;
                }
                setNewItem({...newItem, name: e.target.value});
                if (e.target.value !== "صنف جديد") {
                  setNewItemName('');
                }
              }}
              className="w-full p-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-all duration-200"
            >
              <option value="">اختر الصنف</option>
              {availableItems && availableItems.length > 0 && (
                availableItems.map((item) => (
                  <option key={item.id} value={item.name}>
                    {item.name}
                  </option>
                ))
              )}
              <option value="صنف جديد">+ إضافة صنف جديد</option>
            </select>
            {newItem.name === "صنف جديد" && (
              <input
                type="text"
                value={newItemName}
                onChange={(e) => {
                  console.log('كتابة اسم صنف جديد:', e.target.value);
                  setNewItemName(e.target.value);
                }}
                className="w-full p-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-all duration-200 mt-2"
                placeholder="اكتب اسم الصنف الجديد"
                autoFocus
              />
            )}

            {/* عرض الكمية الحالية للصنف المختار */}
            {newItem.name && newItem.name !== "صنف جديد" && newItem.name !== "" && (
              <div className="mt-2 p-3 bg-purple-50 rounded-lg border border-purple-200">
                <p className="text-sm text-purple-700 font-arabic">
                  <strong>الكمية الحالية:</strong> {(() => {
                    const item = inventory.find(item => item.name === newItem.name);
                    if (item) {
                      return `${item.quantity}`;
                    }
                    return 'غير محدد';
                  })()}
                </p>
              </div>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">الكمية</label>
            <input
              type="number"
              value={newItem.quantity}
              onChange={(e) => setNewItem({...newItem, quantity: parseInt(e.target.value) || 0})}
              className="w-full p-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-all duration-200"
              min="0"
              step="1"
              inputMode="numeric"
              pattern="[0-9]*"
              placeholder="0"
              autoComplete="off"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">تاريخ الإدخال</label>
            <input
              type="date"
              value={newItem.date}
              onChange={(e) => setNewItem({...newItem, date: e.target.value})}
              className="w-full p-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-all duration-200"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">الملاحظات</label>
            <textarea
              value={newItem.notes}
              onChange={(e) => setNewItem({...newItem, notes: e.target.value})}
              className="w-full p-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-all duration-200 resize-none"
              rows={3}
              placeholder="أدخل أي ملاحظات إضافية (اختياري)"
            />
          </div>

          <div className="flex gap-3 pt-4">
            <button
              onClick={handleAddItem}
              className="flex-1 bg-gradient-to-r from-purple-500 to-purple-600 text-white py-3 rounded-xl hover:from-purple-600 hover:to-purple-700 transition-all duration-300 flex items-center justify-center gap-2 shadow-lg"
            >
              <Save size={20} />
              حفظ
            </button>
            <button
              onClick={() => {
                setIsAddModalOpen(false);
                setNewItem({ name: '', quantity: 0, date: new Date().toISOString().split('T')[0], notes: '' });
                setNewItemName('');
              }}
              className="flex-1 bg-gray-500 text-white py-3 rounded-xl hover:bg-gray-600 transition-all duration-300"
            >
              إلغاء
            </button>
          </div>
        </div>
      </Modal>

      {/* Remove Item Modal */}
      <Modal isOpen={isRemoveModalOpen} onClose={() => setIsRemoveModalOpen(false)} title="إخراج مخزون">
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">اختر الصنف</label>
            <select
              value={removeItem.itemId}
              onChange={(e) => {
                if (e.target.value === "صنف جديد") {
                  setIsItemManagementModalOpen(true);
                  return;
                }
                setRemoveItem({...removeItem, itemId: e.target.value});
              }}
              className="w-full p-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-red-500 focus:border-red-500 transition-all duration-200"
            >
              <option value="">اختر صنف</option>
              {availableItems && availableItems.length > 0 ? (
                availableItems.map((item) => (
                  <option key={item.id} value={item.id.toString()}>
                    {item.name}
                  </option>
                ))
              ) : (
                <option disabled>لا توجد أصناف متاحة</option>
              )}
              <option value="صنف جديد">+ إضافة صنف جديد</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">الكمية</label>
            <input
              type="number"
              value={removeItem.quantity}
              onChange={(e) => setRemoveItem({...removeItem, quantity: parseInt(e.target.value) || 0})}
              className="w-full p-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-red-500 focus:border-red-500 transition-all duration-200"
              min="0"
              max={removeItem.itemId ? (inventory.find(item => item.id.toString() === removeItem.itemId)?.quantity || 0) : 0}
              step="1"
              inputMode="numeric"
              pattern="[0-9]*"
              placeholder="0"
              autoComplete="off"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">تاريخ الإخراج</label>
            <input
              type="date"
              value={removeItem.date}
              onChange={(e) => setRemoveItem({...removeItem, date: e.target.value})}
              className="w-full p-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-red-500 focus:border-red-500 transition-all duration-200"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">الملاحظات</label>
            <textarea
              value={removeItem.notes}
              onChange={(e) => setRemoveItem({...removeItem, notes: e.target.value})}
              className="w-full p-2 border border-gray-300 rounded-xl focus:ring-2 focus:ring-red-500 focus:border-red-500 transition-all duration-200 resize-none text-sm"
              rows={2}
              placeholder="ملاحظات (اختياري)"
            />
          </div>

          {removeItem.itemId && (
            <div className="bg-gray-50 p-3 rounded-xl space-y-1">
              <div className="grid grid-cols-2 gap-2 text-sm text-gray-600">
                <p><strong>المتوفر:</strong> {(() => {
                  const item = inventory.find(item => item.id.toString() === removeItem.itemId);
                  return item ? item.quantity : '';
                })()}</p>
                <p><strong>سيتم إخراج:</strong> {removeItem.quantity}</p>
              </div>
              <div className="grid grid-cols-2 gap-2 text-sm text-gray-600">
                <p><strong>التاريخ:</strong> {formatDate(new Date(removeItem.date))}</p>
                <p><strong>المتبقي:</strong> {(() => {
                  const item = inventory.find(item => item.id.toString() === removeItem.itemId);
                  return item ? item.quantity - removeItem.quantity : 0;
                })()}</p>
              </div>
              {removeItem.notes && (
                <p className="text-sm text-gray-600 mt-1">
                  <strong>ملاحظات:</strong> {removeItem.notes}
                </p>
              )}
            </div>
          )}

          <div className="flex gap-3 pt-4">
            <button
              onClick={handleRemoveItem}
              className="flex-1 bg-gradient-to-r from-red-500 to-red-600 text-white py-3 rounded-xl hover:from-red-600 hover:to-red-700 transition-all duration-300 flex items-center justify-center gap-2 shadow-lg"
            >
              <Minus size={20} />
              إخراج
            </button>
            <button
              onClick={() => setIsRemoveModalOpen(false)}
              className="flex-1 bg-gray-500 text-white py-3 rounded-xl hover:bg-gray-600 transition-all duration-300"
            >
              إلغاء
            </button>
          </div>
        </div>
      </Modal>

      {/* Movements Modal */}
      <Modal isOpen={isMovementsModalOpen} onClose={() => setIsMovementsModalOpen(false)} title="سجل الحركات">
        <div className="mb-4 flex gap-3">
          <button
            onClick={handlePrint}
            className="flex-1 bg-gradient-to-r from-purple-500 to-purple-600 text-white py-2 px-4 rounded-xl hover:from-purple-600 hover:to-purple-700 transition-all duration-300 flex items-center justify-center gap-2 shadow-lg"
          >
            <Printer size={18} />
            طباعة
          </button>
          <button
            onClick={handleExportReport}
            className="flex-1 bg-gradient-to-r from-blue-500 to-blue-600 text-white py-2 px-4 rounded-xl hover:from-blue-600 hover:to-blue-700 transition-all duration-300 flex items-center justify-center gap-2 shadow-lg"
          >
            <Download size={18} />
            تصدير التقرير
          </button>
          <button
            onClick={() => {
              if (window.confirm('هل أنت متأكد من مسح جميع السجلات؟ لا يمكن التراجع عن هذا الإجراء.\n\nملاحظة: سيتم حذف سجل الحركات فقط، المخزون الحالي لن يتأثر.')) {
                setMovements([]);
                console.log('تم حذف جميع الحركات');
              }
            }}
            className="bg-gradient-to-r from-red-500 to-red-600 text-white py-2 px-3 rounded-xl hover:from-red-600 hover:to-red-700 transition-all duration-300 flex items-center justify-center gap-1 shadow-lg"
            title="مسح جميع السجلات"
          >
            <X size={16} />
            مسح
          </button>
        </div>

        <div className="max-h-96 overflow-y-auto">
          <div className="space-y-3">
            {movements.length === 0 ? (
              <div className="text-center py-8 text-gray-500">
                <FileText size={48} className="mx-auto mb-4 text-gray-300" />
                <p>لا توجد حركات مسجلة</p>
              </div>
            ) : (
              movements.map((movement) => (
                <div key={movement.id} className={`p-4 rounded-xl border-r-4 ${
                  movement.type === 'in'
                    ? 'bg-purple-50 border-purple-500'
                    : 'bg-red-50 border-red-500'
                }`}>
                  <div className="flex items-center justify-between mb-2">
                    <span className={`px-3 py-1 rounded-full text-sm font-medium ${
                      movement.type === 'in'
                        ? 'bg-purple-100 text-purple-800'
                        : 'bg-red-100 text-red-800'
                    }`}>
                      {movement.type === 'in' ? 'إدخال' : 'إخراج'}
                    </span>
                    <span className="text-sm text-gray-500">{formatDateForDisplay(movement.date)} - {formatTimeForDisplay(movement.time)}</span>
                  </div>
                  <div className="text-sm">
                    <p className="font-medium text-gray-900">{movement.itemName}</p>
                    <p className="text-gray-600">الكمية: {movement.quantity}</p>
                    {movement.notes && (
                      <p className="text-gray-600">ملاحظات الحركة: {movement.notes}</p>
                    )}
                    {getInventoryNotes(movement.itemName) && (
                      <p className="text-purple-600">ملاحظات المخزون: {getInventoryNotes(movement.itemName)}</p>
                    )}
                  </div>
                </div>
              ))
            )}
          </div>
        </div>

        {/* إجماليات بسيطة */}
        {movements.length > 0 && (
          <div className="mt-4 p-3 bg-gray-50 rounded-lg border-t-2 border-purple-500">
            <div className="text-sm text-gray-700 font-arabic text-center">
              <span className="font-semibold">📊 الإجماليات:</span>
              {' '}
              <span className="text-purple-600 font-medium">
                إدخال: {movements.filter(m => m.type === 'in').reduce((sum, m) => sum + m.quantity, 0)}
              </span>
              {' | '}
              <span className="text-red-600 font-medium">
                إخراج: {movements.filter(m => m.type === 'out').reduce((sum, m) => sum + m.quantity, 0)}
              </span>
              {' | '}
              <span className="text-blue-600 font-medium">
                الصافي: {(() => {
                  const totalIn = movements.filter(m => m.type === 'in').reduce((sum, m) => sum + m.quantity, 0);
                  const totalOut = movements.filter(m => m.type === 'out').reduce((sum, m) => sum + m.quantity, 0);
                  const net = totalIn - totalOut;
                  const sign = net >= 0 ? '+' : '';
                  return `${sign}${net}`;
                })()}
              </span>
            </div>
          </div>
        )}
      </Modal>

      {/* Edit Item Modal */}
      <Modal isOpen={isEditModalOpen} onClose={() => setIsEditModalOpen(false)} title="تعديل الصنف">
        {editingItem && (
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">اسم الصنف</label>
              <input
                type="text"
                value={editingItem.name}
                onChange={(e) => setEditingItem({...editingItem, name: e.target.value})}
                className="w-full p-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-all duration-200"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">الكمية</label>
              <input
                type="number"
                value={editingItem.quantity}
                onChange={(e) => setEditingItem({...editingItem, quantity: parseInt(e.target.value) || 0})}
                className="w-full p-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-all duration-200"
                min="0"
                step="1"
                inputMode="numeric"
                pattern="[0-9]*"
                placeholder="0"
                autoComplete="off"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">تاريخ الإدخال</label>
              <input
                type="date"
                value={editingItem.date ? convertDateForInput(editingItem.date) : ''}
                onChange={(e) => setEditingItem({...editingItem, date: formatDate(new Date(e.target.value))})}
                className="w-full p-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-all duration-200"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">الملاحظات</label>
              <textarea
                value={editingItem.notes || ''}
                onChange={(e) => setEditingItem({...editingItem, notes: e.target.value})}
                className="w-full p-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-all duration-200"
                rows={3}
                placeholder="أدخل ملاحظات حول هذا الصنف..."
              />
            </div>

            <div className="flex gap-3 pt-4">
              <button
                onClick={handleSaveEdit}
                className="flex-1 bg-gradient-to-r from-purple-500 to-purple-600 text-white py-3 rounded-xl hover:from-purple-600 hover:to-purple-700 transition-all duration-300 flex items-center justify-center gap-2 shadow-lg"
              >
                <Save size={20} />
                حفظ التعديل
              </button>
              <button
                onClick={() => setIsEditModalOpen(false)}
                className="flex-1 bg-gray-500 text-white py-3 rounded-xl hover:bg-gray-600 transition-all duration-300"
              >
                إلغاء
              </button>
            </div>
          </div>
        )}
      </Modal>

      {/* نافذة إدارة الأصناف */}
      <Modal isOpen={isItemManagementModalOpen} onClose={() => setIsItemManagementModalOpen(false)}>
        <div className="p-6">
          <div className="flex items-center gap-3 mb-6">
            <Package className="h-6 w-6 text-purple-600" />
            <h2 className="text-2xl font-bold text-gray-800">إضافة صنف جديد</h2>
          </div>

          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">اسم الصنف</label>
              <input
                type="text"
                value={newItemData.name}
                onChange={(e) => setNewItemData({...newItemData, name: e.target.value})}
                className="w-full p-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-all duration-200"
                placeholder="أدخل اسم الصنف..."
                dir="rtl"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">الوحدة</label>
              <select
                value={newItemData.unit}
                onChange={(e) => {
                  setNewItemData({...newItemData, unit: e.target.value});
                  if (e.target.value !== "وحدة جديدة") {
                    setNewUnit('');
                  }
                }}
                className="w-full p-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-all duration-200"
                dir="rtl"
              >
                <option value="ربطة">ربطة</option>
                <option value="شوال">شوال</option>
                <option value="كرتونة">كرتونة</option>
                <option value="صندوق">صندوق</option>
                <option value="حبة">حبة</option>
                <option value="وحدة جديدة">+ إضافة وحدة جديدة</option>
              </select>
              {newItemData.unit === "وحدة جديدة" && (
                <input
                  type="text"
                  value={newUnit}
                  onChange={(e) => setNewUnit(e.target.value)}
                  className="w-full p-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-all duration-200 mt-2"
                  placeholder="اكتب اسم الوحدة الجديدة"
                  dir="rtl"
                  autoFocus
                />
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">الوصف (اختياري)</label>
              <textarea
                value={newItemData.description}
                onChange={(e) => setNewItemData({...newItemData, description: e.target.value})}
                className="w-full p-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-all duration-200"
                rows={3}
                placeholder="أدخل وصف للصنف..."
                dir="rtl"
              />
            </div>

            <div className="flex gap-3 pt-4">
              <button
                onClick={handleAddNewItem}
                className="flex-1 bg-gradient-to-r from-purple-500 to-purple-600 text-white py-3 rounded-xl hover:from-purple-600 hover:to-purple-700 transition-all duration-300 flex items-center justify-center gap-2 shadow-lg"
              >
                <Plus size={20} />
                إضافة الصنف
              </button>
              <button
                onClick={() => {
                  setIsItemManagementModalOpen(false);
                  setNewItemData({ name: '', unit: 'حبة', description: '' });
                  setNewUnit('');
                }}
                className="flex-1 bg-gray-500 text-white py-3 rounded-xl hover:bg-gray-600 transition-all duration-300"
              >
                إلغاء
              </button>
            </div>
          </div>
        </div>
      </Modal>

      {/* نافذة إدارة الأصناف الموجودة */}
      <Modal isOpen={isManageItemsModalOpen} onClose={() => setIsManageItemsModalOpen(false)}>
        <div className="p-6">
          <div className="flex items-center gap-3 mb-6">
            <Package className="h-6 w-6 text-green-600" />
            <h2 className="text-2xl font-bold text-gray-800">إدارة الأصناف الموجودة</h2>
          </div>

          <div className="space-y-4 max-h-96 overflow-y-auto">
            {availableItems.length === 0 ? (
              <div className="text-center py-8 text-gray-500">
                <Package size={48} className="mx-auto mb-4 text-gray-300" />
                <p>لا توجد أصناف متاحة</p>
              </div>
            ) : (
              availableItems.map((item) => (
                <div key={item.id} className="bg-gray-50 rounded-xl p-4 border border-gray-200">
                  <div className="flex items-center justify-between">
                    <div className="flex-1">
                      <div className="flex items-center gap-4">
                        <div>
                          <h3 className="font-semibold text-gray-900 text-lg">{item.name}</h3>
                          <p className="text-sm text-gray-600">
                            <span className="font-medium">الوحدة:</span> {item.unit}
                          </p>
                          {item.description && (
                            <p className="text-sm text-gray-600 mt-1">
                              <span className="font-medium">الوصف:</span> {item.description}
                            </p>
                          )}
                        </div>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      {/* عرض حالة الاستخدام */}
                      <div className="text-sm">
                        {inventory.some(invItem => invItem.name === item.name) ? (
                          <span className="bg-green-100 text-green-800 px-2 py-1 rounded-lg font-medium">
                            مستخدم في المخزون
                          </span>
                        ) : (
                          <span className="bg-gray-100 text-gray-600 px-2 py-1 rounded-lg font-medium">
                            غير مستخدم
                          </span>
                        )}
                      </div>
                      <button
                        onClick={() => handleDeleteAvailableItem(item.id)}
                        className="text-red-600 hover:text-red-800 p-2 hover:bg-red-50 rounded-lg transition-all duration-200"
                        title="حذف الصنف"
                      >
                        <Trash2 size={16} />
                      </button>
                    </div>
                  </div>
                </div>
              ))
            )}
          </div>

          <div className="flex gap-3 pt-6 border-t border-gray-200 mt-6">
            <button
              onClick={() => {
                setIsManageItemsModalOpen(false);
                setIsItemManagementModalOpen(true);
              }}
              className="flex-1 bg-gradient-to-r from-purple-500 to-purple-600 text-white py-3 rounded-xl hover:from-purple-600 hover:to-purple-700 transition-all duration-300 flex items-center justify-center gap-2 shadow-lg"
            >
              <Plus size={20} />
              إضافة صنف جديد
            </button>
            <button
              onClick={() => setIsManageItemsModalOpen(false)}
              className="flex-1 bg-gray-500 text-white py-3 rounded-xl hover:bg-gray-600 transition-all duration-300"
            >
              إغلاق
            </button>
          </div>
        </div>
      </Modal>
    </div>
  );
};

export default WarehouseView;