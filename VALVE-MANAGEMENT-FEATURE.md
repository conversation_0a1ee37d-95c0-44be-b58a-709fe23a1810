# 🚰 ميزة إدارة المحابس للمراحل الزراعية

## 🎯 **الميزة الجديدة:**
إمكانية تحديد وتعديل عدد المحابس لكل مرحلة زراعية (من 1 إلى 6 محابس)

---

## ✨ **المميزات المضافة:**

### **1. في نموذج إضافة مرحلة جديدة:**
- 🎛️ **اختيار عدد المحابس:** من 1 إلى 6 محابس
- 🔄 **إنشاء تلقائي:** المحابس تُنشأ تلقائياً حسب العدد المحدد
- 🎨 **واجهة تفاعلية:** عرض مرئي لعدد المحابس المُنشأة
- 🔗 **ربط تلقائي:** المحابس ترتبط بنوع الخس المحدد

### **2. في نموذج تعديل المرحلة:**
- 📊 **عرض العدد الحالي:** إظهار عدد المحابس الموجودة
- ➕ **إضافة محابس:** زيادة عدد المحابس للمرحلة
- ➖ **حذف محابس:** تقليل عدد المحابس (حذف الزائد)
- 👁️ **معاينة مرئية:** عرض المحابس الحالية مع حالتها

### **3. في قاعدة البيانات:**
- 🔧 **إنشاء ذكي:** المحابس تُنشأ مع معرفات فريدة
- 🔄 **تحديث تلقائي:** ربط المحابس بالمرحلة تلقائياً
- 💾 **حفظ آمن:** البيانات تُحفظ بشكل صحيح

---

## 🎨 **واجهة المستخدم:**

### **قسم إعدادات المحابس (إضافة مرحلة):**
```
⚙️ إعدادات المحابس
┌─────────────────────────────────────┐
│ عدد المحابس: [🚰🚰 محبسان ▼]      │
│                                     │
│ المحابس المُنشأة: 2 محبس           │
│ سيتم إنشاء 2 محبس تلقائياً لهذه المرحلة │
└─────────────────────────────────────┘
```

### **قسم إدارة المحابس (تعديل مرحلة):**
```
⚙️ إدارة المحابس
┌─────────────────────────────────────┐
│ العدد الحالي: 2 محبس               │
│ تعديل العدد: [🚰🚰🚰 ثلاثة محابس ▼] │
│                                     │
│ المحابس الحالية:                   │
│ [🟢 محبس 1] [🔴 محبس 2] [🔴 محبس 3] │
└─────────────────────────────────────┘
```

---

## 🔧 **التفاصيل التقنية:**

### **1. إنشاء المحابس التلقائي:**
```typescript
const generateValves = (count: number) => {
  const valves = [];
  for (let i = 1; i <= count; i++) {
    valves.push({
      id: Date.now() + i,
      zoneId: 0, // سيتم تحديثه لاحقاً
      valveNumber: i,
      lettuceType: formData.lettuceType,
      status: 'inactive' as const,
      lastIrrigation: '',
      nextIrrigation: ''
    });
  }
  return valves;
};
```

### **2. تعديل المحابس الديناميكي:**
```typescript
// إضافة محابس جديدة
if (newCount > currentValves.length) {
  for (let i = currentValves.length + 1; i <= newCount; i++) {
    newValves.push({...newValve, valveNumber: i});
  }
}
// حذف المحابس الزائدة
else if (newCount < currentValves.length) {
  newValves = currentValves.slice(0, newCount);
}
```

### **3. ربط تلقائي بنوع الخس:**
```typescript
const handleLettuceTypeChange = (lettuceType) => {
  setFormData({
    ...formData,
    lettuceType,
    valves: formData.valves.map(valve => ({
      ...valve,
      lettuceType // تحديث نوع الخس في جميع المحابس
    }))
  });
};
```

---

## 🎯 **خيارات عدد المحابس:**

| العدد | الرمز | الوصف |
|-------|-------|--------|
| 1 | 🚰 | محبس واحد |
| 2 | 🚰🚰 | محبسان |
| 3 | 🚰🚰🚰 | ثلاثة محابس |
| 4 | 🚰🚰🚰🚰 | أربعة محابس |
| 5 | 🚰🚰🚰🚰🚰 | خمسة محابس |
| 6 | 🚰🚰🚰🚰🚰🚰 | ستة محابس |

---

## 🧪 **كيفية الاستخدام:**

### **إضافة مرحلة جديدة:**
1. اضغط "إضافة مرحلة جديدة"
2. املأ بيانات المرحلة
3. في قسم "إعدادات المحابس"، اختر العدد المطلوب
4. ستظهر رسالة تأكيد بعدد المحابس المُنشأة
5. احفظ المرحلة

### **تعديل مرحلة موجودة:**
1. اضغط "تعديل" على المرحلة المطلوبة
2. في قسم "إدارة المحابس"، ستجد العدد الحالي
3. غيّر العدد من القائمة المنسدلة
4. ستظهر المحابس الجديدة أو تُحذف الزائدة
5. احفظ التغييرات

---

## 🎉 **الفوائد:**

### **للمزارع:**
- ✅ **مرونة كاملة** في تحديد عدد المحابس
- ✅ **سهولة التعديل** بدون تعقيدات
- ✅ **واجهة واضحة** ومفهومة

### **للنظام:**
- ✅ **إدارة ذكية** للمحابس
- ✅ **ربط تلقائي** بالمراحل
- ✅ **حفظ آمن** في قاعدة البيانات

### **للصيانة:**
- ✅ **كود منظم** وقابل للتطوير
- ✅ **معالجة أخطاء** شاملة
- ✅ **تحديثات تلقائية** للبيانات

---

## 🚀 **جاهز للاستخدام!**

الميزة الآن متاحة ومُختبرة. يمكن للمستخدمين:
- إنشاء مراحل بعدد محابس مخصص
- تعديل عدد المحابس للمراحل الموجودة
- رؤية المحابس بصرياً مع حالتها
- إدارة المحابس بسهولة ومرونة

**🌟 استمتع بالتحكم الكامل في محابس الري!**