import React, { useState } from 'react';
import { Search, Filter, X, SortAsc, SortDesc, Calendar, RotateCcw } from 'lucide-react';

interface SearchAndFilterProps {
  searchTerm: string;
  onSearchChange: (term: string) => void;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
  onSortChange?: (sortBy: string, sortOrder: 'asc' | 'desc') => void;
  filters?: Record<string, any>;
  onFilterChange?: (filters: Record<string, any>) => void;
  filterOptions?: Array<{
    key: string;
    label: string;
    type: 'select' | 'multiselect' | 'date' | 'daterange';
    options?: Array<{value: any; label: string}>;
  }>;
  onResetFilters?: () => void;
  placeholder?: string;
  showSorting?: boolean;
  showFilters?: boolean;
  sortOptions?: Array<{value: string; label: string}>;
}

export const SearchAndFilter: React.FC<SearchAndFilterProps> = ({
  searchTerm,
  onSearchChange,
  sortBy,
  sortOrder = 'asc',
  onSortChange,
  filters = {},
  onFilterChange,
  filterOptions = [],
  onResetFilters,
  placeholder = 'البحث...',
  showSorting = true,
  showFilters = true,
  sortOptions = []
}) => {
  const [showFilterPanel, setShowFilterPanel] = useState(false);
  const [dateRange, setDateRange] = useState<{start?: string; end?: string}>({});

  const hasActiveFilters = Object.keys(filters).some(key => 
    filters[key] !== null && filters[key] !== undefined && filters[key] !== ''
  ) || dateRange.start || dateRange.end;

  const handleFilterChange = (key: string, value: any) => {
    const newFilters = { ...filters, [key]: value };
    if (value === null || value === undefined || value === '') {
      delete newFilters[key];
    }
    onFilterChange?.(newFilters);
  };

  const handleDateRangeChange = (type: 'start' | 'end', value: string) => {
    const newDateRange = { ...dateRange, [type]: value };
    setDateRange(newDateRange);
    
    // يمكن إضافة منطق للفلترة حسب التاريخ هنا
    if (onFilterChange) {
      const newFilters = { ...filters };
      if (newDateRange.start) newFilters.dateStart = newDateRange.start;
      if (newDateRange.end) newFilters.dateEnd = newDateRange.end;
      onFilterChange(newFilters);
    }
  };

  const handleResetAll = () => {
    onSearchChange('');
    setDateRange({});
    onResetFilters?.();
  };

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 mb-6">
      {/* شريط البحث الرئيسي */}
      <div className="flex flex-wrap gap-3 items-center mb-4">
        {/* البحث */}
        <div className="flex-1 min-w-[300px] relative">
          <div className="relative">
            <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
            <input
              type="text"
              value={searchTerm}
              onChange={(e) => onSearchChange(e.target.value)}
              placeholder={placeholder}
              className="w-full pr-10 pl-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent text-right"
            />
            {searchTerm && (
              <button
                onClick={() => onSearchChange('')}
                className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
              >
                <X className="w-4 h-4" />
              </button>
            )}
          </div>
        </div>

        {/* أزرار التحكم */}
        <div className="flex gap-2">
          {/* الترتيب */}
          {showSorting && sortOptions.length > 0 && (
            <div className="flex items-center gap-2">
              <select
                value={sortBy || ''}
                onChange={(e) => onSortChange?.(e.target.value, sortOrder)}
                className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 text-sm"
              >
                <option value="">ترتيب حسب</option>
                {sortOptions.map(option => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
              
              {sortBy && (
                <button
                  onClick={() => onSortChange?.(sortBy, sortOrder === 'asc' ? 'desc' : 'asc')}
                  className="p-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
                  title={sortOrder === 'asc' ? 'تصاعدي' : 'تنازلي'}
                >
                  {sortOrder === 'asc' ? (
                    <SortAsc className="w-4 h-4 text-gray-600" />
                  ) : (
                    <SortDesc className="w-4 h-4 text-gray-600" />
                  )}
                </button>
              )}
            </div>
          )}

          {/* الفلاتر */}
          {showFilters && filterOptions.length > 0 && (
            <button
              onClick={() => setShowFilterPanel(!showFilterPanel)}
              className={`flex items-center gap-2 px-4 py-2 rounded-lg transition-colors ${
                hasActiveFilters 
                ? 'bg-emerald-100 text-emerald-700 border border-emerald-300' 
                : 'bg-gray-100 text-gray-700 border border-gray-300 hover:bg-gray-150'
              }`}
            >
              <Filter className="w-4 h-4" />
              فلترة
              {hasActiveFilters && (
                <span className="bg-emerald-600 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
                  {Object.keys(filters).filter(k => filters[k]).length}
                </span>
              )}
            </button>
          )}

          {/* إعادة تعيين */}
          {(hasActiveFilters || searchTerm) && (
            <button
              onClick={handleResetAll}
              className="flex items-center gap-2 px-4 py-2 bg-red-100 text-red-700 border border-red-300 rounded-lg hover:bg-red-150 transition-colors"
            >
              <RotateCcw className="w-4 h-4" />
              إعادة تعيين
            </button>
          )}
        </div>
      </div>

      {/* لوحة الفلاتر المتقدمة */}
      {showFilterPanel && showFilters && (
        <div className="border-t pt-4 mt-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {/* فلتر النطاق الزمني */}
            <div className="md:col-span-2 lg:col-span-1">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                <Calendar className="w-4 h-4 inline ml-1" />
                النطاق الزمني
              </label>
              <div className="flex gap-2">
                <input
                  type="date"
                  value={dateRange.start || ''}
                  onChange={(e) => handleDateRangeChange('start', e.target.value)}
                  className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 text-sm"
                  placeholder="من"
                />
                <input
                  type="date"
                  value={dateRange.end || ''}
                  onChange={(e) => handleDateRangeChange('end', e.target.value)}
                  className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 text-sm"
                  placeholder="إلى"
                />
              </div>
            </div>

            {/* الفلاتر المخصصة */}
            {filterOptions.map(filter => (
              <div key={filter.key}>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  {filter.label}
                </label>
                
                {filter.type === 'select' && (
                  <select
                    value={filters[filter.key] || ''}
                    onChange={(e) => handleFilterChange(filter.key, e.target.value || null)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 text-sm"
                  >
                    <option value="">الكل</option>
                    {filter.options?.map(option => (
                      <option key={option.value} value={option.value}>
                        {option.label}
                      </option>
                    ))}
                  </select>
                )}

                {filter.type === 'multiselect' && (
                  <div className="space-y-2 max-h-32 overflow-y-auto border border-gray-300 rounded-lg p-2">
                    {filter.options?.map(option => (
                      <label key={option.value} className="flex items-center gap-2 text-sm">
                        <input
                          type="checkbox"
                          checked={(filters[filter.key] || []).includes(option.value)}
                          onChange={(e) => {
                            const currentValues = filters[filter.key] || [];
                            const newValues = e.target.checked
                              ? [...currentValues, option.value]
                              : currentValues.filter((v: any) => v !== option.value);
                            handleFilterChange(filter.key, newValues.length > 0 ? newValues : null);
                          }}
                          className="rounded border-gray-300 text-emerald-600 focus:ring-emerald-500"
                        />
                        {option.label}
                      </label>
                    ))}
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};