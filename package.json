{"name": "lettuce-farm-management-system", "productName": "نظام إدارة مزرعة الخس", "description": "نظام متكامل لإدارة مزرعة الخس مع 24 مرحلة زراعية", "author": "Lettuce Farm Management", "private": true, "version": "2.0.0", "type": "module", "main": "main.js", "homepage": "./", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview", "electron": "electron .", "electron-dev": "concurrently \"npm run dev\" \"wait-on http://localhost:5173 && electron .\"", "build-electron": "npm run build && electron .", "dist": "npm run build && electron-builder", "dist-win": "npm run build && electron-builder --win", "dist-mac": "npm run build && electron-builder --mac", "dist-linux": "npm run build && electron-builder --linux", "check": "node system-check.js", "test-improvements": "start test-improvements.html", "quick-start": "quick-start.bat", "clean": "clean-unnecessary-files.bat"}, "dependencies": {"@fontsource/cairo": "^5.2.6", "@fontsource/tajawal": "^5.2.6", "arabic-reshaper": "^1.1.0", "bidi-js": "^1.0.3", "chart.js": "^4.5.0", "file-saver": "^2.0.5", "html2canvas": "^1.4.1", "html2pdf.js": "^0.10.3", "jspdf": "^3.0.1", "jspdf-autotable": "^5.0.2", "lucide-react": "^0.344.0", "moment-hijri": "^3.0.0", "react": "^18.3.1", "react-chartjs-2": "^5.3.0", "react-dom": "^18.3.1", "xlsx": "^0.18.5"}, "devDependencies": {"@eslint/js": "^9.9.1", "@types/file-saver": "^2.0.7", "@types/moment-hijri": "^2.1.4", "@types/react": "^18.3.5", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.18", "concurrently": "^9.2.0", "electron": "^28.3.3", "electron-builder": "^26.0.12", "electron-is-dev": "^3.0.1", "eslint": "^9.9.1", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.11", "globals": "^15.9.0", "postcss": "^8.4.35", "tailwindcss": "^3.4.1", "typescript": "^5.5.3", "typescript-eslint": "^8.3.0", "vite": "^5.4.2", "wait-on": "^8.0.3"}, "build": {"appId": "com.lettucefarm.management", "productName": "نظام إدارة مزرعة الخس", "directories": {"output": "dist-electron"}, "files": ["dist/**/*", "main.js", "public/favicon.ico", "node_modules/**/*"], "win": {"target": "portable", "icon": "public/favicon.ico"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true, "createStartMenuShortcut": true, "shortcutName": "نظام إدارة مزرعة الخس"}, "mac": {"target": "dmg", "icon": "public/favicon.ico"}, "linux": {"target": "AppImage", "icon": "public/favicon.ico"}}}