{"name": "lettuce-farm-design-system", "version": "1.0.0", "description": "نظام تصميم شامل مستخرج من مشروع نظام إدارة مزرعة الخس - Design System for Lettuce Farm Management System", "main": "index.js", "type": "module", "keywords": ["design-system", "css", "tailwind", "react", "arabic", "ui", "ux", "components", "theme", "lettuce-farm", "management-system"], "author": "Lettuce Farm Management", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/lettuce-farm/design-system.git"}, "bugs": {"url": "https://github.com/lettuce-farm/design-system/issues"}, "homepage": "https://github.com/lettuce-farm/design-system#readme", "files": ["theme.css", "tailwind.config.js", "design-system.js", "index.js", "README.md"], "exports": {".": "./index.js", "./css": "./theme.css", "./tailwind": "./tailwind.config.js", "./system": "./design-system.js"}, "peerDependencies": {"react": ">=18.0.0", "tailwindcss": ">=3.0.0"}, "devDependencies": {"tailwindcss": "^3.4.1"}, "engines": {"node": ">=16.0.0"}, "scripts": {"build": "echo 'Design system is ready to use'", "test": "echo 'No tests specified'", "lint": "echo 'No linting specified'"}, "metadata": {"source": "Original project styles extracted without modification", "compatibility": ["React", "Tailwind CSS", "Vanilla CSS", "JavaScript"], "features": ["Arabic Fonts Support", "RTL Layout Support", "Responsive Design", "Component Styles", "Color Palette", "Typography System", "Spacing System", "Shadow System", "Animation System", "Icon System"], "components": ["Buttons", "Cards", "Inputs", "Modals", "Tables", "Navigation", "Status Indicators", "Gradients"], "colors": {"primary": "Green (#059669)", "secondary": "Blue (#2563eb)", "accent": "Orange (#f59e0b)", "success": "<PERSON> (#16a34a)", "warning": "Orange (#eab308)", "error": "Red (#dc2626)"}, "fonts": {"primary": "Cairo", "secondary": "<PERSON><PERSON><PERSON>", "source": "Google Fonts"}, "icons": {"library": "Lucide React", "url": "https://lucide.dev/"}}}