// ملف تكامل المزرعة مع نظام المخزون
import { addInventoryItem, updateInventory, addMovement } from './database';

// أنواع البيانات للتكامل
interface HarvestData {
  tankId: number;
  stageName: string;
  lettuceType: 'iceberg' | 'romaine';
  harvestDate: string;
  actualYield: number;
  quality: 'excellent' | 'good' | 'average' | 'poor';
  notes: string;
}

interface InventoryIntegration {
  farmToWarehouse: (harvestData: HarvestData) => Promise<{ success: boolean; message: string; inventoryId?: number }>;
  getExpectedHarvests: (days: number) => Promise<HarvestData[]>;
  getFarmProductionReport: (startDate: string, endDate: string) => Promise<any>;
  syncFarmWithInventory: () => Promise<{ success: boolean; message: string }>;
}

// دالة تحويل الحصاد إلى المخزون
export const farmToWarehouse = async (harvestData: HarvestData): Promise<{ success: boolean; message: string; inventoryId?: number }> => {
  try {
    // تحديد نوع المنتج حسب نوع الخس
    const productName = harvestData.lettuceType === 'iceberg' 
      ? 'خس أيسبيرغ نخب أول' 
      : 'خس رومين نخب أول';
    
    // حساب عدد الصناديق (كل صندوق 10 كجم)
    const totalBoxes = Math.floor(harvestData.actualYield / 10);
    const pallets = Math.floor(totalBoxes / 70);
    const boxes = totalBoxes % 70;
    
    // إضافة/تحديث المخزون
    const inventoryResult = await addInventoryItem({
      name: productName,
      pallets: pallets,
      boxes: boxes,
      totalBoxes: totalBoxes,
      location: 'المزرعة',
      date: harvestData.harvestDate,
      notes: `حصاد من ${harvestData.stageName} - الجودة: ${harvestData.quality}${harvestData.notes ? ` - ${harvestData.notes}` : ''}`,
      category: 'ورقيات',
      description: `حصاد من المزرعة - ${harvestData.stageName}`
    });

    if (inventoryResult.success) {
      // إضافة حركة إدخال للمخزون
      await addMovement({
        type: 'in',
        itemName: productName,
        quantity: totalBoxes,
        date: harvestData.harvestDate,
        time: new Date().toLocaleTimeString('ar-EG'),
        notes: `حصاد من المزرعة - ${harvestData.stageName} - الكمية: ${harvestData.actualYield} كجم`
      });

      return {
        success: true,
        message: `تم نقل الحصاد بنجاح إلى المخزون\nالكمية: ${harvestData.actualYield} كجم (${totalBoxes} صندوق)\nالتقسيم: ${pallets} طبلية و ${boxes} صندوق`,
        inventoryId: inventoryResult.id
      };
    } else {
      return {
        success: false,
        message: `فشل في إضافة الحصاد للمخزون: ${inventoryResult.error}`
      };
    }
  } catch (error) {
    console.error('خطأ في تحويل الحصاد للمخزون:', error);
    return {
      success: false,
      message: `خطأ في تحويل الحصاد للمخزون: ${error instanceof Error ? error.message : 'خطأ غير معروف'}`
    };
  }
};

// دالة الحصول على الحصاد المتوقع
export const getExpectedHarvests = async (days: number = 7): Promise<HarvestData[]> => {
  try {
    // تحميل بيانات المزرعة من localStorage
    const farmStagesData = localStorage.getItem('farmStages');
    if (!farmStagesData) {
      return [];
    }

    const farmStages = JSON.parse(farmStagesData);
    const today = new Date();
    const futureDate = new Date(today.getTime() + days * 24 * 60 * 60 * 1000);
    
    const expectedHarvests: HarvestData[] = [];

    farmStages.forEach((stage: any) => {
      stage.tanks.forEach((tank: any) => {
        if (tank.currentPhase === 'harvest' || tank.currentPhase === 'maturation') {
          const harvestDate = new Date(tank.expectedHarvestDate);
          
          if (harvestDate >= today && harvestDate <= futureDate) {
            expectedHarvests.push({
              tankId: tank.id,
              stageName: stage.name,
              lettuceType: tank.lettuceType,
              harvestDate: tank.expectedHarvestDate,
              actualYield: tank.estimatedYield,
              quality: tank.growthStatus || 'good',
              notes: tank.notes || ''
            });
          }
        }
      });
    });

    return expectedHarvests.sort((a, b) => new Date(a.harvestDate).getTime() - new Date(b.harvestDate).getTime());
  } catch (error) {
    console.error('خطأ في الحصول على الحصاد المتوقع:', error);
    return [];
  }
};

// دالة تقرير إنتاج المزرعة
export const getFarmProductionReport = async (startDate: string, endDate: string): Promise<any> => {
  try {
    const farmStagesData = localStorage.getItem('farmStages');
    if (!farmStagesData) {
      return {
        totalHarvests: 0,
        totalYield: 0,
        icebergYield: 0,
        romaineYield: 0,
        qualityBreakdown: { excellent: 0, good: 0, average: 0, poor: 0 },
        stageBreakdown: {}
      };
    }

    const farmStages = JSON.parse(farmStagesData);
    const start = new Date(startDate);
    const end = new Date(endDate);
    
    let totalHarvests = 0;
    let totalYield = 0;
    let icebergYield = 0;
    let romaineYield = 0;
    const qualityBreakdown = { excellent: 0, good: 0, average: 0, poor: 0 };
    const stageBreakdown: { [key: string]: number } = {};

    farmStages.forEach((stage: any) => {
      stage.tanks.forEach((tank: any) => {
        if (tank.harvestDate && tank.actualYield) {
          const harvestDate = new Date(tank.harvestDate);
          
          if (harvestDate >= start && harvestDate <= end) {
            totalHarvests++;
            totalYield += tank.actualYield;
            
            if (tank.lettuceType === 'iceberg') {
              icebergYield += tank.actualYield;
            } else {
              romaineYield += tank.actualYield;
            }
            
            qualityBreakdown[tank.growthStatus || 'good']++;
            
            if (!stageBreakdown[stage.name]) {
              stageBreakdown[stage.name] = 0;
            }
            stageBreakdown[stage.name] += tank.actualYield;
          }
        }
      });
    });

    return {
      totalHarvests,
      totalYield,
      icebergYield,
      romaineYield,
      qualityBreakdown,
      stageBreakdown,
      averageYieldPerHarvest: totalHarvests > 0 ? totalYield / totalHarvests : 0
    };
  } catch (error) {
    console.error('خطأ في تقرير إنتاج المزرعة:', error);
    return {
      totalHarvests: 0,
      totalYield: 0,
      icebergYield: 0,
      romaineYield: 0,
      qualityBreakdown: { excellent: 0, good: 0, average: 0, poor: 0 },
      stageBreakdown: {}
    };
  }
};

// دالة مزامنة المزرعة مع المخزون
export const syncFarmWithInventory = async (): Promise<{ success: boolean; message: string }> => {
  try {
    // الحصول على جميع الحصادات الجاهزة
    const expectedHarvests = await getExpectedHarvests(0); // الحصادات لليوم الحالي
    
    let syncedCount = 0;
    let errors: string[] = [];

    for (const harvest of expectedHarvests) {
      const result = await farmToWarehouse(harvest);
      if (result.success) {
        syncedCount++;
      } else {
        errors.push(`${harvest.stageName}: ${result.message}`);
      }
    }

    if (errors.length === 0) {
      return {
        success: true,
        message: `تم مزامنة ${syncedCount} حصاد مع المخزون بنجاح`
      };
    } else {
      return {
        success: false,
        message: `تم مزامنة ${syncedCount} حصاد، فشل في ${errors.length} حصاد:\n${errors.join('\n')}`
      };
    }
  } catch (error) {
    console.error('خطأ في مزامنة المزرعة مع المخزون:', error);
    return {
      success: false,
      message: `خطأ في المزامنة: ${error instanceof Error ? error.message : 'خطأ غير معروف'}`
    };
  }
};

// دالة حساب التكلفة لكل محبس
export const calculateTankCost = (tank: any): number => {
  let totalCost = 0;
  
  // تكلفة البذور (متوسط 50 دينار لكل جرام)
  totalCost += tank.seedQuantity * 50;
  
  // تكلفة الماء (متوسط 0.5 دينار لكل لتر)
  totalCost += tank.waterUsage * 0.5;
  
  // تكلفة الأسمدة (متوسط 20 دينار لكل كيلو)
  totalCost += tank.fertilizerUsage * 20;
  
  // تكلفة العمالة (متوسط 100 دينار لكل محبس في الدورة)
  totalCost += 100;
  
  return totalCost;
};

// دالة حساب الربحية
export const calculateProfitability = (tank: any): { cost: number; revenue: number; profit: number; profitMargin: number } => {
  const cost = calculateTankCost(tank);
  
  // سعر البيع (متوسط 3 دينار لكل كيلو)
  const pricePerKg = 3;
  const revenue = tank.actualYield ? tank.actualYield * pricePerKg : tank.estimatedYield * pricePerKg;
  
  const profit = revenue - cost;
  const profitMargin = revenue > 0 ? (profit / revenue) * 100 : 0;
  
  return {
    cost,
    revenue,
    profit,
    profitMargin
  };
};

// تصدير جميع الدوال
export const farmIntegration: InventoryIntegration = {
  farmToWarehouse,
  getExpectedHarvests,
  getFarmProductionReport,
  syncFarmWithInventory
};

export default farmIntegration;