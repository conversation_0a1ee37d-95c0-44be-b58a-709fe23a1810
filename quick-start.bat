@echo off
chcp 65001 > nul
title نظام إدارة مزرعة الخس - التشغيل السريع

echo.
echo ===============================================
echo    🌱 نظام إدارة مزرعة الخس المحسن
echo    شركة الشفق للزراعة الحديثة
echo ===============================================
echo.

echo 🔄 جاري فحص متطلبات النظام...
echo.

:: فحص Node.js
node --version > nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js غير مثبت
    echo يرجى تثبيت Node.js من https://nodejs.org
    pause
    exit /b 1
) else (
    echo ✅ Node.js مثبت
)

:: فحص npm
npm --version > nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ npm غير متاح
    pause
    exit /b 1
) else (
    echo ✅ npm متاح
)

echo.
echo 📦 جاري تثبيت التبعيات...
echo.

npm install

if %errorlevel% neq 0 (
    echo ❌ فشل في تثبيت التبعيات
    pause
    exit /b 1
)

echo.
echo ✅ تم تثبيت التبعيات بنجاح
echo.

echo 🚀 جاري تشغيل النظام...
echo.
echo سيتم فتح المتصفح تلقائياً على العنوان:
echo http://localhost:5173
echo.
echo للإيقاف: اضغط Ctrl+C
echo.

:: تشغيل الخادم
npm run dev

pause